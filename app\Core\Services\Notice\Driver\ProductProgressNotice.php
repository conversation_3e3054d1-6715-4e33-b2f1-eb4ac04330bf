<?php 
namespace App\Core\Services\Notice\Driver;

use App\Core\Services\Notice\NoticeService;
use App\Core\Services\UserService;
use App\Model\Redmine\CategoryModel;
use App\Model\Redmine\ProjectsProgressModel;
use Hyperf\Di\Annotation\Inject;
use App\Core\Services\Notice\Driver\NoticeHandlerInterface;

class ProductProgressNotice implements NoticeHandlerInterface
{
    /**
     * @Inject()
     * @var ProjectsProgressModel
     */
    protected $projectsProgressModel;

    /**
     * @Inject()
     * @var CategoryModel
     */
    protected $categoryModel;

    /**
     * @Inject()
     * @var UserService
     */
    protected $userService;

    /**
     * @Inject()
     * @var NoticeService
     */
    protected $noticeService;

    public function handle(...$arguments)
    {
        [$progressId, $progress] = $arguments;

        // 如果没有传入progress数据,则直接查询一次
        if (!$progress) {
            $progress = $this->projectsProgressModel::query()
                ->with(['project', 'projectExt'])
                ->find($progressId);
        }

        if (!$progress || empty($progress->description)) {
            return false;
        }
        $progress = $progress->toArray();

        //获取项目创建人
        $userInfo = $this->userService->getUserInfo($progress['create_user_id']);

        $redmineUserIds = array_merge($progress['mail_user'], $progress['workwx_user']);
        if (!$redmineUserIds) {
            return false;
        }

        $progressType = $this->categoryModel::query()
            ->where('keywords', $progress['type'])
            ->where('type', 'project_progress_type')
            ->first();

        $host = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
        $activeName = 'workProgress';
        if ($progress['type'] == 10) {
            $activeName = 'noticePCN';
        }
        $host .= "/#/project/productDetailsIndex?product_id={$progress['project_id']}&active_name={$activeName}&skip={$progress['id']}";

        $users = $this->userService->biUserIdByThirdUserId($redmineUserIds);
        $users = array_unique($users);

        foreach ($users as $user) {
            $params = [
                'project_name' => $progress['project']['name'],
                'project_type_text' => '产品',
                'progress_type_text' => $progressType['name'] ?? '',
                'notes' => $progress['description_html'] ?? ($progress['description'] ?? ''),
                'host' => $host,
                'author' => $userInfo['username'] ?? '未知',
                'created_on' => $progress['created_at'],
            ];
            $this->noticeService->handleNotice('productProgress', $user, $params);
        }

        return true;
    }
}