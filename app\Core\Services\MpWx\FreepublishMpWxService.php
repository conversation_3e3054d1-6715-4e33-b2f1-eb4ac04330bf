<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/17 上午10:39
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\MpWx;

/**
 * 发布能力
 */
class FreepublishMpWxService extends MpWxBaseService
{
    /**
     * 获取成功发布列表
     * @return mixed
     */
    public function batchget($mp = 'stationpc')
    {
        $response = $this->sendRequest('cgi-bin/freepublish/batchget', [
            'query' => [
                'access_token' => getCache($this->mpList($mp)['redis_key']),
            ],
            'json'  => [
                'offset'     => 0,
                'count'      => 1,
                'no_content' => 1
            ]
        ], 'post');
        return $response;
    }

    /**
     * 通过 article_id 获取已发布文章
     * @param $mp
     * @return mixed
     */
    public function getarticle($mp = 'stationpc')
    {
        $response = $this->sendRequest('cgi-bin/freepublish/getarticle', [
            'query' => [
                'access_token' => getCache($this->mpList($mp)['redis_key']),
            ],
            'json'  => [
                'article_id' => 1,
            ]
        ], 'post');
        return $response;
    }
}