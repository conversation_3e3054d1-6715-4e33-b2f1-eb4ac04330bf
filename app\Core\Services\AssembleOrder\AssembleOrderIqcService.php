<?php

/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Core\Services\AssembleOrder;

use App\Constants\OaQcErpCode;
use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Core\Services\TchipSale\SaleBaseService;
use App\Exception\AppException;
use App\Model\TchipBi\AssembleOrderIqcModel;
use App\Model\TchipBi\AssembleOrderModel;
use App\Model\TchipBi\CategoryModel;
use App\Model\TchipBi\OaQcModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class AssembleOrderIqcService extends BusinessService
{
    /**
     * @Inject
     * @var assembleOrderIqcModel
     */
    protected $model;

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        [
            $query,
            $limit,
            $sort,
            $order
        ] = $this->buildparams($filter, $op, $sort, $order, $limit);
        return $query->leftJoin('oa_qc as qc', 'qc.id', 'assemble_order_iqc.oa_qc_id')
            ->select('assemble_order_iqc.*')
            ->addSelect('qc.prod_name', 'qc.id as oa_qc_id', 'qc.prod_code', 'qc.prod_spec', 'qc.order_no', 'qc.result_remark')
            ->orderBy($sort, $order)->paginate($limit);
    }

    /**
     * 解除生产单和qc的关联
     * @param mixed $orderId
     * @param mixed $qcId
     */
    public function deleteRelation($orderId = [], $qcId = [])
    {
        if (!$orderId && !$qcId) return;
        !is_array($orderId) && $orderId = explode(',', $orderId);
        !is_array($qcId) && $qcId = explode(',', $qcId);
        $model = $this->model::query();
        if ($orderId) {
            $model->whereIn('assemble_order_id', $orderId);
        }
        if ($qcId) {
            $model->whereIn('oa_qc_id', $qcId);
        }
        $model->delete();
    }

    /**
     * 自动关联QC
     * 创建订单时，获取该产品的BOM，然后去匹配QC模块中的检验记录
     * 当条件为最后一次不合格时且不是退货的话，主动关联该记录.
     * @param mixed $orderId
     */
    public function autoRelateQc($orderId)
    {
        $order = assembleOrderModel::query()->find($orderId);
        if (!$order) {
            return;
        }
        //根据单号关联
        $orderRelatedQcId = $this->relateByOrderCode($order->code);
        $productCode = $order['product_code'];
        $relateQcId = $this->getRelateQc($productCode);
        $relateQcId = array_unique(array_filter(array_merge($orderRelatedQcId,$relateQcId)));
        if ($relateQcId) {
            $relateData = [
                'assemble_order_id' => $orderId,
                'oa_qc_id'          => $relateQcId,
            ];
            $this->doEdit(0, $relateData);
        }
    }

    /**
     * 获取符合条件的qc
     * 创建订单时，获取该产品的BOM，然后去匹配QC模块中的检验记录
     * 当条件为最后一次不合格时且不是退货的话，主动关联该记录.
     * @param mixed $productCode
     * @return array
     */
    public function getRelateQc($productCode)
    {
        $json = ['goods' => $productCode];
        $erpData = make(SaleBaseService::class)
            ->sendRequest('firefly_erpapi/public/index.php/sc/bomd/getGoodsBomList', ['json' => $json], 'POST');
        $relateQcId = [];
        if (!empty($erpData['data'])) {
            // 每个料号取最后一次符合条件的记录
            $qcList = OaQcModel::query()->whereIn('prod_code', $erpData['data'])
                ->where('status', 2)
                // 两种情况
                // 1.未选择不及格处理方案，批量处理方案不为空和不为退货
                // 2.选择了不及格处理方案且不为退货
                ->whereRaw('((unqualified_handle = 0 and handle not in (0,5)) or (unqualified_handle not in (0,5)))')
                ->select([
                    'prod_code',
                    Db::raw('max(id) as id')
                ])
                ->groupBy(['prod_code'])
                ->get()->toArray();
            $relateQcId = array_column($qcList, 'id');
        }
        return $relateQcId;
    }

    public function doEdit(int $id, array $values)
    {
        if ($id > 0) {
            $row = $this->model::query()->find($id);
            if (!$row) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            }
            $result = $row->update($values);
        } else {
            // 关联iqc
            if (empty($values['oa_qc_id']) || empty($values['assemble_order_id'])) {
                throw new AppException(StatusCode::VALIDATION_ERROR, __('common.Missing_parameter'));
            }
            $insertData = [];
            if ($id == -1) {// qc绑定组装订单
                $assembleOrderId = is_array($values['assemble_order_id']) ? $values['assemble_order_id'] : explode(',', $values['assemble_order_id']);
                $relatedOrderId = $this->model::query()->where('oa_qc_id', $values['oa_qc_id'])->pluck('assemble_order_id')->toArray();
                $insertOrderId = array_diff($assembleOrderId, $relatedOrderId);
                $deleteOrderId = array_diff($relatedOrderId, $assembleOrderId);
                $deleteOrderId && $this->model::query()->whereIn('assemble_order_id', $deleteOrderId)->where('oa_qc_id', $values['oa_qc_id'])->delete();
                foreach ($insertOrderId as $orderId) {
                    $insertData[] = [
                        'assemble_order_id' => $orderId,
                        'oa_qc_id'          => $values['oa_qc_id'],
                        'created_at'        => date('Y-m-d H:i:s'),
                    ];
                }
            } else {// 组装订单绑定qc
                // 过滤已关联的qc数据
                $oaQcId = is_array($values['oa_qc_id']) ? $values['oa_qc_id'] : explode(',', $values['oa_qc_id']);
                $relatedQcId = $this->model::query()->whereIn('oa_qc_id', $oaQcId)->where('assemble_order_id', $values['assemble_order_id'])->pluck('oa_qc_id')->toArray();
                $insertQcId = array_diff($oaQcId, $relatedQcId);
                // 批量插入
                foreach ($insertQcId as $qcId) {
                    $insertData[] = [
                        'assemble_order_id' => $values['assemble_order_id'],
                        'oa_qc_id'          => $qcId,
                        'remark'            => $values['remark'] ?? null,
                        'created_at'        => date('Y-m-d H:i:s'),
                    ];
                }
            }
            $result = $insertData && $this->model::query()->insert($insertData);
        }

        return $result;
    }

    public function relateByOrderCode($orderCode)
    {
        $qcType = CategoryModel::query()->where('keywords', OaQcErpCode::QC_ASSEMBLE_ENTER)->value('id');
        return OaQcModel::query()->where('order_no', $orderCode)->where('type', $qcType)->pluck('id')->toArray();
    }
}
