<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/20 下午5:27
 * <AUTHOR> X
 * @Description
 */

namespace App\Core\Services\Index\Quota;

use App\Core\Services\TchipOa\OaFilesService;
use Hyperf\Di\Annotation\Inject;

class Oa
{
    /**
     * @Inject()
     * @var OaFilesService
     */
    protected $service;

    public function birthdayCount()
    {
        return $this->service->birthdayCount();
    }

    public function entryCount()
    {
        return $this->service->entryCount();
    }

    public function incumbencyCount()
    {
        return $this->service->incumbencyCount();
    }
}