<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/9/8 上午11:39
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing;

use App\Model\Model;
use App\Model\StationPCManager\AppStatResultModel;
use Hyperf\Cache\Annotation\Cacheable;
use Hyperf\DbConnection\Db;

class AppReportService extends \App\Core\Services\BusinessService
{
    /**
     * 获取面板数据
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return array
     */
    public function getPanelData($filter, $op, $sort, $order, $limit): array
    {
        $initData = $this->getInitData($filter, $op, $sort, $order, $limit)->get();
        $result = [
            'new_device_count_sum'        => $initData->sum('new_device_count_sum'),
            'active_device_count_sum'     => $initData->sum('active_device_count_sum'),
            'avg_device_session_time_avg' => round($initData->avg('avg_device_session_time_avg')),
            'avg_device_time_avg'         => round($initData->avg('avg_device_time_avg')),
            'total_devices_sum'           => $initData->last()['total_devices_sum']
        ];
        return $result;
    }

    /**
     * 趋势图数据
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return array
     */
    public function getTrendData($filter, $op, $sort, $order, $limit)
    {
        return $this->getInitData($filter, $op, $sort, $order, $limit)->get();

    }

    /**
     * 图表数据
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return array
     */
    public function getTableData($filter, $op, $sort, $order, $limit)
    {
        $initData = $this->getInitData($filter, $op, $sort, $order, $limit);
        return $initData->orderBy('stat_date', 'desc')->paginate($limit);
    }

    /**
     * 基础数据
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return \Hyperf\Utils\Collection
     */
    private function getInitData($filter, $op, $sort, $order, $limit)
    {
        /* @var AppStatResultModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, AppStatResultModel::query());
        $items = $query->groupBy('stat_date')->select(array(
            'stat_date',
            'dimension',
            'start_time',
            'end_time',
            Db::raw('SUM(new_device_count) AS new_device_count_sum'),
            Db::raw('SUM(active_device_count) AS active_device_count_sum'),
            Db::raw('AVG(avg_device_session_time) AS avg_device_session_time_avg'),
            Db::raw('AVG(avg_device_time) AS avg_device_time_avg'),
            Db::raw('SUM(total_devices) AS total_devices_sum'),
        ));
        return $items;
    }
}