<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 下午2:50
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Project;

use App\Constants\ProjectCode;
use App\Constants\RedmineCode;
use App\Core\Services\Redmine\SettingsService;
use App\Model\Redmine\TrackersModel;
use Hyperf\Di\Annotation\Inject;

/**
 * 项目管理相关接口服务
 */
class TrackersService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var TrackersModel
     */
    protected $model;

    /**
     * 获取项目trackers
     * @param $projectType
     * @return \Hyperf\Utils\HigherOrderTapProxy|int|mixed|\Tightenco\Collect\Support\HigherOrderTapProxy|void|null
     */
    public function getProjectDefaultTrackers($projectType = null)
    {
        switch ($projectType) {
            case ProjectCode::PROJECT_TYPE_TASK:
            case ProjectCode::PROJECT_TYPE_COM:
                $trackers = TrackersModel::query(true)->where('name', '任务')->pluck('id')->toArray();
                break;
            case ProjectCode::PROJECT_TYPE_PROD:
                // 研发流程
                $trackers = TrackersModel::query(true)->where('id', 21)->pluck('id')->toArray();
                break;
            case ProjectCode::PROJECT_TYPE_SOFT:
            case ProjectCode::PROJECT_TYPE_HARD:
            $trackers = TrackersModel::query(true)->whereIn('name', ['缺陷', '任务', '需求'])->pluck('id')->toArray();
                break;
            default:
                $trackers = make(SettingsService::class)->getSettingValue(RedmineCode::SETTING_DEFAULT_TRACKER, 'yaml');
        }
        return $trackers;
    }
}