<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:59
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing;

use App\Constants\AutoUserCountCode;
use App\Constants\StatusCode;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\StationPCManager\MarketingChannelModel;
use Hyperf\Context\Context;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Retry\Annotation\Retry;

/**
 * 营销模块-频道数据
 */
class ChannelService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var MarketingChannelModel
     */
    protected $model;

    /**
     * 获取列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return mixed
     */
    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        return $query->orderBy($sort, $order)->with(['platform'])->paginate($limit);
    }

    /**
     * 采集用户数
     * @return bool
     */
    public function collectUserCount(): bool
    {
        $items = $this->model::query()->where('is_auto_user_count', 1)->with(['platform'])->get();
        Log::get('system', 'system')->info('==============================================================================');
        Log::get('system', 'system')->info('开始采集渠道用户数');
        $collectUserCountStartTime = microtime(true);
        foreach ($items as $key => $item) {
            Log::get('system', 'system')->info('进度:' . ($key + 1) . '/' . count($items) . ' '.$item['platform']['name'].'-' . $item['name']);
            $this->collectUserCountFunc($item['id']);
        }
        $executionTime = sprintf('%.2f', ((microtime(true) - $collectUserCountStartTime) * 1000));
        Log::get('system', 'system')->info('采集结束，耗时'.$executionTime.'毫秒');
        Log::get('system', 'system')->info('==============================================================================');

        return true;
    }

    /**
     * 采集用户数，具体执行动作
     * @Retry()
     * @param $channelId
     * @return mixed
     */
    public function collectUserCountFunc($channelId)
    {
        try {
            $channel = $this->model::query()->with(['platform'])->find($channelId);
            if (!isset(AutoUserCountCode::AUTO_USER_COUNT[$channel['auto_user_count_func']])) {
                throw new AppException(StatusCode::ERR_SERVER,
                    '平台:'.$channel['platform']['name'].' 频道:' . $channel['name'] . ' 无效执行动作 -- ' . $channel['auto_user_count_func']);
            }
            $autoObj = AutoUserCountCode::AUTO_USER_COUNT[$channel['auto_user_count_func']];
            $userCount = make($autoObj['class'])->getUserCount($channelId, $channel['space_url']);
            if($userCount > 0){
                Log::get('system', 'system')->info('采集结果：'.$channel['platform']['name'].'-' . $channel['name'] . '，获取用户数' . $userCount);
                $channel->user_count = $userCount;
                $result = $channel->save();
            }else{
                $failLog = '采集结果:'.$channel['platform']['name'].'-' . $channel['name'] . '，获取用户数' . $userCount . '，无效数据，请检查对应执行动作';
                Log::get('system', 'system')->info($failLog);
                throw new AppException(StatusCode::ERR_SERVER, $failLog);
            }
            return $userCount;
        } catch (\Throwable $e) {
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }
}