<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:59
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing;

use App\Constants\MarketingCode;
use App\Constants\StatusCode;
use App\Core\Services\TchipBi\ProductService;
use App\Core\Utils\TimeUtils;
use App\Exception\AppException;
use App\Model\Marketing\MarketingPlatform;
use App\Model\TchipBi\CategoryModel;
use App\Model\TchipBi\MarketingPlatformModel;
use App\Model\TchipSale\ClientSaleKyptTableModel;
use App\Model\TchipSale\LinkageModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use App\Constants\CommonCode;
use App\Core\Services\TchipSale\SaleService;
/**
 * 品牌运营-推广平台
 */
class MarketingPlatformService extends \App\Core\Services\BusinessService
{

    /**
     * @Inject()
     * @var MarketingPlatformModel
     */
    protected $model;

    public function getAllList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        if (!isset($filter['status'])) {
            $filter['status'] = 1;
        }
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 9999);
        $list = $query->selectRaw('id, name, IFNULL(sales_name, name) as sales_name, type, status')->orderBy($sort, $order)->get();

        if (!$list) return [];

        $list = $list->toArray();
        $platformNames = array_column($list, 'sales_name');
        $salesLinkage = LinkageModel::query()->whereIn('text', $platformNames)->get();
        $salesLinkage = $salesLinkage ? $salesLinkage->toArray() : [];
        $salesLinkage = array_column($salesLinkage, null, 'text');
        foreach ($list as &$item) {
            $item['sale_platform_id'] = $salesLinkage[$item['sales_name']]['id'] ?? 0;
        }
        return $list;
    }

    /**
     * 保存平台
     * @param array $items
     * @return bool
     */
    public function savePlatform(array $items)
    {
        $ids = [];
        Db::beginTransaction();
        try {
            foreach ($items as $item) {
                if (empty($item['name'])) continue;
                // 如果 sales_name 为空，则设置为 name
                if (empty($item['sales_name'])) {
                    $item['sales_name'] = $item['name'];
                }
                // 获取 item 的 ID，默认为 0
                $itemId = $item['id'] ?? 0;

                // 保存或更新 item，并返回保存后的对象
                $savedItem = $this->doEdit($itemId, $item);

                // 检查保存的结果，更新 ids 数组
                if ($savedItem && isset($savedItem->id)) {
                    $ids[] = $savedItem->id;
                } elseif (!empty($item['id'])) {
                    $ids[] = $item['id'];
                }
            }

            if (count($ids) > 0) {
                $this->model::query()->whereNotIn('id', $ids)->delete();
            }
            Db::commit();
        }  catch (\Throwable $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
        return true;
    }
}