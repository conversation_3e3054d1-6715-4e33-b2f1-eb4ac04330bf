<?php

namespace App\Core\Services\AssembleOrder;

use App\Constants\AssembleOrderCode;
use App\Constants\ProductionCode;
use App\Constants\StatusCode;
use App\Constants\WorkFlowSceneCode;
use App\Core\Services\BusinessService;
use App\Core\Services\ExcelAnalyze\Writer\ExcelWriter;
use App\Core\Services\ExceptionRecord\ExceptionRecordService;
use App\Core\Services\MacAddress\MacAddressService;
use App\Core\Services\Notice\NoticeService;
use App\Core\Services\SnCode\SnCodeService;
use App\Core\Services\UserService;
use App\Core\Services\WorkFlow\WorkFlowService;
use App\Core\Services\WorkFlow\WorkStatusService;
use App\Core\Services\TchipSale\OrderService;
use App\Core\Utils\Log;
use App\Core\Utils\TimeUtils;
use App\Exception\AppException;
use App\Model\TchipBi\AssembleOrderAttachmentModel;
use App\Model\TchipBi\AssembleOrderInfoModel;
use App\Model\TchipBi\AssembleOrderIqcModel;
use App\Model\TchipBi\AssembleOrderMaterialCompletenessModel;
use App\Model\TchipBi\AssembleOrderModel;
use App\Model\TchipBi\AssembleOrderSummaryModel;
use App\Model\TchipBi\AttachmentModel;
use App\Model\TchipBi\ExceptionRecordRelationModel;
use App\Model\TchipBi\MacAddressModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\WorkFlowModel;
use App\Model\TchipBi\WorkStatusModel;
use App\Model\TchipSale\StockOrderlistModel;
use App\Model\TchipSale\UserTableModel;
use Exception;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Utils\Parallel;
use Psr\SimpleCache\CacheInterface;
use Throwable;


class AssembleOrderService extends BusinessService
{
    const SCENE_TYPE = WorkFlowSceneCode::SCENE_ASSEMBLE_ORDER;
    /**
     * @Inject()
     * @var AssembleOrderModel
     */
    protected $model;
    /**
     * @Inject
     * @var CacheInterface
     */
    private $cache;

    /**
     * 同步创建当天的生产订单
     */
    public function syncAssembleOrder($json = null)
    {
        $startDate = date('Y-m-d', strtotime('-5 day'));
        //组装订单同步条件：组装公司不为空，范围由创建时间决定
        $json = $json ?: [
            'filter' => [
                "zzhcreatedt"    => $startDate,
                "wxcompanycode" => "IS NOT NULL"
            ],
            'op'     => [
                "zzhcreatedt" => ">=",
            ]
        ];
        $startTime = microtime(true);
        $erpData = make(\App\Core\Services\TchipSale\SaleBaseService::class)
            ->sendRequest('firefly_erpapi/public/index.php/stock/zzh/getList', ['json' => $json], 'POST');
        if (!empty($erpData['data'])) {
            $data = [];
            $currentTime = time();
            $endOfDay = strtotime('tomorrow') - 2; // 今天结束的时间戳
            $processedOrders = $this->cache->get('assemble_processed_orders', []);

            foreach ($erpData['data'] as $order) {
                // 检查该订单号是否已处理
                if (in_array($order['zzhcode'], $processedOrders) || $this->model::query()->where('code', $order['zzhcode'] ?? '')->exists()) {
                    continue;
                }
                $paymentTime = $this->getPaymentTime($order['zzhcode'] ?? '');
                $paymentTime = TimeUtils::formatDate($paymentTime, 'Y-m-d H:i:s');
                $firstBackDate = $this->getFirstBackTime($order['zzhcode'] ?? '');
                $firstBackDate = TimeUtils::formatDate($firstBackDate);
                $data[] = [
                    //订单编号
                    'code'             => $order['zzhcode'] ?? '',
                    //产品名称
                    'product_name'     => $order['NAME'] ?? '',
                    //料号
                    'product_code'     => $order['zzhgoods'] ?? '',
                    //数量
                    'num'              => $order['zzhqty'] ?? 0,
                    //订单日期
                    //                    'order_date'       => $order['zzhcreatedt'] ?? null,
                    'order_date'      => isset($order['zzhcreatedt']) ? TimeUtils::formatDate($order['zzhcreatedt']) : null,
                    //业务员
                    //组装地点
                    'assemble_address' => $order['zzhcol4'] ?? '',
                    //出账时间
                    'payment_time'    => $paymentTime,
                    'first_back_date' => $firstBackDate,
                    //审核时间
                    //                    'checked_time'     => $order['zzhcheckdt'] ?? null,
                    'checked_time'    => isset($order['zzhcheckdt']) ? TimeUtils::formatDate($order['zzhcheckdt'], 'Y-m-d H:i:s') : null,
                    'info'             => [],
                ];
                // 将已处理的订单号添加到数组中
                $processedOrders[] = $order['zzhcode'];
            }

            // 更新缓存中的订单号，并设置过期时间为当天结束
            $this->cache->set('assemble_processed_orders', $processedOrders, $endOfDay - $currentTime);
            $parallel = new Parallel(5);
            $crontabData = [];
            foreach ($data as $item) {
                $crontabData[] = $item['code'] ?? '';
                // 调用 doEdit 方法，将 id 设置为 -1，并传递 $item 作为 values
                $parallel->add(function () use ($item) {
                    $orderIds = $parallelSuccessData = $parallelFailData = [];
                    try {
                        $result = $this->doEdit(-1, $item);
                        $orderIds[] = $result['id'] ?? 0;
                        $parallelSuccessData[] = $result;
                        $logData = Context::get(ProductionCode::LOG_KEY_TABLE_DATA, []);
                        return [
                            'order_ids'    => $orderIds,
                            'success_data' => $parallelSuccessData,
                            'fail_data'    => [],
                            'log_data'     => $logData
                        ];
                    } catch (Throwable $e) {
                        Log::get('system', 'system')
                            ->info('创建订单失败', [
                                'id'     => -1,
                                'values' => $item,
                                'error'  => $e->getMessage()
                            ]);
                        $parallelFailData[] = [
                            'data' => $item,
                            'msg'  => $e->getMessage()
                        ];
                        return [
                            'order_ids'    => [],
                            'success_data' => [],
                            'fail_data'    => $parallelFailData,
                            'log_data'     => []
                        ];
                    }
                });
            }
            $result = $parallel->wait();
            $orderIds = $successData = $failData = $logData = [];
            foreach ($result as $item) {
                $orderIds = array_merge($orderIds, $item['order_ids'] ?? []);
                $successData = array_merge($successData, $item['success_data'] ?? []);
                $failData = array_merge($failData, $item['fail_data'] ?? []);
                $logData = array_merge($logData, $item['log_data'] ?? []);
            }
            Context::set(ProductionCode::LOG_KEY_TABLE_DATA, $logData);
            //同步完成发生邮件通知
            if ($successData) {
                $userArr = make(UserService::class)->getUsersByRole(AssembleOrderCode::PRODUCTION_ROLE);
                $userArr = array_column($userArr, 'id');
                make(NoticeService::class)->syncAssembleOrderSend($userArr, $successData);
            }
            $endTime = microtime(true);
            Log::get('system', 'system')
                ->info('----------同步用时----------', [$endTime - $startTime]);
            return [
                'order_ids'      => $orderIds,
                'crontab_data'   => $crontabData,
                'crontab_result' => [
                    'success' => $successData,
                    'fail'    => $failData
                ],
                'crontab_status' => empty($failData) ? 1 : 2
            ];
        }
    }

    public function doEdit(int $id, array $values)
    {
        $result = [];
        $isChangeCompleted = false;
        $infoService = make(AssembleOrderInfoService::class);
        Db::beginTransaction();
        try {

            if ($id > 0) {
                $orderId = $id;
                $row = $this->model::query()->find($orderId);
                $info = AssembleOrderInfoModel::query()->where('assemble_order_id', $orderId)->first();
                if (!$row || !$info) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));

                }
                if (!empty($values)) {
                    //校验备货单号
                    if (!empty($values['stock_order_code']) && $values['stock_order_code'] != $row->stock_order_code) {
                        if (!StockOrderlistModel::query()->where('sn', $values['stock_order_code'])->exists()) {
                            throw new AppException(StatusCode::ERR_SERVER, __('production.Not_exit_stock_order'));
                        }
                    }
                    if (isset($values['is_complete']) && $values['is_complete'] == 1 && $values['is_complete'] != $info['is_complete']) {
                        $isChangeCompleted = true;
                    }

                    if (empty($row->created_by)) {
                        $values['created_by'] = auth()->id();
                    }
                    //业务员为空时，默认为文晓东
                    if(isset($values['sales_user'])){
                        if(empty($values['sales_user'])){
                            $values['sales_user'] = AssembleOrderCode::DEFAULT_ORDER_USER['order_user_id'];
                        }
                        $values['sales_user_id']=UserModel::query()->where('name', 'like', '%' . $values['sales_user'] . '%')->value('id') ?: 0;
                    }
                    //编辑订单信息
                    $result = $row->update($values);

                    //编辑基本信息
                    unset($values['id']);
                    if (!empty($values['info'])) {
                        $infoService->doEdit(0, $values['info']);
                    }
                    if (!empty($values['material_completeness'])) {
                        make(AssembleOrderMaterialCompletenessService::class)->batchDoEdit($orderId, $values['material_completeness']);
                    }
                }
            } else {
                //是否同步创建
                $isAutoCreated = $id == -1;
                if (empty($values['code'])) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, __('common.Missing_parameter'));
                }
                //校验是否已经创建
                if ($this->model::query()->where('code', $values['code'])->exists()) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, __('production.Exist_order_code'));
                }
                //校验备货单号
                if (!empty($values['stock_order_code'])) {
                    if (!StockOrderlistModel::query()->where('sn', $values['stock_order_code'])->exists()) {
                        throw new AppException(StatusCode::ERR_SERVER, __('production.Not_exit_stock_order'));
                    }
                }
                $values['created_by'] = $isAutoCreated ? 0 : auth()->id();
                //创建类型,手动新增还是同步新增
                $values['create_type'] = $isAutoCreated ? AssembleOrderCode::CREATE_TYPE_AUTO : AssembleOrderCode::CREATE_TYPE_HAND;
                //默认的订单相关人员
                if($isAutoCreated){
                    $this->setOrderDefaultUser($values);
                }else{
                    $value['info']['order_user_id'] = UserModel::query()->where('name',AssembleOrderCode::DEFAULT_ORDER_USER['order_user_id'])->value('id')?:0;
                }
                $result = $this->model::create($values);

                if (!empty($result->id)) {
                    $orderId = $result->id;
                    //保存基本信息
                    $infoData = array_merge($values['info'], [
                        'assemble_order_id' => $orderId
                    ]);
                    $infoService->doEdit(-1, $infoData,$isAutoCreated);
                    //填写了初始mac才分配mac地址

                    //创建订单时，先用订单号关联再用BOM关联
                    //获取该产品的BOM，然后去匹配QC模块中的检验记录
                    //当条件为最后一次不合格时且不是退货的话，主动关联该记录
                    make(AssembleOrderIqcService::class)->autoRelateQc($orderId);
                    //齐料状态更新
                    if (!empty($values['material_completeness'])) {
                        make(AssembleOrderMaterialCompletenessService::class)->batchDoEdit($orderId, $values['material_completeness']);
                    }

                    if ($isAutoCreated) {
                        Log::get('system', 'system')->info('创建订单成功', ['result' => $result]);
                    } elseif (isset($values['is_complete']) && $values['is_complete'] == 1) {
                        $isChangeCompleted = true;
                    }
                    //订单新建时自动关联异常
                    make(ExceptionRecordService::class)->doRelateException(ProductionCode::EXCEPTION_TYPE_ASSEMBLE_ORDER, $orderId);
                }
            }
            //信息完善后操作
            if ($isChangeCompleted && !empty($orderId)) {
                $this->sendCommonNotice($orderId, '有新的组装订单录入');
//                $this->sendNoticeAfterCompleted($orderId);
            }
            Db::commit();
            return $result;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    public function sendCommonNotice($orderId, $msg)
    {
        $order = $this->model::query()->find($orderId);
        $data = [
            'notice_msg'       => $msg,
            'code'             => $order['code'] ?? '',
            'product_name'     => $order['product_name'] ?? '',
            'woqty'            => $order['num'] ?? 0,
            'product_code'     => $order['product_code'] ?? '',
            'assemble_address' => $order['assemble_address'] ?? '',
            'host'             => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            'order_id'         => $order['id'] ?? 0
        ];
        $userArr = make(UserService::class)->getUsersByRole(AssembleOrderCode::PRODUCTION_ROLE);
        $userArr = array_column($userArr, 'id');
        make(NoticeService::class)->syncAssembleOrderSend($userArr, $data);
    }

    public function conf()
    {
        $data = [
            'approve_status'          => associativelyIndex(AssembleOrderCode::APPROVE_STATUS),
            'attachment_audit_status' => associativelyIndex(AssembleOrderCode::ATTACHMENT_AUDIT_STATUS),
            'summary_status'          => associativelyIndex(AssembleOrderCode::SUMMARY_STATUS),
            'material_status'         => associativelyIndex(AssembleOrderCode::MATERIAL_STATUS),
            'order_type'              => associativelyIndex(AssembleOrderCode::ORDER_TYPE),
            'assemble_type'           => associativelyIndex(AssembleOrderCode::ASSEMBLE_TYPE),
//            'attachment_type'         => associativelyIndex(AssembleOrderCode::ATTACHMENT_TYPE),
            'material_type'           => associativelyIndex(AssembleOrderCode::MATERIAL_TYPE),
            'shipment_place'          => associativelyIndex(AssembleOrderCode::SHIPMENT_PLACE),
            'assemble_test_status'    => associativelyIndex(AssembleOrderCode::ASSEMBLE_TEST_STATUS),
            'audit_status'            => associativelyIndex(AssembleOrderCode::ATTACHMENT_AUDIT_STATUS),
            'work_status'             => $this->getWorkStatusList(),
            'origin_type' => associativelyIndex(ProductionCode::ORIGIN_TYPE),
            'completeness_status' => associativelyIndex(AssembleOrderCode::COMPLETENESS_STATUS),
            'data_status' => associativelyIndex(AssembleOrderCode::DATA_STATUS),
            'express_company' => ProductionCode::EXPRESS_COMPANY,
            'change_category' => associativelyIndex(AssembleOrderCode::LOG_CHANGE_CATEGORY_ARR),
            'exception_status' => associativelyIndex(ProductionCode::EXCEPTION_STATUS),
        ];
        $data['work_tag'] = $this->getWorkTagOption($data['work_status']);
        return $data;
    }

    /**
     * 获取工作阶段状态
     * @param $workStatus
     * @return array
     */
    public function getWorkTagOption($workStatus)
    {
        $result = [];
        foreach ($workStatus as $item) {
            switch ($item['key']){
                case 'confirm_order':
                    $item['children'] = [
                        [
                            'id' => 'is_complete=0',
                            'name' => '未完成'
                        ]
                    ];
                    break;
                case 'production_preparation':
                    $item['children'] = [
                        [
                            'id' => 'completeness_status!=4',
                            'name' => '备料信息',
                        ],
                        [
                            'id' => 'assemble_data_status!=2',
                            'name' => '组装资料',
                        ],
                        [
                            'id' => 'soft_data_status!=3',
                            'name' => '软件资料',
                        ]
                    ];
                    break;
                case 'first_assembly':
                    $item['children'] = [
                        [
                            'id' => 'first_assemble_data_status=1',
                            'name' => '组装上传',
                        ],
                        [
                            'id' => 'first_assemble_data_status=2',
                            'name' => '组装审核',
                        ],
                        [
                            'id' => 'first_soft_data_status=1',
                            'name' => '软件上传',
                        ],

                        [
                            'id' => 'first_soft_data_status=2',
                            'name' => '软件审核',
                        ],
                    ];
                    break;
                case  'producing':
                    $item['children'] = [
                        [
                            'id' => 'product_finished_data_status=1',
                            'name' => '成品上传',
                        ],
                        [
                            'id' => 'product_finished_data_status=2',
                            'name' => '成品审核',
                        ],
                        [
                            'id' => 'product_soft_data_status=1',
                            'name' => '软件上传',
                        ],
                        [
                            'id' => 'product_soft_data_status=2',
                            'name' => '软件审核',
                        ],
                    ];
                    break;
                case  'assemble_summary':
                    $item['children'] = [
                        [
                            'id' => 'assemble_status=1',
                            'name' => '组装总结',
                        ],
                        [
                            'id' => 'test_status=1',
                            'name' => '测试总结',
                        ],
                    ];
                    break;
//                case  'shipping':
//                    $item['children'] = [
//                        [
//                            'id' => 'ship_data_status=1',
//                            'name' => '发货资料',
//                        ],
//                    ];
//                    break;
                case 'finished':
                    $item['children'] = [
                        [
                            'id' =>'approve_status=1',
                            'name' => '待核准',
                        ],
                    ];
                    break;
                default:
                    break;
            }
            if(!empty($item['children'])){
                $result[] = $item;
            }
        }
        return $result;
    }

    /**
     * 获取工作状态选项列表
     * @return mixed[]
     */
    public function getWorkStatusList()
    {
        return make(WorkStatusModel::class)::query()->where('scene_type', WorkFlowSceneCode::SCENE_ASSEMBLE_ORDER)->select([
            'id',
            'key',
            'name'
        ])->orderBy('sort','desc')->get()->toArray();
    }

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10, $field = [])
    {
        $keywords = !empty($filter['keywords']) ? $filter['keywords'] : '';
        unset($filter['keywords']);
        //阶段状态筛选
        $tagCondition = !empty($filter['work_status_tag'])? $filter['work_status_tag'] : '';
        unset($filter['work_status_tag']);
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        //关键字查询
        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords) {
                $query->where('code', 'like', '%' . $keywords . '%')
                    ->orWhere('product_name', 'like', '%' . $keywords . '%')
                    ->orWhere('product_code', 'like', '%' . $keywords . '%');
            });
        }
        //阶段状态筛选
        if ($tagCondition) {
            $query = $query->where(function($query)use($tagCondition){
                $query->whereRaw($tagCondition);
            });
        }

        //工厂默认测试人员只能查看自己的
        $userName = auth()->user()->name;
        $address = array_keys(AssembleOrderCode::DEFAULT_ASSEMBLE_ADDRESS_USER,$userName);
        if($address && in_array($userName,AssembleOrderCode::FACTORY_USER)){
            $query = $query->whereIn('assemble_address',$address);
        }

        $defaultFiled = [
            'assemble_order.*',
            'assemble_order_info.id as order_info_id',
            'assemble_order_info.assemble_order_id',
            'assemble_order_info.predict_online_date',
            'assemble_order_info.actual_online_date',
            'assemble_order_info.order_type',
            'assemble_order_info.assemble_type',
            'assemble_order_info.assemble_user_id',
            'assemble_order_info.test_user_id',
            'assemble_order_info.software_user_id',
            'assemble_order_info.shipment_place',
            'assemble_order_info.is_complete',
            'assemble_order_info.attachment_ids',
            'assemble_order_info.material_status',
            'assemble_order_info.material_remark',
            'assemble_order_info.other_remark',
            'assemble_order_info.summary_finish_status',
            'assemble_order_info.approve_status',
            'assemble_order_info.approve_date',
            'assemble_order_info.predict_delivery_date',
            'assemble_order_info.work_status_id',
            'assemble_order_info.completeness_status',
            'assemble_order_info.assemble_data_status',
            'assemble_order_info.soft_data_status',
            'assemble_order_info.first_assemble_data_status',
            'assemble_order_info.first_soft_data_status',
            'assemble_order_info.product_finished_data_status',
            'assemble_order_info.product_soft_data_status',
            'assemble_order_info.ship_data_status',
//            'assemble_order_info.pre_assemble_order_id',
            'assemble_order_info.pcba_remark',
            'assemble_order_info.sn_remark',
            'assemble_order_summary.id as summary_id',
            'assemble_order_summary.assemble_status',
            'assemble_order_summary.test_status',
            'assemble_order_summary.status as summary_status',
            'work_status.name as work_status_name',
            'work_status.key as work_status_key',
        ];
        $field = $field ?: $defaultFiled;
        //获取齐料字段
        $materialType = AssembleOrderCode::MATERIAL_TYPE;
        $defaultMaterialStatus = AssembleOrderCode::MATERIAL_STATUS_WAITING;
        foreach ($materialType as $key => $item) {
            $query = $query->selectRaw("MAX(CASE WHEN bi_assemble_order_material_completeness.type = {$key} THEN bi_assemble_order_material_completeness.status ELSE {$defaultMaterialStatus} END) AS material_status_{$key}");
        }
        $query = $query->leftJoin('assemble_order_info', 'assemble_order_info.assemble_order_id', '=', 'assemble_order.id')
            ->leftJoin('assemble_order_material_completeness', 'assemble_order_material_completeness.assemble_order_id', '=', 'assemble_order.id')
            ->leftJoin('work_status', 'work_status.id', '=', 'assemble_order_info.work_status_id')
            ->leftJoin('assemble_order_summary', 'assemble_order_summary.assemble_order_id', '=', 'assemble_order.id');
        //查询mac连表
        if(!empty($filter['mac_address'])){
            $query->leftJoin('mac_address',function($join) use($filter){
                $join->on('mac_address.used_relate_id', '=', 'assemble_order.id')
                    ->whereNull('mac_address.deleted_at')
                    ->where('mac_address.used_type', '=', ProductionCode::CODE_USED_TYPE_ASSEMBLE);
            });
        }
        if(!empty($filter['sn_code'])){
            $query->leftJoin('sn_code',function($join) use($filter){
                $join->on('sn_code.used_relate_id', '=', 'assemble_order.id')
                    ->whereNull('sn_code.deleted_at')
                    ->where('sn_code.used_type', '=', ProductionCode::CODE_USED_TYPE_ASSEMBLE);
            });
        }
        $paginate = $query->addSelect($field)
            ->when($sort!=='id', function ($query) use ($order,$sort) {
                $query->orderBy($sort, $order);
            }, function ($query) {
                $query->orderBy('assemble_order.assemble_address')->orderBy('assemble_order_info.predict_online_date');
            });

        $paginate = $paginate->groupBy('assemble_order.id')->paginate($limit)->toArray();

        //获取用户信息
        $userIdArr = collectFiledArrFromData($paginate['data'], [
            'sales_user_id',
            'created_by',
            'assemble_user_id',
            'software_user_id',
            'test_user_id',
        ]);
        $userData = $userIdArr ? UserModel::query()->whereIn('id', $userIdArr)->pluck('name', 'id')->toArray() : [];
        $conf = $this->conf();
        foreach ($paginate['data'] as &$item) {
            $item['sales_user_name'] = $userData[$item['sales_user_id']] ?? '';
            $item['created_by_name'] = $userData[$item['created_by']] ?? '';
            $item['assemble_user_name'] = $userData[$item['assemble_user_id']] ?? '';
            $item['test_user_name'] = $userData[$item['test_user_id']] ?? '';
            $item['software_user_name'] = $userData[$item['software_user_id']] ?? '';

            //生产中，补充未完成状态
            $item['work_status_tag'] = [];
            $item['work_status_append_text']  = '';
            $item['work_status_tips']  = '';
            switch ($item['work_status_key']){
                case 'confirm_order':
                    if($item['is_complete'] ==0){
                        $item['work_status_tag'][] = '待完善';
                    }
                    break;
                case 'production_preparation':
                    $isCompletenessStatusNot4 = $item['completeness_status'] != 4;
                    $isAssembleDataStatusNot2 = $item['assemble_data_status'] != 2;
                    $isSoftDataStatusNot3 = $item['soft_data_status'] != 3;
                    if($isCompletenessStatusNot4){
                        $item['work_status_tag'][] = '备料信息';
                    }
                    if($isAssembleDataStatusNot2){
                        $item['work_status_tag'][] = '组装资料';
                    }
                    if($isSoftDataStatusNot3){
                        $item['work_status_tag'][] = '软件资料';
                    }
                    break;
                case 'first_assembly':
                    if($item['first_assemble_data_status'] == 1){
                        $item['work_status_tag'][] = '组装上传';
                    }
                    if($item['first_soft_data_status'] == 1){
                        $item['work_status_tag'][] = '软件上传';
                    }
                    if($item['first_assemble_data_status'] == 2){
                        $item['work_status_tag'][] = '组装审核';
                    }
                    if($item['first_soft_data_status'] == 2){
                        $item['work_status_tag'][] = '软件审核';
                    }
                    break;
                case  'producing':
                    if($item['product_finished_data_status'] == 1){
                        $item['work_status_tag'][] = '成品上传';
                    }
                    if($item['product_soft_data_status'] == 1){
                        $item['work_status_tag'][] = '软件上传';
                    }
                    if($item['product_finished_data_status'] == 2){
                        $item['work_status_tag'][] = '成品审核';
                    }
                    if($item['product_soft_data_status'] == 2){
                        $item['work_status_tag'][] = '软件审核';
                    }
                    break;
                case  'assemble_summary':
                    if(empty($item['summary_id'])){
                        $item['work_status_tag'][] = '组装总结';
                        $item['work_status_tag'][] = '测试总结';
                    }else{
                        if($item['assemble_status'] == 1){
                            $item['work_status_tag'][] = '组装总结';
                        }
                        if($item['test_status'] == 1){
                            $item['work_status_tag'][] = '测试总结';
                        }
                    }
                    break;
                case  'shipping':
                    if($item['ship_data_status'] == 1){
                        $item['work_status_tag'][] = '发货资料';
                    }
                    break;
                case 'finished':
                        if($item['approve_status'] == 1){
                            $item['work_status_tag'][] = '待核准';
                        }
                    break;
                default:
                    break;
            }
            if(!empty($item['approve_date'])){
                $item['order_days'] = $item['order_date'] ? (int)((strtotime($item['approve_date']) - strtotime($item['order_date'])) / 86400) : null;
            }else{
                $item['order_days'] = $item['order_date'] ? (int)((time() - strtotime($item['order_date'])) / 86400) : null;
            }
            $item['order_type_text'] = $conf['order_type'][$item['order_type']]['name'] ?? '';
            $item['assemble_type_text'] = $conf['assemble_type'][$item['assemble_type']]['name'] ?? '';
            $item['approve_status_text'] = $conf['approve_status'][$item['approve_status']]['name'] ?? '';
            $item['shipment_place_text'] = $conf['shipment_place'][$item['shipment_place']]['name'] ?? '';
            $item['has_not_audit'] = AssembleOrderAttachmentModel::query()->where('assemble_order_id', $item['id'])
                ->where('file_key', AssembleOrderCode::ATTACHMENT_TYPE_FIRMWARE_CUSTOMER)
                ->where('audit_status',1)->exists();
            //齐料备注
            $item['completeness_remark'] = $this->getCompletenessRemark($item['id']);
            //日期
            $item['payment_time_text'] = TimeUtils::formatDate($item['payment_time']);
            $item['checked_time_text'] = TimeUtils::formatDate($item['checked_time']);
        }
        return $paginate;
    }

    /**
     * 获取各种齐料类型的备注汇集
     * @param $id
     * @return string
     */
    public function getCompletenessRemark($id)
    {
        //齐料备注
        $materialList = AssembleOrderMaterialCompletenessModel::query()
            ->where('assemble_order_id', $id)
            ->whereNotNull('remark')->orderBy('type')->pluck('remark', 'type')->toArray();
        $completenessRemark = '';
        foreach ($materialList as $k => $v) {
            if($v != ''){
                $completenessRemark .= (AssembleOrderCode::MATERIAL_TYPE[$k] ?? '') . '：' . $v . "；\n";
            }
        }
        return $completenessRemark;
    }

    //上线准备

    public function getOverView($id)
    {
        $overView = $this->model::query()->with([
            'info',
        ])->find($id);
        $overView = $overView ? $overView->toArray() : [];
        if ($overView) {
            //订单天数
            if(!empty($overView['info']['approve_date'])){
                $overView['order_days'] = $overView['order_date'] ? (int)((strtotime($overView['info']['approve_date']) - strtotime($overView['order_date'])) / 86400) : null;
            }else{
                $overView['order_days'] = $overView['order_date'] ? (int)((time() - strtotime($overView['order_date'])) / 86400) : null;
            }
            //获取相关人员
            $userArr = [
                $overView['created_by'],
                $overView['info']['assemble_user_id'],
                $overView['info']['software_user_id'],
                $overView['info']['test_user_id'],
            ];
            $userArr = array_unique(array_filter($userArr));
            if (!empty($userArr)) {
                $userData = UserModel::query()->whereIn('id', $userArr)->pluck('name', 'id')->toArray();
            }
            $overView['created_by_name'] = $userData[$overView['created_by']] ?? '';
            $overView['info']['assemble_user_name'] = $userData[$overView['info']['assemble_user_id']] ?? '';
            $overView['info']['software_user_name'] = $userData[$overView['info']['software_user_id']] ?? '';
            $overView['info']['test_user_name'] = $userData[$overView['info']['test_user_id']] ?? '';
            //附件
            $overView['info']['attachments'] = empty($overView['info']['attachment_ids']) ? [] : AttachmentModel::query()->whereIn('id', $overView['info']['attachment_ids'])->get()->toArray();
            //齐料
            $overView['material_completeness'] = make(AssembleOrderMaterialCompletenessService::class)->getMaterialStatus($id);

            //mac地址
            $overView['mac_address_arr'] = MacAddressModel::query()
                ->leftJoin('mac_address_relation as relation', 'relation.mac_address_id', '=', 'mac_address.id')
                ->where([
                    'relation.relation_type' => ProductionCode::CODE_USED_TYPE_ASSEMBLE,
                    'relation.relation_id'   => $id
                ])
                ->whereNull('relation.deleted_at')
                ->select([
                    'mac_address.id as mac_id',
                    'mac_address.mac_address'
                ])
                ->get()->toArray();
            //状态
            $overView['info']['work_status_key'] = $overView['info']['work_status_id']
                ? WorkStatusModel::query()->where('id', $overView['info']['work_status_id'])->value('key')
                : '';
            //生产次数
            $overView['production_count'] = $this->getProductionCount($overView) ;
            //sn附件
            $overView['info']['mac_sn_attachment'] = $overView['info']['mac_sn_attachment_id'] ?
                AttachmentModel::query()->where('id', $overView['info']['mac_sn_attachment_id'])->get() : [];
            $overView['payment_time_text'] = TimeUtils::formatDate($overView['payment_time']);
            $overView['checked_time_text'] = TimeUtils::formatDate($overView['checked_time']);

            //获取上次生产总结订单id
            $overView['info']['pre_assemble_order_id'] =$this->getPreOrderId($overView['product_code'],$overView['id']);

            // 销售订单要求数据获取
            $overView['order_demand'] = !empty($overView['stock_order_code']) ? make(OrderService::class)->orderDemandByStockSn($overView['stock_order_code']) : null;
        }

        return $overView;
    }

    /**
     * 获取同个产品上次挂起的订单
     * @param $productCode
     * @return false|\Hyperf\Utils\HigherOrderTapProxy|int|mixed|\Tightenco\Collect\Support\HigherOrderTapProxy
     */
    public function getPreOrderId($productCode,$order_id)
    {
        return $this->model::query()
            ->leftJoin('assemble_order_info as info', 'info.assemble_order_id', '=', 'assemble_order.id')
            ->where('product_code', $productCode)
            ->where('assemble_order.id','<',$order_id)
            ->where('info.summary_finish_status', '=', 3)
            ->orderBy('assemble_order.created_at', 'desc')
            ->select('assemble_order.id')
            ->value('id')?:0;
    }

    public function prepareForOnline()
    {
        //添加工作状态
        make(WorkStatusService::class, [self::SCENE_TYPE])->handleInit();
        make(WorkFlowService::class, [self::SCENE_TYPE])->handleInit();
    }

    /**
     * 删除
     * @param $ids
     * @return int
     */
    public function doDelete($ids): int
    {

        Db::beginTransaction();
        try {

            $ids = !is_array($ids) ? explode(',', $ids) : $ids;
            $orderList = $this->model::query()->whereIn('id', $ids)->get();
            foreach ($orderList as $order) {
                $info = AssembleOrderInfoModel::query()->where('assemble_order_id', $order->id)->first();
                if (!empty($info['start_mac_address'])) {
                    //取消mac绑定
                    make(MacAddressService::class)->reduceByRelationId($order->id, ProductionCode::CODE_USED_TYPE_ASSEMBLE, $order->woqty);
                }
                //解绑和qc的关联
                make(AssembleOrderIqcService::class)->deleteRelation($order->id);
            }
            $result = $this->model::destroy($ids);
            Db::commit();
            return $result;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 导出订单
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function export(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        $data = $this->getList($filter, $op, $sort, $order, 9999);
        $data = !empty($data['data']) ? $data['data'] : [];
        $filename = "组装订单.xls";
        $sheetName = 'sheet1';
        $excelWriter = new ExcelWriter($filename, $sheetName);
        $option = [
            '序号'         => 'index',
            '订单编号'     => 'code',
            '产品名称'     => 'product_name',
            '数量'         => 'num',
            '料号'         => 'product_code',
            '订单类型'     => 'order_type_text',
            '订单日期'     => 'order_date',
            '天数'         => 'order_days',
            '审核时间'     => 'checked_time_text',
            '出账时间'     => 'payment_time_text',
            '组装类型'     => 'assemble_type_text',
            '组装地点'     => 'assemble_address',
            '首次回货日期' => 'first_back_date',
            '出货地点'     => 'shipment_place_text',
            '交货日期'     => 'delivery_date',
            '业务员'       => 'sales_user',
            '软件负责人'   => 'software_user_name',
            '组装负责人'   => 'assemble_user_name',
            '状态'         => 'work_status_name',
            '核准状态'     => 'approve_status_text',
            '材料情况'     => 'material_remark',
            '其他备注'     => 'other_remark',
        ];
        $titleData = [
            array_keys($option)
        ];

        $excelWriter->addData($titleData);
        $row = [];
        foreach ($data as $k => $v) {
            //获取目录
            $index = $k + 1;
            $tempRow = [];
            foreach ($option as $key => $value) {
                if($value ==='index'){
                    $tempRow[] = $index;
                }else{
                    $tempRow[] = $v[$value];
                }
            }
            $row = [
                $tempRow
            ];
            $excelWriter->addData($row);
        }
        $result = $excelWriter->download();
        $excelWriter->close();
        return $result;
    }

    /**
     * 加锁防止同步并发
     * @param $isCli bool 是否命令执行
     * @return bool
     */
    public function syncStart(bool $isCli = false)
    {
        $cacheKey = 'syncAssembleOrder';
        if ($this->cache->has($cacheKey)) {
            if ($isCli) {
                //定时任务执行时，如果有同步执行等待10秒待其结束
                for ($i = 0; $i < 10; $i++) {
                    sleep(1);
                    if (!$this->cache->has($cacheKey)) {
                        break;
                    }
                    if($i==9){//超时不同步
                        return false;
                    }
                }
            } else {
                throw new AppException(StatusCode::ERR_SERVER, __('production.Has_other_sync_task'));
            }
        }
        $this->cache->set($cacheKey, $cacheKey, 300);
        return true;
    }

    /**去锁
     * @return void
     */
    public function syncEnd()
    {
        $cacheKey = 'syncAssembleOrder';
        $this->cache->delete($cacheKey);
    }

    /**
     * 获取生产次数
     * @param $overView
     * @return int
     */
    public function getProductionCount($overView){
        return $this->model::query()->where('product_code',$overView['product_code'])
            ->where(function($query)use($overView){
                $query->where('order_date','<',$overView['order_date'])
                    ->orwhere(function ($query)use($overView){
                        $query->where('order_date','=',$overView['order_date'])
                            ->where('id','<=',$overView['id']);
                    });
            })
            ->count();
    }

    /**
     * 备货清单的详情
     * @param $code
     * @return array
     */
    public function getStockOverView($code)
    {
        $stockOrder =  StockOrderlistModel::query()->where('sn',$code)->first();
        if($stockOrder){
            $stockOrder['sales_user_name'] = UserTableModel::query()->where('id',$stockOrder['user_id'])->value('realname');
        }
        return $stockOrder ? $stockOrder->toArray() : [];
    }

    /**
     * 获取组装订单在erp的数据
     * @param $code
     * @return array|mixed
     */
    public function getOrderErp($code)
    {
        if(empty($code))return [];
        $json = [
            'filter' => [
                'zzhcode' => $code,
            ],
        ];
        $erpInfo = make(\App\Core\Services\TchipSale\SaleBaseService::class)
            ->sendRequest('firefly_erpapi/public/index.php/stock/zzh/overview', ['json' => $json], 'POST');

        if(!empty($erpInfo['data'])){
            $erpInfo['data']['payment_time'] = $this->getPaymentTime($code);
            $erpInfo['data']['first_back_date'] = $this->getFirstBackTime($code);
        }
        return empty($erpInfo['data']) ? []:$erpInfo['data'];
    }

    /**
     * 更新订单
     */
    public function updateAssembleOrder(): array
    {
        //先查出未核准的数据
        $orderIds = AssembleOrderInfoModel::query()->where('approve_status', AssembleOrderCode::APPROVE_STATUS_WAIT)->pluck('assemble_order_id')->toArray();
        if (empty($orderIds)) return [];

        $list = $this->model::query()->whereIn('assemble_order.id', $orderIds)->get();

        $erpField = [
            [
                'order_field' => 'product_name',
                'relate_field'   => 'product_name',
                'description' => '产品名称',
                'type'        => 'string'
            ],
            [
                'order_field' => 'product_code',
                'relate_field'   => 'zzhgoods',
                'description' => '料号',
                'type'        => 'string'
            ],
            [
                'order_field' => 'num',
                'relate_field'   => 'zzhqty',
                'description' => '数量',
                'type'        => 'int'
            ],
            [
                'order_field' => 'assemble_address',
                'relate_field'   => 'zzhcol4',
                'description' => '组装地点',
                'type'        => 'string'
            ],
            [
                'order_field' => 'order_date',
                'relate_field'   => 'zzhcreatedt',
                'description' => '订单日期',
                'type'        => 'date'
            ],
            [
                'order_field' => 'checked_time',
                'relate_field'   => 'zzhcheckdt',
                'description' => '审核日期',
                'type'        => 'datetime'
            ],
            [
                'order_field' => 'payment_time',
                'relate_field'   => 'payment_time',
                'description' => '出账日期',
                'type'        => 'datetime'
            ],
            [
                'order_field' => 'first_back_date',
                'relate_field'   => 'first_back_date',
                'description' => '首次回货日期',
                'type'        => 'date'
            ],
        ];
        $stockField = [
            [
                'order_field' => 'sales_user',
                'relate_field'   => 'sales_user_name',
                'description' => '业务员',
                'type'        => 'string'
            ],
            [
                'order_field' => 'delivery_date',
                'relate_field'   => 'ship_time',
                'description' => '交货日期',
                'type'        => 'date'
            ],
            [
                'order_field' => 'customer_remark',
                'relate_field'   => 'notes',
                'description' => '客户备注',
                'type'        => 'string'
            ],

        ];
        $parallel = new Parallel(5);
        $crontabData = [];
        foreach ($list as $item) {
            $crontabData[] = $item->code;
            $parallel->add(function () use ($item, $erpField, $stockField) {
                $parallelSuccessData = $parallelFailData = [];
                try {
                    //更新erp信息
                    $erpInfo = $this->getOrderErp($item->code);
                    $values = [];
                    if (!empty($erpInfo)) {
                        $this->formatUpdateField($erpField,$erpInfo,$values);
                    }
                    if ($item->stock_order_code) {
                        $stockInfo = $this->getStockOverView($item->stock_order_code);
                        if(!empty($stockInfo)){
                            $this->formatUpdateField($stockField,$stockInfo,$values);
                            if(!empty($values['sales_user'])){
                                $values['sales_user_id']=UserModel::query()->where('name', 'like', '%' . $values['sales_user'] . '%')->value('id') ?: 0;
                            }
                        }
                    }
                    if(!empty($values)){
                        $msg = '';
                        $infoValue = [];
                        $allField = $item->stock_order_code && !empty($stockInfo) ? array_merge($erpField, $stockField) : $erpField;
                        foreach ($allField as $val){
                            $key = $val['order_field'];
                            if($item[$key] != $values[$key]){
                                //组装地点修改，连带测试负责人一起修改
                                if ($key == 'assemble_address') {
                                    $defaultUser = AssembleOrderCode::DEFAULT_ASSEMBLE_ADDRESS_USER[$values['assemble_address']] ?? 0;
                                    $defaultUserId = UserModel::query()->where('name', 'like', '%'. $defaultUser. '%')->value('id')?: 0;
                                    $infoValue['test_user_id'] = $defaultUserId;
                                }
                                $msg .= $val['description'].'从【'.$item[$key].'】更新为【'.$values[$key]."】\n";
                            }
                        }
                        $params = make(AssembleOrderInfoService::class)->getCommonNoticeParams($item);
                        $item->update($values);
                        $valueTemp = $item->getChanges();
                        if (!empty($valueTemp)) {
                            $valueTemp['id'] = $item->id;
                            $valueTemp['code'] = $item->code;
                            $parallelSuccessData[] = $valueTemp;
                        }

                        //如果需要改变info的信息
                        if(!empty($infoValue)){
                            AssembleOrderInfoModel::query()->where('assemble_order_id', $item->id)->update($infoValue);
                        }

                        if($msg){
                            $msg = '订单信息已更新：'."\n".$msg;
                            $params['notice_msg'] = $msg;
                            $sendUser = make(AssembleOrderInfoService::class)->getOrderUser($item['id']);
                            //发送消息
                            make(NoticeService::class)->commonAssembleOrderNotice($sendUser, $params);
                        }
                    }
                    $logData = Context::get(ProductionCode::LOG_KEY_TABLE_DATA, []);
                    return [
                        'success_data' => $parallelSuccessData,
                        'fail_data'    => [],
                        'log_data'     => $logData
                    ];
                } catch (Exception $e) {
                    $parallelFailData[] = [
                        'data' => $item,
                        'msg'  => $e->getMessage()
                    ];
                    Log::get('system', 'system')
                        ->info('更新订单失败', [
                            'id'     => -1,
                            'values' => $item,
                            'error'  => $e->getMessage()
                        ]);
                    return [
                        'success_data' => [],
                        'fail_data'    => $parallelFailData,
                        'log_data'     => []
                    ];
                }
            });
        }
        try {
            $result = $parallel->wait();
        } catch (Exception $e) {
            $result = [
                'success_data' => [],
                'fail_data'    => [
                    ['msg'  => $e->getMessage(),
                     'data' => []
                    ]
                ],
                'log_data'     => []
            ];
        }

        $successData = $failData = $logData = [];
        foreach ($result as $item) {
            $successData = array_merge($successData, $item['success_data'] ?? []);
            $failData = array_merge($failData, $item['fail_data'] ?? []);
            $logData = array_merge($logData, $item['log_data'] ?? []);
        }

        Context::set(ProductionCode::LOG_KEY_TABLE_DATA, $logData);
        //返回日志内容
        return [
            'order_ids'      => $orderIds,
            'crontab_data'   => $crontabData,
            'crontab_result' => [
                'success' => $successData,
                'fail'    => $failData
            ],
            'crontab_status' => empty($failData) ? 1 : 2
        ];
    }

    //同步更新时先格式化字段再进行比较
    public function formatUpdateField($fields, $info, &$values)
    {
        foreach ($fields as $field) {
            if ($field['type'] == 'int') {
                $values[$field['order_field']] = (int)$info[$field['relate_field']] ?? 0;
            } elseif ($field['type'] == 'datetime') {
                $values[$field['order_field']] = TimeUtils::formatDate($info[$field['relate_field']] ?? '', 'Y-m-d H:i:s');
            } elseif ($field['type'] == 'date') {
                $values[$field['order_field']] = TimeUtils::formatDate($info[$field['relate_field']] ?? '');
            } else {
                $values[$field['order_field']] = $info[$field['relate_field']]??'';
            }
        }
    }

    /**
     * 获取出账时间
     * @param $code
     * @return mixed|null
     */
    public function getPaymentTime($code)
    {
        if(empty($code))return null;
        $json = [
            'filter' => [
                'Stock_gosd.FROMCODE' => $code,
                "Stock_gosh.CHECKDATE"=>"IS NOT NULL"
            ],
            'op'     => [],
            'sort'   => 'CHECKDATE',
            'order'  => 'ASC',
            'limit'  => 1
        ];
        $erpInfo = make(\App\Core\Services\TchipSale\SaleBaseService::class)
            ->sendRequest('firefly_erpapi/public/index.php/stock/gosd/getPage', ['json' => $json], 'POST');
        return $erpInfo['data']['data'][0]['CHECKDATE'] ?? null;
    }

    /**
     * 获取首次回货时间
     * @param $code
     * @return mixed|null
     */
    public function getFirstBackTime($code)
    {
        if(empty($code))return null;
        $json = [
            'filter' => [
                'FROMCODE' => $code,
            ],
        ];
        $erpInfo = make(\App\Core\Services\TchipSale\SaleBaseService::class)
            ->sendRequest('firefly_erpapi/public/index.php/stock/pisd/getPage', ['json' => $json], 'POST');
        return $erpInfo['data']['data'][0]['CHECKDATE'] ?? null;
    }

    /**设置订单默认人员
     * @param $value
     * @return void
     */
    public function setOrderDefaultUser(&$value)
    {
        $defaultUser = AssembleOrderCode::DEFAULT_ORDER_USER;
        if(!empty($value['assemble_address'])){
            $defaultTestUser = AssembleOrderCode::DEFAULT_ASSEMBLE_ADDRESS_USER[$value['assemble_address']]??'';
            if(!empty($defaultTestUser)){
                $defaultUser['test_user_id'] = $defaultTestUser;
            }
        }
        $userName = array_values($defaultUser);
        $userData = UserModel::query()->whereIn('name',$userName)->pluck('id','name')->toArray();
        foreach ($defaultUser as $key=>$val){
            if(!empty($userData[$val])){
                $value['info'][$key] = $userData[$val];
            }
        }
    }

    /**
     * 获取订单的mac
     * @param $param
     * @return array|mixed[]
     */
    public function getOrderCode($param)
    {
        if (empty($param['code_type']) || empty($param['assemble_order_id'])) {
            throw new AppException(StatusCode::VALIDATION_ERROR, __('common.Missing_parameter'));
        }
        if ($param['code_type'] == 'mac') {
            $result = make(MacAddressService::class)->getOrderCode(ProductionCode::CODE_USED_TYPE_ASSEMBLE, $param['assemble_order_id']);
        } elseif ($param['code_type'] == 'sn') {
            $result = make(SnCodeService::class)->getOrderCode(ProductionCode::CODE_USED_TYPE_ASSEMBLE, $param['assemble_order_id']);
        }elseif($param['code_type'] == 'mac_sn_range'){
            $relateOrder = [
                'used_type' => ProductionCode::CODE_USED_TYPE_ASSEMBLE,
                'used_relate_id' => $param['assemble_order_id']
            ];
            return [
                'mac_range' => make(MacAddressService::class)->getOrderCodeRange($relateOrder,'text'),
                'sn_range' => make(SnCodeService::class)->getOrderCodeRange($relateOrder,'text'),
            ];
        } else {
            $result = [];
        }
        //添加序号
        $index = 1;
        return array_map(function ($item) use (&$index) {
            $item['index'] = $index++;
            return $item;
        }, $result);
    }

    /**
     * 2025-01-03更新任务
     * @return void
     */
    public function updateWorkFlow()
    {
        Db::beginTransaction();
        try {
            //添加条件，避免再次执行
            //1.查出原来的工作状态
            $oldWorkStatus = WorkStatusModel::query()->where('scene_type', self::SCENE_TYPE)->pluck('key', 'id')->toArray();
            if(in_array('production_preparation',$oldWorkStatus)) return;
            //2.删除原来的工作状态和工作流
            WorkStatusModel::query()->where('scene_type', self::SCENE_TYPE)->delete();
            WorkFlowModel::query()->where('scene_type', self::SCENE_TYPE)->delete();
            //3.插入新的工作状态
            make(WorkStatusService::class, [self::SCENE_TYPE])->handleInit();
            make(WorkFlowService::class, [self::SCENE_TYPE])->handleInit();
            //更新生产订单is_reverse字段
            $productionStatus = WorkStatusModel::query()->where('scene_type', WorkFlowSceneCode::SCENE_PRODUCTION_ORDER)->pluck('sort', 'id')->toArray();
            $productionFlow = WorkFlowModel::query()->where('scene_type', WorkFlowSceneCode::SCENE_PRODUCTION_ORDER)->select([
                'id',
                'new_status_id',
                'old_status_id'
            ])->get();
            foreach ($productionFlow as $item) {
                $oldStatus = $item->old_status_id;
                $newStatus = $item->new_status_id;
                if ($productionStatus[$oldStatus] < $productionStatus[$newStatus]) {
                    $item->is_reverse = 1;
                    $item->save();
                }
            }
            //4.查询新的工作状态
            $newWorkStatus = WorkStatusModel::query()->where('scene_type', self::SCENE_TYPE)->pluck('id', 'key')->toArray();
            //5.更新订单的工作状态
            $orderList = AssembleOrderInfoModel::query()->get();
            foreach ($orderList as $item) {
                $oldStatusId = $item->work_status_id;
                $oldStatusKey = $oldWorkStatus[$oldStatusId] ?? '';
                switch ($oldStatusKey) {
                    case 'finished':
                    case 'to_approve':
                        $item->work_status_id = $newWorkStatus['finished'] ?? 0;
                        $item->completeness_status = 4;
                        $item->first_assemble_data_status = 3;
                        $item->first_soft_data_status = 3;
                        $item->soft_data_status = 3;
                        $item->assemble_data_status = 2;
                        break;
                    case 'pending_summary':
                        $item->work_status_id = $newWorkStatus['assemble_summary'] ?? 0;
                        $item->completeness_status = 4;
                        $item->first_assemble_data_status = 3;
                        $item->first_soft_data_status = 3;
                        $item->soft_data_status = 3;
                        $item->assemble_data_status = 2;
                        break;
                    case 'transferred_to_assembly':
                        $item->work_status_id = $newWorkStatus['production_preparation'] ?? 0;
                        $item->completeness_status = 4;
                        break;
                    case 'prepared_not_sent':
                        $item->work_status_id = $newWorkStatus['production_preparation'] ?? 0;
                        $item->completeness_status = 3;
                        break;
                    case 'preparing_materials':
                        if ($item->is_complete == 0) {
                            $item->work_status_id = $newWorkStatus['confirm_order'] ?? 0;
                        } else {
                            $item->work_status_id = $newWorkStatus['production_preparation'] ?? 0;
                        }
                        break;
                    default:
                        $item->work_status_id = $newWorkStatus['confirm_order'] ?? 0;
                        break;
                }
                $item->save();
            }
            Db::commit();
            Log::get('system', 'system')->info("更新成功");
        } catch (Exception $e) {
            Db::rollBack();
            Log::get('system', 'system')->info("更新失败".$e->getMessage());
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }

    }

    public function addWorkFlow()
    {
        Db::beginTransaction();
        try {
            //判断已经添加新的工作状态，防止重复执行脚本
            if(WorkStatusModel::query()->where('scene_type', self::SCENE_TYPE)->where('key','producing')->exists())return;
            //添加工作状态
            $sceneType = self::SCENE_TYPE;
            $addWorkStatus = [
                [
                    'name' => '生产中',
                    'key'  => 'producing',
                    'sort' => '970',
                    'scene_type' => $sceneType
                ],
//                [
//                    'name' => '发货',
//                    'key'  => 'shipping',
//                    'sort' => '950',
//                    'scene_type' => $sceneType
//                ],
            ];
            WorkStatusModel::query()->insert($addWorkStatus);
//            //添加工作流程
            WorkFlowModel::query()->where('scene_type', self::SCENE_TYPE)->delete();
            make(WorkFlowService::class, [self::SCENE_TYPE])->handleInit();
            //根据阶段状态来修改生产资料状态和发货资料状态
            //完成状态的，生产资料状态为完成，发货资料状态为完成
            $finishStatus = WorkStatusModel::query()->where('scene_type',self::SCENE_TYPE)
                ->where('key', 'finished')->value('id')?:0;
            $infoList = AssembleOrderInfoModel::query()->where('work_status_id',$finishStatus)->get();

            $finishedId = array_object_column($infoList,'id');
            AssembleOrderInfoModel::query()->whereIn('id',$finishedId)->update([
                'product_finished_data_status' => 3,
                'product_soft_data_status' => 3,
//                'ship_data_status' => 2,
            ]);
            //组装总结的，生产资料状态为完成
            $summaryStatus = WorkStatusModel::query()->where('scene_type',self::SCENE_TYPE)
                ->where('key', 'assemble_summary')->value('id')?:0;
            $infoList = AssembleOrderInfoModel::query()->where('work_status_id',$summaryStatus)->get();
            $summaryId = array_object_column($infoList,'id');
            AssembleOrderInfoModel::query()->whereIn('id',$summaryId)->update([
                'product_finished_data_status' => 3,
                'product_soft_data_status' => 3,
            ]);
            Db::commit();
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 获取工作状态流
     * @param $id
     * @return array
     */
    public function getWorkFlow($id)
    {
        $info = AssembleOrderInfoModel::query()->where('assemble_order_id', $id)->first();
        if (empty($info)) {
            return [];
        }
        $workStatusID = $info->work_status_id;
        $isFinish = $info->approve_status > 1;
        return make(WorkStatusService::class,[self::SCENE_TYPE])->formatStatus($workStatusID, $isFinish);
    }

    /**
     * 关联订单
     * @param $id
     * @param $params
     * @return true
     */
    public function relateOrder($id, $params)
    {
        if (empty($params['relate_order_code'])) {
            throw new AppException(StatusCode::VALIDATION_ERROR, __('common.Missing_parameter'));
        }

        Db::beginTransaction();
        try {
            // 1. 先查询原始订单和目标订单
            $sourceOrder = $this->model::query()->where('code', $params['relate_order_code'])->first();
            $targetOrder = $this->model::query()->find($id);

            if (!$sourceOrder) {
                throw new AppException(StatusCode::VALIDATION_ERROR, '未找到来源订单');
            }
            if (!$targetOrder) {
                throw new AppException(StatusCode::VALIDATION_ERROR, '未找到目标订单');
            }

            // 1.1 同步主订单表信息的部分字段
            $targetOrder->update([
                'first_back_date'  => $sourceOrder->first_back_date,
                'stock_order_code' => $sourceOrder->stock_order_code,
                'delivery_date' => $sourceOrder->delivery_date,
                'sales_user' => $sourceOrder->sales_user,
                'sales_user_id' => $sourceOrder->sales_user_id,
                'customer_remark' => $sourceOrder->customer_remark
            ]);

            // 2. 同步info信息
            $sourceInfo = AssembleOrderInfoModel::query()->where('assemble_order_id', $sourceOrder->id)->first();
            if ($sourceInfo) {
                $targetInfo = AssembleOrderInfoModel::query()->where('assemble_order_id', $id)->first();
                $infoData = $sourceInfo->toArray();
                unset($infoData['id'], $infoData['created_at'], $infoData['updated_at']);

                // 不同步MAC和SN相关字段
                unset(
                    $infoData['mac_sn_attachment_id'],
                    $infoData['mac_text_origin_type'],
                    $infoData['sn_text_origin_type'],
                    $infoData['sn_no_text'],
                    $infoData['mac_address_text'],
                    $infoData['mac_range_origin_type'],
                    $infoData['sn_range_origin_type'],
                    $infoData['sn_no_range'],
                    $infoData['mac_address_range']
                );

                $infoData['assemble_order_id'] = $id;
                $infoData['relate_order_code'] = $params['relate_order_code']; // 记录来源订单编号

                if ($targetInfo) {
                    $targetInfo->update($infoData);
                } else {
                    AssembleOrderInfoModel::query()->create($infoData);
                }
            }

            // 3. 同步material信息
            $sourceMaterials = AssembleOrderMaterialCompletenessModel::query()
                ->where('assemble_order_id', $sourceOrder->id)
                ->get();
            // 先删除目标订单的齐料记录
            AssembleOrderMaterialCompletenessModel::query()
                ->where('assemble_order_id', $id)
                ->delete();
            if ($sourceMaterials && $sourceMaterials->count() > 0) {
                // 遍历复制
                foreach ($sourceMaterials as $material) {
                    $materialData = $material->toArray();
                    unset($materialData['id'], $materialData['created_at'], $materialData['updated_at']);
                    $materialData['assemble_order_id'] = $id;
                    AssembleOrderMaterialCompletenessModel::query()->create($materialData);
                }
            }

            // 4. 同步attachment信息 - 需要按category_pid、category_id、created_at排序
            $sourceAttachments = AssembleOrderAttachmentModel::query()
                ->where('assemble_order_id', $sourceOrder->id)
                ->orderBy('category_pid')
                ->orderBy('category_id')
                ->orderBy('created_at')
                ->get();
            // 先删除目标订单的附件记录
            AssembleOrderAttachmentModel::query()
                ->where('assemble_order_id', $id)
                ->delete();
            if ($sourceAttachments && $sourceAttachments->count() > 0) {
                // 遍历复制
                foreach ($sourceAttachments as $attachment) {
                    $attachmentData = $attachment->toArray();
                    unset($attachmentData['id'], $attachmentData['created_at'], $attachmentData['updated_at']);
                    $attachmentData['assemble_order_id'] = $id;
                    AssembleOrderAttachmentModel::query()->create($attachmentData);
                }
            }

            // 5. 同步iqc信息
            $sourceIqcs = AssembleOrderIqcModel::query()
                ->where('assemble_order_id', $sourceOrder->id)
                ->get();
            // 先删除目标订单的iqc记录
            AssembleOrderIqcModel::query()
                ->where('assemble_order_id', $id)
                ->delete();
            if ($sourceIqcs && $sourceIqcs->count() > 0) {
                // 遍历复制
                foreach ($sourceIqcs as $iqc) {
                    $iqcData = $iqc->toArray();
                    unset($iqcData['id'], $iqcData['created_at'], $iqcData['updated_at']);
                    $iqcData['assemble_order_id'] = $id;

                    AssembleOrderIqcModel::query()->create($iqcData);
                }
            }

            // 6. 同步summary信息
            $sourceSummary = AssembleOrderSummaryModel::query()
                ->where('assemble_order_id', $sourceOrder->id)
                ->first();

            if ($sourceSummary) {
                $targetSummary = AssembleOrderSummaryModel::query()
                    ->where('assemble_order_id', $id)
                    ->first();

                $summaryData = $sourceSummary->toArray();
                unset($summaryData['id'], $summaryData['created_at'], $summaryData['updated_at']);
                $summaryData['assemble_order_id'] = $id;

                if ($targetSummary) {
                    $targetSummary->update($summaryData);
                } else {
                    AssembleOrderSummaryModel::query()->create($summaryData);
                }
            }

            // 7. 同步异常关联信息
            $sourceExceptionRelations = ExceptionRecordRelationModel::query()
                ->where('relate_type', ProductionCode::EXCEPTION_TYPE_ASSEMBLE_ORDER)
                ->where('relate_id', $sourceOrder->id)
                ->get();

            // 先删除目标订单的异常关联记录
            ExceptionRecordRelationModel::query()
                ->where('relate_type', ProductionCode::EXCEPTION_TYPE_ASSEMBLE_ORDER)
                ->where('relate_id', $id)
                ->delete();

            if ($sourceExceptionRelations && $sourceExceptionRelations->count() > 0) {
                // 遍历复制异常关联
                foreach ($sourceExceptionRelations as $relation) {
                    $relationData = [
                        'exception_record_id' => $relation->exception_record_id,
                        'relate_type' => ProductionCode::EXCEPTION_TYPE_ASSEMBLE_ORDER,
                        'relate_id' => $id,
                        'relate_product_code' => $relation->relate_product_code,
                        'relate_product_name' => $relation->relate_product_name,
                        'is_first_related' => 0, // 设置为非首次关联
                        'created_by' => $relation->created_by,
                    ];

                    ExceptionRecordRelationModel::query()->create($relationData);
                }
            }

            Db::commit();
            // 8. 发送通知到相关用户
            $noticeMsg = "订单已同步,请前往核对数据！\n数据来源：{$sourceOrder->code}";
            $params = make(AssembleOrderInfoService::class)->getCommonNoticeParams($targetOrder);
            $params['notice_msg'] = $noticeMsg;

            // 获取需要通知的订单用户
            $sendUser = make(AssembleOrderInfoService::class)->getOrderUser($id);

            // 发送通知
            make(NoticeService::class)->commonAssembleOrderNotice($sendUser, $params);

            return true;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }   
}