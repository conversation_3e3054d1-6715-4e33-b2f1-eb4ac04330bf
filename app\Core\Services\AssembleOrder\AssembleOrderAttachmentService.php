<?php

namespace App\Core\Services\AssembleOrder;

use App\Constants\AssembleOrderCode;
use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Core\Services\Notice\NoticeService;
use App\Exception\AppException;
use App\Model\TchipBi\AssembleOrderAttachmentModel;
use App\Model\TchipBi\AssembleOrderInfoModel;
use App\Model\TchipBi\AssembleOrderModel;
use App\Model\TchipBi\AttachmentModel;
use App\Model\TchipBi\CategoryModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\WorkStatusModel;
use Exception;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;


class   AssembleOrderAttachmentService extends BusinessService
{
    /**
     * @Inject()
     * @var AssembleOrderAttachmentModel
     */
    protected $model;

    public function getTreeList($assembleOrderId, $attachmentCategory)
    {
        if (empty($assembleOrderId) || empty($attachmentCategory)) {
            return [];
        }
        $categoryPid = CategoryModel::query()->where('keywords', $attachmentCategory)->where('type', AssembleOrderCode::ATTACHMENT_TYPE)->value('id');
        $category = CategoryModel::query()->where('pid', $categoryPid)->orderBy('sort', 'desc')->get();
        $category = $category ? $category->toArray() : [];
        foreach ($category as &$item) {
            $item['key'] = 'category_' . $item['id'];
            $item['tree_depth'] = 0;
            $item['file_type'] = $item['name'];
            $item['file_key'] = $item['keywords'];
            $item['assign_user_id'] = 0;
            $item['assign_audit_user_id'] = 0;
            $item['assign_user_arr'] = [];
            $item['attachment_remark'] = '';
            $children = $this->getAllList(['assemble_order_id'   => $assembleOrderId,
                                           'attachment_type' => $item['keywords']
            ], [], 'sort', 'desc');
//            foreach ($children as &$child) {
//                $child['key'] = 'list_' . $child['id'];
//                $child['tree_depth'] = 1;
//            }
            $item['children'] = $children;
        }
        return $category;
    }


    public function getAllList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        if (empty($filter['assemble_order_id'])) {
            return [];
        }
        $orderData = AssembleOrderModel::query()->where('id',$filter['assemble_order_id'])->first();
        $info = AssembleOrderInfoModel::query()->where('assemble_order_id', $filter['assemble_order_id'])->first();
        $attachmentType = empty($filter['attachment_type']) ? null : trim($filter['attachment_type']);
        unset($filter['attachment_type']);
        //根据上传类型获取分类
        if ($attachmentType) {
            $categoryPid = CategoryModel::where('keywords', $attachmentType)->where('type', AssembleOrderCode::ATTACHMENT_TYPE)->value('id');
            $filter['category_pid'] = $categoryPid;
        }
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 999);
        $result = $query->with('attachment')->orderBy($sort, $order)->get()->toArray();
        $userIds = collectFiledArrFromData($result, [
            'upload_user_id',
            'audit_user_id',
            'assign_audit_user_id',
        ]);
        $userList = $userIds ? UserModel::query()->whereIn('id', $userIds)->pluck('name', 'id')->toArray() : [];
        foreach ($result as &$item) {
            $item['key'] = 'list_' . $item['id'];
            $item['tree_depth'] = 1;
            $item['upload_user_name'] = $userList[$item['upload_user_id']] ?? null;
            $item['audit_user_name'] = $userList[$item['audit_user_id']] ?? null;
            $item['assign_audit_user_name'] = $userList[$item['assign_audit_user_id']] ?? null;
            $item['upload_time_text'] = $item['upload_time'] ? date('Y-m-d H:i', strtotime($item['upload_time'])) : null;
            $item['audit_time_text'] = $item['audit_time'] ? date('Y-m-d H:i', strtotime($item['audit_time'])) : null;
            $item['p_category'] = $attachmentType?:CategoryModel::query()->where('id', $item['category_pid'])->value('keywords');
            switch ($attachmentType){
                case AssembleOrderCode::ATTACHMENT_TYPE_FIRMWARE:
                    //软件审批人：软件负责人，业务员，指定审核人
                    $item['assign_user_arr'] = array_filter([
                        $info['software_user_id'] ?? 0,
                        $orderData['sales_user_id'] ?? 0,
                        $item['assign_audit_user_id']
                    ]);
                    break;
                case AssembleOrderCode::ATTACHMENT_TYPE_FIRST_ASSEMBLE:
                case AssembleOrderCode::ATTACHMENT_TYPE_PRODUCT_FINISH:
                $item['assign_user_arr'] = array_filter([
                    $info['assemble_user_id'] ?? 0,
                    $item['assign_audit_user_id']
                ]);
                    break;
                case AssembleOrderCode::ATTACHMENT_TYPE_FIRST_SOFT:
                case AssembleOrderCode::ATTACHMENT_TYPE_PRODUCT_SOFTWARE:
                $item['assign_user_arr'] = array_filter([
                    $info['software_user_id'] ?? 0,
                    $item['assign_audit_user_id']
                ]);
                    break;
                default:
                    $item['assign_user_arr'] = [];
            }
            $item['assign_user_arr'] = array_values($item['assign_user_arr']);
        }
        return $result;
    }

    public function doEdit(int $id, array $values)
    {
        Db::beginTransaction();
        try {
            //反改订单的状态信息
            $assignNoticeArr = [];
            if ($id === -3) {
                // 分类配置里进行了修改，需要批量更新
                $result = $this->model->where('category_id', $values['category_id'])->update($values);
            } elseif ($id > 0) {
                $hasChangeAudit = false;
                $sendWxNoticeData = [];
                $row = $this->model::query()->find($id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, 'No_results_were_found');
                }
                if (!empty($values['category_id']) && $row->category_id != $values['category_id']) {
                    //获取分类信息
                    $this->formatCategoryValues($values);
                }
                $info = AssembleOrderInfoModel::query()->where('assemble_order_id', $row['assemble_order_id'])->first();
                //重新上传后获取未上传文件只填写备注，审核状态改为未审核
                if ((isset($values['attachment_id']) && $values['attachment_id'] != $row->attachment_id) || (empty($values['attachment_id']) && isset($values['attachment_remark']) && $values['attachment_remark'] != $row->attachment_remark)) {
                    if ($row->audit_status == 2) {
                        throw new AppException(StatusCode::ERR_EXCEPTION, '文件已审核，不允许重新上传');
                    }
                    $values['audit_status'] = 1;
                    $values['audit_time'] = null;
                    $values['upload_user_id'] = auth()->id();
                    $values['upload_time'] = date('Y-m-d H:i:s');
                }
                //指定审核人邮件逻辑
                $assignNoticeArr = $this->handleAssignAuditNotice($values, $row);
                //处理审核时间和拒绝理由
                if (isset($values['audit_status']) && $values['audit_status'] != $row->audit_status) {
                    $values['audit_user_id'] = $values['audit_status'] == 0 ? 0 : auth()->id();
                    $values['audit_time'] = $values['audit_status'] == 1 ? null : date('Y-m-d H:i:s');
                    if ($values['audit_status'] == 1) {
                        $values['reject_reason'] = '';
                    } elseif ($values['audit_status'] == 3) {
                        $fileName = AttachmentModel::query()->where('id', $row['attachment_id'])->value('filename') . (!empty($values['reject_reason']) ? "({$values['reject_reason']})" : '');
                        $sendWxNoticeData = [
                            'file_name' => $fileName,
                            'notice_msg' => '您上传的以下文件已被驳回，请重新上传'
                        ];
                    }elseif ($values['audit_status'] == 2){
                        $fileName = AttachmentModel::query()->where('id', $row['attachment_id'])->value('filename');
                        $sendWxNoticeData = [
                            'file_name' => $fileName,
                            'notice_msg' => '您上传的以下文件已通过，请知晓'
                        ];
                    }
                    $hasChangeAudit = true;
                }

                $oldRow = $row->toArray();

                $result = $row->update($values);
                //判断资料状态是否需要修改
                $this->changeInfoDataStatus($oldRow, array_merge($values, ['id'=>$id]));
                if ($sendWxNoticeData) {
                    make(NoticeService::class)->rejectAssembleOrder($row->upload_user_id, $row->assemble_order_id, $sendWxNoticeData);
                }
            } else {

                //获取分类信息
                $this->formatCategoryValues($values);
                if (empty($values['upload_user_id'])) {
                    $values['upload_user_id'] = auth()->id();
                    $values['upload_time'] = date('Y-m-d H:i:s');
                }
                $result = $this->model::query()->create($values);
                //指派审核人逻辑
                $assignNoticeArr = $this->handleAssignAuditNotice($values);
                //新增时，判断资料状态是否需要修改
                $this->changeInfoDataStatus([], $values);
            }
            Db::commit();
            //微信推送
            foreach ($assignNoticeArr as $notice) {
                make(NoticeService::class)->commonAssembleOrderNotice($notice['user_id'], $notice);
            }

            return $result;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 指定审核人通知
     * @param $values
     * @param $row
     * @return array
     */
    public function handleAssignAuditNotice($values, $row = null)
    {
        $notice = [];
        // 判断是否有$row，如果有$row则优先使用$row中的数据
        $attachmentId = $row ? $row->attachment_id : $values['attachment_id'];
        $categoryPid = $row ? $row->category_pid : $values['category_pid'];  // 使用正确的逻辑
        $categoryId = $row ? $row->category_id : $values['category_id'];
        $assembleOrderId = $row ? $row->assemble_order_id : $values['assemble_order_id'];

        // 如果传入$row，表示修改操作
        if ($row) {
            // 检查是否需要变更审核人
            if (isset($values['assign_audit_user_id']) && $values['assign_audit_user_id'] != $row->assign_audit_user_id) {
                if ($row->audit_status == 2) {
                    throw new AppException(StatusCode::ERR_EXCEPTION, '文件已经审核通过，无需变更审核人');
                }
                if ($row->assign_audit_user_id) {
                    //变更后通知原指派人
                    $notice[] = [
                        'user_id'    => $row->assign_audit_user_id,
                        'notice_msg' => "【%s】的【%s】分类中的文件已取消指派给您，请知晓！\n文件名称：%s",
                    ];
                }
                if ($values['assign_audit_user_id']) {
                    $notice[] = [
                        'user_id'    => $values['assign_audit_user_id'],
                        'notice_msg' => "【%s】的【%s】分类中的文件已指派给您审核，请及时处理！\n文件名称：%s",
                    ];
                }
            }
        } else {
            // 如果没有传入$row，表示新指派
            if (!empty($values['assign_audit_user_id'])) {
                $notice[] = [
                    'user_id'    => $values['assign_audit_user_id'],
                    'notice_msg' => "【%s】的【%s】分类中的文件已指派给您审核，请及时处理！\n文件名称：%s",
                ];
            }
        }
        if ($notice) {
            // 获取文件名称和分类名称
            $fileName = AttachmentModel::query()->where('id', $attachmentId)->value('filename') ?: '';
            $topCategory = $this->model->getChangeCategory($categoryPid) ?: '';
            $topCategory = AssembleOrderCode::LOG_CHANGE_CATEGORY_ARR[$topCategory] ?? '';
            $categoryName = CategoryModel::query()->where('id', $categoryId)->value('name') ?: '';
            $order = AssembleOrderModel::find($assembleOrderId);
            $params = make(AssembleOrderInfoService::class)->getCommonNoticeParams($order);
            $result = [];
            foreach ($notice as $item) {
                $item['notice_msg'] = sprintf($item['notice_msg'], $topCategory, $categoryName, $fileName);
                $result[] = array_merge($item, $params);
            }
            return $result;
        }
        return [];
    }

    /**
     * 文件编辑或审核时会回滚资料状态
     * 如果资料状态是审核完成状态，再次对文件审核就会回滚到已上传状态
     * 如果文件不是待上传状态，对文件编辑会回滚到待上传状态
     * @param $row
     * @param $values
     * @return void
     */
    public function changeInfoDataStatus($row,$values)
    {
        if(empty($values['category_type']))return;
        $this->getCategoryStatusFiled($row,$values);
        $statusFiled = $values['category_type'].'_status';
        $assembleOrderId = $row['assemble_order_id']??$values['assemble_order_id']??0;
        $info = AssembleOrderInfoModel::query()->where('assemble_order_id', $assembleOrderId)->first();
        if(!$info)return;
        //文件发生编辑，如果状态不是待上传状态，则修改为待上传状态
        if($row){
            $hasEdit = (isset($values['attachment_id']) && $values['attachment_id']!= $row['attachment_id']) ||
                (isset($values['category_id']) && $values['category_id']!= $row['category_id']) ||
                (isset($values['attachment_remark']) && $values['attachment_remark']!= $row['attachment_remark']);
            //文件发生审核，如果状态是已完成，则修改为已上传状态
            $hasAudit = isset($values['audit_status']) && $values['audit_status']!= $row['audit_status'];
            //文件发生指派
            $hasAssign = isset($values['assign_audit_user_id']) && $values['assign_audit_user_id'] != $row['assign_audit_user_id'];
        }else{
            $hasEdit = true;
            $hasAudit = false;
            $hasAssign = $values['assign_audit_user_id'] ? 1 : 0;
        }

        if($hasEdit && $info[$statusFiled] != AssembleOrderCode::DATA_STATUS_WAIT){
            $infoValue = [
                $statusFiled => AssembleOrderCode::DATA_STATUS_WAIT,
                'assemble_order_id' => $assembleOrderId
            ];
        } elseif ($hasAssign && ($values['audit_status']??$row['audit_status']??0) == AssembleOrderCode::ATTACHMENT_AUDIT_WAITING && $info[$statusFiled] == AssembleOrderCode::DATA_STATUS_FINISHED) {
            //文件未审核指派审核人时，如果是已完成审核状态，则修改为为已上传未审核状态
            //适用于常规备货的软件资料场景
            $infoValue = [
                $statusFiled => AssembleOrderCode::DATA_STATUS_UPLOADED,
                'assemble_order_id' => $assembleOrderId
            ];
        }elseif($hasAudit){
            switch (true){
                case $values['audit_status'] == 1 && $info[$statusFiled] == AssembleOrderCode::DATA_STATUS_FINISHED:
                case $values['audit_status'] == 3 && $info[$statusFiled] == AssembleOrderCode::DATA_STATUS_FINISHED:
                    $infoValue = [
                        $statusFiled => AssembleOrderCode::DATA_STATUS_UPLOADED,
                        'assemble_order_id' => $assembleOrderId
                    ];
                    break;
                case $values['audit_status'] == 3 && $info[$statusFiled] != AssembleOrderCode::DATA_STATUS_WAIT:
                    $infoValue = [
                        $statusFiled => AssembleOrderCode::DATA_STATUS_WAIT,
                        'assemble_order_id' => $assembleOrderId
                    ];
                    break;
                default:
                    break;
            }
        }
        if(!empty($infoValue)) {
            make(AssembleOrderInfoService::class)->doEdit(0, $infoValue);
        }
    }

    /**
     * 批量编辑
     * @param $ids
     * @param $params
     * @return bool
     */
    public function doMulti($ids, $params): bool
    {
        if (empty($ids) || empty($params)) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
        }
        if (!is_array($ids)) {
            $ids = explode(',', $ids);
        }
        if (!is_array($params)) {
            parse_str($params, $params);
        }
        $count = 0;
        Db::beginTransaction();
        try {
            $hasChangeAudit = false;
            $sendWxUserData = [];
            $items = $this->model::query()->whereIn('id', $ids)->get();
            $assembleOrderId = $items[0]->assemble_order_id;
            $info = AssembleOrderInfoModel::query()->where('assemble_order_id', $assembleOrderId)->first();
            $infoWorkStatusKey = WorkStatusModel::query()->where('id', $info['work_status_id'] ?? 0)->value('key') ?: '';
            foreach ($items as $item) {
                $values = $params;
                //批量修改审核状态的只有状态不相同的才会修改
                if (isset($values['audit_status']) && $values['audit_status'] != $item->audit_status) {
                    $values['audit_user_id'] = $values['audit_status'] == 1 ? 0 : auth()->id();
                    $values['audit_time'] = $values['audit_status'] == 1 ? null : date('Y-m-d H:i:s');
                    if ($values['audit_status'] != 3) {
                        $values['reject_reason'] = '';
                        if($values['audit_status'] == 2){
                            $fileName = AttachmentModel::query()->where('id', $item->attachment_id)->value('filename') ;
                            $sendWxUserData[$item->upload_user_id][] = $fileName;
                        }
                    } else {
                        $fileName = AttachmentModel::query()->where('id', $item->attachment_id)->value('filename') . ($values['reject_reason'] ? "({$values['reject_reason']})" : '');
                        $sendWxUserData[$item->upload_user_id][] = $fileName;
                    }
                    //修改资料状态
                    $this->changeInfoDataStatus($item, $values);
                    $result = $item->update($values);
                    $result && $hasChangeAudit = true;
                    $count += $result;
                } else {
                    $count += $item->update($values);
                }
            }

            $notice_msg = $values['audit_status'] == 2? '您上传的以下文件已通过，请知晓' : '您上传的以下文件已被驳回，请重新上传';
            foreach ($sendWxUserData as $userId => $fileData) {
                $fileName = implode("\n", $fileData);
                $noticeParams = [
                    'file_name' => $fileName,
                    'notice_msg' => $notice_msg
                ];
                make(NoticeService::class)->rejectAssembleOrder($userId, $assembleOrderId, $noticeParams);
            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
        if ($count > 0) {
            return true;
        } else {
            throw new AppException(StatusCode::ERR_SERVER, __('common.No_rows_were_updated'));
        }
    }

    /**
     * 格式化附件数据,按分类来管理
     * @return void
     */
    public function formatAttachmentData()
    {
        //以前的文件类型修改为分类
        $commonFirm = CategoryModel::query()->where('type', AssembleOrderCode::ATTACHMENT_TYPE)->where('keywords', AssembleOrderCode::ATTACHMENT_TYPE_FIRMWARE_COMMON)->first();
        if ($commonFirm) {
            $this->model::query()->where('type', 1)->update([
                'category_id'  => $commonFirm['id'],
                'category_pid' => $commonFirm['pid'],
                'sort'         => $commonFirm['sort'],
                'file_type'    => $commonFirm['name'],
                'file_key'     => $commonFirm['keywords'],
            ]);
        }
        $customerFirm = CategoryModel::query()->where('type', AssembleOrderCode::ATTACHMENT_TYPE)->where('keywords', AssembleOrderCode::ATTACHMENT_TYPE_FIRMWARE_CUSTOMER)->first();
        if ($customerFirm) {
            $this->model::query()->where('type', 2)->update([
                'category_id'  => $customerFirm['id'],
                'category_pid' => $customerFirm['pid'],
                'sort'         => $customerFirm['sort'],
                'file_type'    => $customerFirm['name'],
                'file_key'     => $customerFirm['keywords'],
            ]);
        }
    }

    public function formatCategoryValues(&$values)
    {
        $category = CategoryModel::query()->where('id', $values['category_id'])->first();
        if (!$category) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
        }
        $values['category_pid'] = $category['pid'];
        $values['sort'] = $category['sort'];
        $values['file_type'] = $category['name'];
        $values['file_key'] = $category['keywords'];
    }

    /**
     * 删除
     * @param $ids
     * @param array $params
     * @return int
     */
    public function doDelete($ids,array $values = []): int
    {
        Db::beginTransaction();
        try {
            $ids = explode(',', $ids);
            if(!empty($values['category_type'])){
                $row = $this->model::query()->whereIn('id',$ids)->first();
                if(!$row) return false;
                $this->getCategoryStatusFiled($row->toArray(),$values);
                $infoValues = [
                    $values['category_type'].'_status' => AssembleOrderCode::DATA_STATUS_WAIT,
                    'assemble_order_id' => $row['assemble_order_id']??0
                ];
                make(AssembleOrderInfoService::class)->doEdit(0,$infoValues);
            }

            $result =  $this->model::destroy($ids);
            Db::commit();
            return $result;
        }catch (Exception $e){
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER,$e->getMessage());
        }
    }

    public function getCategoryStatusFiled($row,&$values)
    {
        if($values['category_type'] == 'first_data'){
            $categoryPid = $row['category_pid']??$values['category_pid']??0;
            $pCategory = CategoryModel::query()->where('id',$categoryPid)->value('keywords');
            if($pCategory === AssembleOrderCode::ATTACHMENT_TYPE_FIRST_SOFT){
                $values['category_type'] = 'first_soft_data';
            }elseif($pCategory === AssembleOrderCode::ATTACHMENT_TYPE_FIRST_ASSEMBLE){
                $values['category_type'] = 'first_assemble_data';
            }
        }elseif ($values['category_type'] == 'product_data'){
            $categoryPid = $row['category_pid']??$values['category_pid']??0;
            $pCategory = CategoryModel::query()->where('id',$categoryPid)->value('keywords');
            if($pCategory === AssembleOrderCode::ATTACHMENT_TYPE_PRODUCT_FINISH){
                $values['category_type'] = 'product_finished_data';
            }elseif($pCategory === AssembleOrderCode::ATTACHMENT_TYPE_PRODUCT_SOFTWARE){
                $values['category_type'] = 'product_soft_data';
            }
        }
    }

    /**
     * 检查是否有上传客户文件
     * @param $orderId
     * @return int
     */
    public function checkHasCustomer($orderId)
    {
        return $this->model::query()->where('assemble_order_id',$orderId)->where('file_key',AssembleOrderCode::ATTACHMENT_TYPE_FIRMWARE_CUSTOMER)->count();
    }

    /**
     * 检查软件资料有没有审核完成
     * @param $orderId
     * @return int
     */
    public function checkCategoryHasAudit($orderId, $categoryKeyWords)
    {
        $orderInfo = AssembleOrderInfoModel::query()->where('assemble_order_id', $orderId)->first();
        if (!$orderInfo) {
            return false;
        }

        $pCategory = CategoryModel::query()->where('keywords', $categoryKeyWords)->value('id');
        $attachment = $this->model::query()->where('assemble_order_id', $orderInfo['assemble_order_id'])->where('category_pid', $pCategory)->get();
        $attachment = $attachment ? $attachment->toArray() : [];
        //软件资料：常规备货，指派了审核人才需要审核
        //其他类别：全部审核完成
        if ($categoryKeyWords == AssembleOrderCode::ATTACHMENT_TYPE_FIRMWARE && $orderInfo['order_type'] == AssembleOrderCode::ORDER_TYPE_COMMON) {
            $finishedAudit = !array_filter($attachment, function ($item) {
                return $item['assign_audit_user_id'] > 0 && $item['audit_status'] != AssembleOrderCode::ATTACHMENT_AUDIT_PASS;
            });
        } else {
            $finishedAudit = !array_filter($attachment, function ($item) {
                return $item['audit_status'] != AssembleOrderCode::ATTACHMENT_AUDIT_PASS;
            });
        }
        return $finishedAudit;

    }
}