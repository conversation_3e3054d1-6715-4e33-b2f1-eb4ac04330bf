<?php

namespace App\Controller\TestPlan;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\TestPlan\TestPlanService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("测试计划")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class PlanController extends BaseController
{
    /**
     * @var TestPlanService
     * @Inject()
     */
    protected $service;

    public function conf()
    {
        $result = $this->service->conf();
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("版本信息")
     * @Middleware(AuthMiddleware::class)
     */
    public function getVersionList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getVersionList($filter, $op, $sort, $order);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("用例树")
     * @Middleware(AuthMiddleware::class)
     */
    public function getProjectCaseTree()
    {
        $projectId = $this->request->input('project_id');
        $result = $this->service->getProjectCaseTree($projectId);

        return $this->response->success($result);
    }

    /**
     * 获取未绑定版本的数量
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function getNoVersionCount()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getNoVersionCount($filter, $op, $sort, $order);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("添加用例")
     * @Middleware(AuthMiddleware::class)
     */
    public function addCase()
    {
        $id = (int) $this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->addCase($id, $params);
        return $this->response->success($result);
    }


    public function getTestResult()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getTestResult($filter, $op, $sort, $order, (int) $limit);
        return $this->response->success($result);
    }
}
