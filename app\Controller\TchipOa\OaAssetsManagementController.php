<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\OaAssetsManagementService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("固定资产管理")
 * @AutoController
 * @Middleware(AuthMiddleware::class)
 */
class OaAssetsManagementController extends \App\Controller\BaseController
{
    /**
     * @Inject
     * @var OaAssetsManagementService
     */
    protected $service;

    public function getAssetStatusList(): \Psr\Http\Message\ResponseInterface
    {
        $result = $this->service->getAssetStatusList();
        return $this->response->success($result);
    }

    public function getOverView(): \Psr\Http\Message\ResponseInterface
    {
        $id = $this->request->input('id');
        $overView = $this->service->getOverView($id);
        return $this->response->success($overView);
    }

    public function overViewOneDetails(): \Psr\Http\Message\ResponseInterface
    {
        $id = $this->request->input('id');
        $overViewOne = $this->service->getOverViewOneDetails($id);
        return $this->response->success($overViewOne);
    }

    public function oneRecordDetails(): \Psr\Http\Message\ResponseInterface
    {
        $id = $this->request->input('id');
        $dId = $this->request->input('d_id');
        $overViewOne = $this->service->getOneRecordDetails($id, $dId);
        return $this->response->success($overViewOne);
    }

    public function getAssetCountData(): \Psr\Http\Message\ResponseInterface
    {
        $a_name = $this->request->input('asset_name');
//        $dId = $this->request->input('d_id');
        $overViewOne = $this->service->getAssetCountData($a_name);
        return $this->response->success($overViewOne);
    }

    public function getRecordsList(): \Psr\Http\Message\ResponseInterface
    {
        $data = $this->request->all();
        $records = $this->service->getRecordsList($data);
        return $this->response->success($records);
    }

    public function getCategoryList()
    {
        $data = $this->request->all();
        $category = $this->service->getCategoryList($data);
        return $this->response->success($category);
    }

    public function getAllAssetCount(array $filter = [], array $op = [], string $sort = 'name', string $order = 'DESC', int $limit = 10, string $pageSize = '10')
    {
        $filter = $this->request->input('filter', $filter);
        $pageSize = $this->request->input('pageSize', $pageSize);
        $sort = $this->request->input('sort', $sort);
        $order = $this->request->input('order', $order);
        $allCount = $this->service->getAllAssetCount($filter, $op, $sort, $order, $limit, $pageSize);
        return $this->response->success($allCount);
    }

    public function getReportDetails(array $filter = [])
    {
        $filter = $this->request->input('filter', $filter);
        $reportDetails = $this->service->getReportDetails($filter);
        return $this->response->success($reportDetails);
    }

    public function doEditRecord()
    {
        $data = $this->request->all();
        $this->service->doEditRecord($data);
        return $this->response->success();
    }

    public function receiveConfirm()
    {
        $data = $this->request->all();
        $this->service->receiveConfirm($data);
        return $this->response->success();
    }

}
