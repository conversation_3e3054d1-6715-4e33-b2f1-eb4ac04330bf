<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/13 下午5:21
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing;

use App\Constants\DataBaseCode;
use App\Model\StationPCManager\CategoryModel;
use App\Model\StationPCManager\DeviceModel;
use App\Model\StationPCManager\MarketingChannelModel;
use App\Model\StationPCManager\MarketingChannelUserModel;
use App\Model\StationPCManager\MarketingContentModel;
use Carbon\Carbon;
use Hyperf\Cache\Annotation\Cacheable;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Collection;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use App\Core\Utils\TimeUtils;

class IndexService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var RequestInterface
     */
    protected $request;

    /**
     * @Inject()
     * @var MarketingChannelModel
     */
    protected $managerMarketingChannelModel;

    /**
     * @Inject()
     * @var MarketingChannelUserModel
     */
    protected $managerMarketingChannelUserModel;

    /**
     * @Inject()
     * @var MarketingContentModel
     */
    protected $managerMarketingContentModel;

    /**
     * @Inject()
     * @var DeviceModel
     */
    protected $managerDeviceModel;

    /**
     * 用户数
     * @Cacheable(prefix="marketing/stationpc/topCardUser", ttl=86400)
     * @return string
     */
    public function topCardUser(): string
    {
        // @Cacheable(prefix="marketing/stationpc/topCardUser", ttl=86400)
        $userCount = $this->managerMarketingChannelModel::query()->where('is_official', 1)->sum('user_count');
        return number_format($userCount);
    }

    /**
     * 本月新增用户
     * @Cacheable(prefix="marketing/stationpc/topCardUserMonth", ttl=86400, listener="marketing-topcard-user-month-update")
     * @return string
     */
    public function topCardUserMonth(): string
    {

        // * @Cacheable(prefix="marketing/stationpc/topCardUserMonth", ttl=86400, listener="marketing-topcard-user-month-update")

        // $preMonth = Carbon::now()->subMonth()->format('Y-m');
        //
        // $ids = $this->managerMarketingChannelModel::query()->where('is_official', 1)->pluck('id')->toArray();
        // $updateTimeIn = $this->managerMarketingChannelUserModel::query()
        //     ->whereIn('channel_id', $ids)
        //     ->whereRaw("from_unixtime(`updatetime`, '%Y-%m') = '" . $preMonth . "'")
        //     ->groupBy(Db::raw('from_unixtime(updatetime, \'%Y-%m\')'), 'channel_id')
        //     ->selectRaw('MAX(updatetime) AS updatetime_max');
        //
        // $preUserCount = $this->managerMarketingChannelUserModel::query()->whereIn('channel_id', $ids)
        //     ->whereIn('updatetime', $updateTimeIn)
        //     ->sum('user_count');
        //
        // $userCount = $this->managerMarketingChannelModel::query()->where('is_official', 1)->sum('user_count');

        $beginDate = strtotime(date('Y-m-01'));
        $increase = $this->managerMarketingChannelUserModel::query()->whereExists(function ($query) {
            $query->select()->from('marketing_channel')->whereRaw('ma_marketing_channel.id=ma_marketing_channel_user.channel_id')
                ->whereRaw('ma_marketing_channel.is_official=1');
        })->where('createtime', '>', $beginDate)->sum('user_increase');
        return number_format($increase);
    }

    /**
     * 用户列表
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return Builder[]|Collection
     */
    public function getChannelList($filter, $op, $sort, $order, $limit)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $this->managerMarketingChannelModel::query());

        $userItems = $query->with(['platform'])->orderBy($sort, $order)->paginate($limit);
        $userCountSum = $query->limit($limit)->sum('user_count');

        foreach ($userItems as &$userItem) {
            $userItem->rate = sprintf('%.2f', $userItem->user_count / $userCountSum) * 100 . '%';
            $userCountPre = $this->managerMarketingChannelUserModel::query()->where('channel_id', $userItem['id'])
                ->whereRaw("from_unixtime(`updatetime`, '%m') = '" . sprintf('%02d', date('m') - 1) . "'")
                ->latest('updatetime')->value('user_count');
            $userItem->month_add = number_format($userItem->user_count - $userCountPre);
        }

        return $userItems;
    }

    /**
     * 推广内容
     * @Cacheable(prefix="marketing/stationpc/topCardContentCount", ttl=86400)
     * @return int
     */
    public function topCardContentCount()
    {
        $userCount = $this->managerMarketingContentModel::query()->count();
        return number_format($userCount);
    }

    /**
     * 本月新增推广内容
     * @Cacheable(prefix="marketing/stationpc/topCardContentCountMonth", ttl=86400)
     * @return int
     */
    public function topCardContentCountMonth()
    {
        $userCount = $this->managerMarketingContentModel::query()->whereRaw("from_unixtime(`createtime`, '%m') = '" . date('m') . "'")
            ->count();
        return number_format($userCount);
    }

    /**
     * 推广内容列表
     * @return Builder[]|Collection
     */
    public function getContentList($filter, $op, $sort, $order, $limit)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $this->managerMarketingContentModel::query());
        // list($query, $limit, $sort, $order) = $this->buildparams($this->managerMarketingContentModel::query());

        $items = $query->with(['platform', 'channel'])->orderBy($sort, $order)->paginate($limit);


        return $items;
    }

    /**
     * 推广内容分布比例
     * @return array
     */
    public function getContentRatio($filter, $op, $sort, $order, $limit)
    {
        $ratioRadio = $this->request->input('ratio_radio');
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $this->managerMarketingContentModel::query());
        $result['item'] = $query->select('platform_id', 'channel_id')->selectRaw('SUM(exposure) as sum_exposure')
            ->with(['platform', 'channel'])->groupBy($ratioRadio)->get();
        foreach ($result['item'] as $item) {
            $result['sum_exposure'] += $item['sum_exposure'] ?? 0;
        }
        $result['item'] = collect($result['item'])->sortByDesc('sum_exposure')->slice(0, 8)->values()->all();
        return $result;
    }

    /**
     * 曝光量
     * @Cacheable(prefix="marketing/stationpc/topCardContentExposure", ttl=86400)
     * @return string
     */
    public function topCardContentExposure()
    {
        $userCount = $this->managerMarketingContentModel::query()->sum('exposure');
        return number_format($userCount);
    }

    /**
     * 本月新增曝光量
     * @Cacheable(prefix="marketing/stationpc/topCardContentExposureMonth", ttl=86400)
     * @return int|mixed|string|void
     */
    public function topCardContentExposureMonth()
    {
        $exposure = $this->managerMarketingContentModel::query()->whereBetween('updatetime', [strtotime(date('Y-m-01 00:00:00')), time()])
            ->sum('exposure');
        return number_format($exposure);
    }

    /**
     * 设备数
     * @Cacheable(prefix="marketing/stationpc/topCardDevice", ttl=86400)
     * @return string
     */
    public function topCardDevice()
    {
        $deviceCount = $this->managerDeviceModel::query()->count();
        $deviceCountEn = $this->managerDeviceModel::on(DataBaseCode::STATIONPC_MANAGEREN)->count();
        return number_format(($deviceCount + $deviceCountEn));
    }

    /**
     * 本月新增设备
     * @Cacheable(prefix="marketing/stationpc/topCardDeviceMonthAdd", ttl=86400)
     * @return int
     */
    public function topCardDeviceMonthAdd()
    {
        $deviceCount = $this->managerDeviceModel::query()->whereRaw("from_unixtime(`createtime`, '%Y-%m') = '" . date('Y-m') . "'")->count();
        $deviceCountEn = $this->managerDeviceModel::on(DataBaseCode::STATIONPC_MANAGEREN)
            ->whereRaw("from_unixtime(`createtime`, '%Y-%m') = '" . date('Y-m') . "'")->count();
        return number_format(($deviceCount + $deviceCountEn));
    }

    /**
     * 曝光量趋势-近半年
     * @return array
     */
    public function getExposureTrend()
    {
        $startTime = time() - 3600 * 24 * 30 * 6;
        $endTime = time();

        for ($i = 0; $i < 6; $i++) {
            $ym = date('Y.m', $endTime);

            $result['zh'][] = $this->_getContentTrend($ym, 'zh_cn');
            $result['en'][] = $this->_getContentTrend($ym, 'en_us');

            $endTime = $endTime - 3600 * 24 * 30;
        }

        $result['zh'] = collect($result['zh'])->sortBy('ym')->values()->all();
        $result['en'] = collect($result['en'])->sortBy('ym')->values()->all();

        return $result;

    }

    /**
     * 用户增长趋势数据
     * @param $filter
     * @param $op
     * @param string $type month-按月，day-按日
     * @return array
     */
    public function userIncreaseTrend($filter, $op, $type = 'month')
    {
        if (!empty($filter['updatetime'])) {
            $time = array_slice(explode('-', $filter['updatetime']), 0, 2);;
            $startTime = $time[0];
            $endTime = $time[1];
            unset($filter['updatetime']);
        } else {
            $time = time();
            $startTime = strtotime('-6 month', $time);
            $endTime = $time;
        }

        $startTime = strtotime(date('Y-m-d', (int)$startTime));
        $endTime = strtotime(date('Y-m-d 23:59:59', (int)$endTime));
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, '', '', '', MarketingChannelModel::query());
        $channelItems = $query->with(['platform'])->get();

        $channelUserIncreaseitems = $this->managerMarketingChannelUserModel::query()
            ->selectRaw('id, channel_id, user_count, from_unixtime(updatetime, \'%Y-%m\') as month, from_unixtime(updatetime, \'%Y-%m-%d\') as day, user_increase, updatetime')
            ->whereIn('channel_id', $query->pluck('id'))
            ->whereBetween('updatetime', [$startTime, $endTime])
            ->orderBy('updatetime', 'asc')
            ->get();

        $timeNum = TimeUtils::getTimeNum(date('Y-m-d', $startTime), date('Y-m-d', $endTime), $type);
        $monthData = [];
        foreach ($channelItems as &$channelItem) {
            for ($i = 0; $i <= $timeNum; $i++) {
                if($type === 'month'){
                    $currentDate = date('Y-m', strtotime("+$i month", $startTime));
                }else{
                    $currentDate = date('Y-m-d', strtotime("+$i day", $startTime));
                }
                $currentUserIncrease = $channelUserIncreaseitems->where('channel_id', $channelItem['id'])
                    ->where($type,  $currentDate)->sum('user_increase');
                $monthData[$i] = [
                    'dated_at'      => $currentDate,
                    'user_increase' => $currentUserIncrease
                ];
            }
            $channelItem['data'] = $monthData;
        }
        return $channelItems;
    }

    private function _getContentTrend($ym, $keywords)
    {
        $item = $this->managerMarketingContentModel::query()
            ->whereHas('platform', function ($query) use ($keywords) {
                $query->where('keywords', $keywords);
            })
            ->selectRaw('from_unixtime(updatetime, \'%Y.%m\') as ym, sum(exposure) as sum_exposure, count(*) as content_count')
            ->whereRaw('from_unixtime(updatetime, \'%Y.%m\') = ' . $ym)
            ->groupBy('ym')->first();
        if (empty($item)) {
            $item = ['ym' => $ym, 'sum_exposure' => 0, 'content_count' => 0];
        }
        $item['channel_count'] = $this->_getChannelCount($ym);
        return $item;
    }

    private function _getChannelCount($ym)
    {
        $item = $this->managerMarketingContentModel::query()
            ->selectRaw('count(distinct channel_id) as channel_count')
            ->whereRaw('from_unixtime(updatetime, \'%Y.%m\') = ' . $ym)->first();
        if (empty($item)) {
            return 0;
        }
        return $item['channel_count'];
    }
}