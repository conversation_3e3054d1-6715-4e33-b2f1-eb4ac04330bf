<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/17 上午11:13
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\WorkWx;

use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Services\WorkWx\WorkWxMessageService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * 企业微信-消息推送
 * @AutoController()
 * @WorkWxTokenAnnotation(type="agent_tchip_bi")
 */
class MessageController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var WorkWxMessageService
     */
    protected $workWxMessageService;

    /**
     * 发送文本消息
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function sendText()
    {
        $content = $this->request->input('content');
        $toUser = $this->request->input('to_user', '');
        $toParty = $this->request->input('to_party','');
        $toTag = $this->request->input('to_tag', '');
        $result = $this->workWxMessageService->sendText($content, $toUser, $toParty, $toTag);
        return $this->response->success($result);
    }
}