<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/19 下午5:11
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Stationpcbbs;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Bbs\MemberService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @ControllerNameAnnotation("StationPC成员管理")
 * @AutoController()
 */
class MemberController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var MemberService
     */
    protected $stationpcBbsMemberService;

    public function getMemberList()
    {
        $result = $this->stationpcBbsMemberService->getMemberList();
        return $this->response->success($result);
    }

    public function updateMember()
    {
        $result = $this->stationpcBbsMemberService->updateMember();
        return $this->response->success($result);
    }
}
