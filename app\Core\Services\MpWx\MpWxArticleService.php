<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/18 下午2:17
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\MpWx;

use App\Constants\AutoUserCountCode;

class MpWxArticleService extends MpWxBaseService
{
    /**
     * 获取图文群发每日数据
     * @return mixed
     */
    public function getArticleSummary($mp='stationpc')
    {
        $response = $this->sendRequest('datacube/getarticlesummary', [
            'query' => [
                'access_token' => getCache($this->mpList($mp)['redis_key']),
            ],
            'json' => [
                'begin_date' => '2022-05-10',
                'end_date' => '2022-05-10'
            ]
        ],'post');
        return $response;
    }

    /**
     * 获取图文群发总数据
     * @return mixed
     */
    public function getArticleTotal($mp='stationpc')
    {
        $response = $this->sendRequest('datacube/getarticletotal', [
            'query' => [
                'access_token' => getCache($this->mpList($mp)['redis_key']),
            ],
            'json' => [
                'begin_date' => '2022-05-17',
                'end_date' => '2022-05-17'
            ]
        ],'post');
        return $response;
    }

    /**
     * 获取图文统计数据
     * @return mixed
     */
    public function getUserRead($mp='stationpc')
    {
        $response = $this->sendRequest('datacube/getuserread', [
            'query' => [
                'access_token' => getCache($this->mpList($mp)['redis_key']),
            ],
            'json' => [
                'begin_date' => '2022-05-10',
                'end_date' => '2022-05-12'
            ]
        ],'post');
        return $response;
    }
}