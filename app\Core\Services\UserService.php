<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 上午10:39
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services;

use App\Annotation\WorkWxTokenAnnotation;
use App\Constants\DataBaseCode;
use App\Constants\StatusCode;
use App\Core\Services\Redmine\AccountService;
use App\Core\Services\Setting\AuthMenuService;
use App\Core\Services\WorkWx\WorkWxUserService;
use App\Core\Utils\Log;
use App\Core\Utils\Random;
use App\Core\Utils\TimeUtils;
use App\Event\UserLogged;
use App\Exception\AppException;
use App\Model\Redmine\EmailAddressModel;
use App\Model\Redmine\UserModel as RedmineUserModel;
use App\Model\TchipBi\AuthGroupModel;
use App\Model\TchipBi\OaUserFilesModel;
use App\Model\TchipBi\UserDepartmentModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\UserThirdModel;
use Carbon\Carbon;
use Hyperf\Context\Context;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\SimpleCache\CacheInterface;
use Psr\SimpleCache\InvalidArgumentException;
use Qbhy\HyperfAuth\AuthManager;
use Tightenco\Collect\Support\HigherOrderTapProxy;

/**
 * 用户操作服务类
 */
class UserService extends BaseService
{
    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * @Inject()
     * @var UserModel
     */
    protected $userModel;

    /**
     * @Inject()
     * @var AccountService
     */
    protected $accountService;

    /**
     * @Inject()
     * @var EventDispatcherInterface
     */
    protected $eventDispatcher;

    /**
     * @Inject()
     * @var AuthService
     */
    protected $authService;

    /**
     * @Inject
     * @var CacheInterface
     */
    private $cache;



    // 忘记密码令牌有效期（30分钟）
    private const FORGET_PASSWORD_TOKEN_EXPIRE_TIME = 1800;
    
    // 缓存键前缀
    private const FORGET_PASSWORD_CACHE_PREFIX = 'forget_password:';
    private const FORGET_PASSWORD_EMAIL_CACHE_PREFIX = 'forget_password_email:';


    public function login($username, $password)
    {
        $user = $this->userModel::query()->where(function ($query) use ($username) {
            $query->where('email', $username)->orWhere('biz_mail', $username);
        })->where('status', 1)->where('bi_status', 1)->first();
        if (!$user) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '用户不存在');
        }
        // if ($user->must_change_password === 0) {
        if (makePassword($password, $user->salt) != $user->password) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '密码错误');
        }
        // }
        $this->eventDispatcher->dispatch(new UserLogged($user));
        // 更新用户最后登录时间
        $user->last_login_time =  Carbon::now();
        $user->save();
        return [
            'token' => $this->auth->login($user),
            'default_index' => !empty($user->group_access_ids) ? make(AuthMenuService::class)->roleDefaultMenu($user->group_access_ids) : ''
        ];
    }

    /**
     * @WorkWxTokenAnnotation(type="agent_tchip_bi")
     * @param $code
     * @return mixed
     */
    public function wxlogin($code)
    {
        $wxuserId = make(WorkWxUserService::class)->getUserIdByCode($code);
        if ($wxuserId) {
            $user = $this->userModel::query()->where('workwx_userid', $wxuserId)->where('status', 1)->first();
            if ($user) {
                $this->eventDispatcher->dispatch(new UserLogged($user));
                return [
                    'token' => $this->auth->login($user),
                    'default_index' => !empty($user->group_access_ids) ? make(AuthMenuService::class)->roleDefaultMenu($user->group_access_ids) : ''
                ];
            }
        }
        if (!$user) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '用户不存在');
        }
    }

    public function logout()
    {
        return $this->auth->logout();
    }

    /**
     * 用户信息
     * @return array
     */
    public function getUserInfo($user_id = null, $isThirdUid = true)
    {
        if ($user_id && $isThirdUid) {
            $user_id = make(\App\Model\TchipBi\UserThirdModel::class)::query()->where('third_user_id', $user_id)->value('user_id');
        }

        $userInfo = $this->userModel::query()->with(['userThird', 'groupsAccess'])->find($user_id ?? $this->auth->user()->getId());
        if (!$userInfo) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '用户不存在');
        }
        $userArr = $userInfo->toArray();
        if ($userInfo['status'] != 1) {
            throw new AppException(StatusCode::ERR_EXCEPTION, '用户存在异常');
        }
        // 只有管理员才可以访问的接口
        if (!empty($userArr['user_third'][0]['third_user_id'])) {
            $group = make(\App\Core\Services\Redmine\GroupsUsersService::class)->myGroup($userArr['user_third'][0]['third_user_id']);
        }

        $oaFiles = OaUserFilesModel::query()->select(['oa_user_files.*', 'oa_company.name as company_name'])->leftJoin('oa_company', 'oa_user_files.company_id', '=', 'oa_company.id')->where('email', $userInfo->biz_mail)->where('incumbency', '在职')
            ->where('oa_user_files.status', 1)->first();

        if ($oaFiles) {
            if ($oaFiles->entry_date == '0000-00-00') {
                $oaFiles->entry_date = null;
            }
            $createdDay = $oaFiles->entry_date ? TimeUtils::getTimeNum($oaFiles->entry_date, date('Y-m-d H:i:s', time())) : '-';
        } else {
            $createdDay = '-';
        }


        $roles = !empty($userArr['groups_access']) ? array_column($userArr['groups_access'], 'group_id') : [];
        // 合并user主表中的角色和角色表中的角色，防止两边数据不同步致导权限不正常
        $roles = is_array($userArr['group_access_ids']) ? array_unique(array_merge($roles, $userArr['group_access_ids'])) : $roles;
        $permissions = [];
        $rolesTextArr = [];
        if ($roles) {
            $groups = make(AuthGroupModel::class)::query()->whereIn('id', $roles)->get();
            $groups = $groups ? $groups->toArray() : [];
            $roles = array_column($groups, 'role');
            $rolesTextArr = array_column($groups, 'name');
            $gPermissions = array_column($groups, 'btn_roles');
            foreach ($gPermissions as $permission) {
                if (is_array($permission)) {
                    $permissions = array_merge($permissions, $permission);
                }
            }
            $permissions = count($permissions) > 0 ? array_unique($permissions) : $permissions;
        }
        // 加入Leader角色
        if (!empty($userArr['is_leader_in_dept']) && is_array($userArr['is_leader_in_dept']) &&
            (in_array(1, $userArr['is_leader_in_dept']) || in_array('1', $userArr['is_leader_in_dept']))
        ) {
            $roles[] = 'Leader';
        }
        // $permissions = ['write:system', 'read:system'];
        // $permissions = [];
        // 获取部门名称
        $departmentText = '';
        if (!empty($userInfo->department)) {
            $departmentIds = is_array($userInfo->department) ? $userInfo->department : explode(',', $userInfo->department);
            $departmentText = UserDepartmentModel::query()->whereIn('id', $departmentIds)->pluck('name')->toArray();
            $departmentText = $departmentText ? implode(',', $departmentText) : null;
        }

        $result = [
            'user_settings' => $userInfo->user_settings,
            'product_detail_label_page' => $userInfo->product_detail_label_page,
            'product_detail_opening_mode' => $userInfo->product_detail_opening_mode,
            'user_id'              => $userInfo->id,
            'username'             => $userInfo->name,
            'email'                => $userInfo->biz_mail,
            'is_leader_in_dept'    => $userInfo->is_leader_in_dept,
            // "roles"                => ["Admin"],
            "roles"                => $roles,
            "roles_text"           => $rolesTextArr,
            // "permissions"          => ["read:system", "write:system", "delete:system"],
            "permissions"          => $permissions,
            "avatar"               => $userInfo->avatar,
            "oa"                   => $userInfo->oa_user,
            "must_change_password" => $userInfo->must_change_password,
            'position'             => $userInfo->position ?: ($oaFiles->duty ?? '-'),
            'position_auth'        => (strpos($userInfo->position, '经理') !== false || strpos($userInfo->position, '总监') !== false) ? 1 : 0,
            'created_day'          => $createdDay,
            'group_name'           => $group['lastname'] ?? '',
            'department'           => $userInfo->department,
            'department_text'      => $departmentText,
            'company_text'         => $oaFiles->company_name ?? '',
            'description'          => $userInfo->description,
            'birthday'             => $oaFiles->birthday ?? null,
            'is_super'             => $this->authService->isSuper(),
            'contact'              => $userInfo->contact,
            'sex'                  => $userInfo->sex,
            'birthday_custom'      => $userInfo->birthday_custom,
            'created_at'           => $userInfo->created_at,
            'last_login_time'      => $userInfo->last_login_time,
        ];
        if (isset($userInfo->userThird)) {
            foreach ($userInfo->userThird as $item) {
                $result[$item->platform] = $item;
            }
        }
        return $result;
    }

    /**
     * 初始化账号 修改密码&绑定redmine账号
     * @param $password
     * @param $redminePassword
     * @return void
     */
    public function initAccount($password, $redminePassword)
    {
        // $userInfo = $this->userModel::query()->find($this->auth->user()->getId());
        // $result = $this->bindRedmine($userInfo->email, $redminePassword);
        // return $result;
    }

    /**
     * 重置密码
     * @param $password
     * @param int $userId
     * @return int
     */
    public function resetPassword($password, int $userId = 0, $notice = false): int
    {
        $salt = Random::alnum(10);
        $makePass = md5($password . $salt);
        if ($userId == 0) {
            $userId = $this->auth->user()->getId();
        }
        $result = $this->userModel::query()->where('id', $userId)->update([
            'password'             => $makePass,
            'salt'                 => $salt,
            'must_change_password' => 0
        ]);
        if ($result && $notice) {
            $user = $this->userModel::query()->find($userId);
            // $content = "<p>您的账号: {$user->biz_mail} 已重置密码。</p><p>密码为: <span style='font-weight: bold'>{$password}</span> 请妥善保管！！</p>";
            $content =<<<EOT
                Hi {$user->name}<br/>
                <br/>
                您的账号安全等级低，系统已将密码重置。<br/>
                <br/>
                账号：{$user->biz_mail}<br/>
                密码：{$password}<br/>
                <br/>
                <br/>
                点击查看详情 <a href="http://bi.t-firefly.com:2101/">http://bi.t-firefly.com:2101/</a><br/>
                <br/>
                以上信息由系统发出，如有疑问请联系管理员。<br/>
            EOT;

            sendEmail('重置密码通知', $content, $user->biz_mail);
        }
        return 1;
    }

    /**
     * 初始化用户密码
     * @param $userId
     * @return void
     */
    public function initPassword($userId)
    {
        $newPass = Random::alnum(10);
        return $this->resetPassword($newPass, $userId, true);
    }

    /**
     * 初始化所有用户的密码(主要是除去密码为123456的)
     * @return bool
     */
    public function initUsersPassword()
    {
        // 企微状态为1且系统状态为1的用户
        $users = Db::table('user')->where('status', 1)->where('bi_status', 1)->whereNull('deleted_at')->get()->toArray();
        // $users = $this->userModel::query()->select(['*'])->where('status', 1)->where('bi_status', 1)->get()->toArray();
        $changCount = 0;
        // model的password等字段被隐藏输出，故用Db查询，查询转array后，单条数据依然为object格式
        foreach ($users as $user) {
            $defdultPass = makePassword($this->userModel->defaultPassword, $user->salt);
            // 未修改过密码
            if ($user->password == $defdultPass || empty($user->password) || empty($user->salt)) {
                $newPass = Random::alnum(10);
                Log::get()->info("{$user->name},为初始密码，重新设置为:{$newPass}");
                $this->resetPassword($newPass, $user->id, true);
                $changCount++;
            } else {
                Log::get()->info("{$user->name},已不是初始密码");
            }
        }
        Log::get()->info("操作完成{$changCount}个用户被重置");
        return true;
    }

    public function userThirdList($third)
    {
        $userTable = 'bi_user';
        $thirdTable = 'bi_user_third';
        return $this->userModel::query()->selectRaw("{$userTable}.id, {$userTable}.name, {$userTable}.thumb_avatar, {$thirdTable}.platform, CAST({$thirdTable}.third_user_id as SIGNED) as third_user_id")
            ->join('user_third', 'user.id', '=', 'user_third.user_id')
            ->where('platform', $third)->where('user.status', 1)->orderBy('id', 'asc')->get();
    }

    /**
     * 获取第三方绑定数据
     * @return Builder|Model|object|null
     */
    //public function getThird($platform = 'redmine')
    //{
    //    $uid = Context::get('authUserId');
    //    $uid = $uid ?: $this->auth->user()->getId();
    //    return UserThirdModel::query()->where('user_id', $uid)->where('platform', $platform)->first();
    //}

    /**
     * 获取第三方绑定数据
     * @return Builder|Model|object|null
     */
    public function getThird($platform = 'redmine')
    {
        $uid = Context::get('authUserId');
        $uid = $uid ?: $this->auth->user()->getId();

        // 构建缓存键
        $cacheKey = "user_third_{$uid}_{$platform}";
        // 使用缓存组件
        $data = $this->cache->get($cacheKey);

        // 如果缓存不存在，则从数据库获取并存入缓存
        if ($data === null) {
            $data = UserThirdModel::query()
                ->where('user_id', $uid)
                ->where('platform', $platform)
                ->first();

            // 将数据存入缓存，设置过期时间为1分钟
            try {
                $this->cache->set($cacheKey, $data, 60);
            } catch (InvalidArgumentException $e) {
                var_dump($e->getMessage());
            }
        }

        return $data;
    }
    /**
     *获取用户信息
     * @param array $uids
     * @param string $platform
     * @return array
     */
    public function getUserListByThirdUserId(array $uids, string $platform): array
    {
        if (count($uids) == 0) {
            return [];
        }
        $userInfo = $this->userModel::query()->select(['user.*', 'user_third.third_user_id'])->join('user_third', 'user.id', '=', 'user_third.user_id')
            ->whereIn('user_third.third_user_id', $uids)->where('user_third.platform', $platform)->get();
        return $userInfo ? $userInfo->toArray() : [];
    }

    /**
     * 同步bi用户到redmine
     * @return bool
     */
    public function syncThirdByRedmine()
    {
        Log::get('system', 'system')->info("开始同步到redmine系统");
        // 所有BI user
        $users = $this->userModel::query()->get()->toArray();
        $count = count($users);
        $key = 1;
        Context::set('initRedmine', 'sync');
        foreach ($users as $user) {
            Log::get('system', 'system')->info("同步 {$user['name']}-{$user['id']} .({$key}/{$count})");
            if ($user['status'] == 1) {
                $this->accountService->initRedmine($user['id'], $user['name'], $user['biz_mail']);
            } else {
                $thridRow = UserThirdModel::query()->where('user_id', $user['id'])->first();
                // 存在第三方用户数据
                if ($thridRow) {
                    Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use ($user, $thridRow) {
                        $redmineUser = RedmineUserModel::query()->where('id', $thridRow->third_user_id)->first();
                        if ($redmineUser) {
                            if ($redmineUser->status == 1) {
                                // 设置锁定状态
                                $redmineUser->status = RedmineUserModel::STATUS_LOCK;
                                $redmineUser->save();
                            }
                            // 删除绑定邮箱
                            $emailAddrss = EmailAddressModel::query()->where('user_id', $redmineUser->id)->first();
                            if ($emailAddrss) {
                                $emailAddrss->delete();
                            }
                            Log::get('system', 'system')->info("{$user['name']} 设置为关闭状态.");
                        }
                    });

                }

            }
            $key++;
        }
        Log::get('system', 'system')->info("同步到redmine系统完成");
        return true;
    }

    /**
     * 获取指定部门的用户ID列表
     * @param array $departmentIds
     * @return array|mixed[]
     */
    public function assignDepartmentUserIds(array $departmentIds): array
    {
        $users = $this->assignDepartmentUserList($departmentIds);
        return $users ? array_column($users, 'id') : [];
    }

    public function assignDepartmentUserList(array $departmentIds): array
    {
        if (count($departmentIds) > 0) {
            $users = UserModel::where(function ($query) use ($departmentIds) {
                foreach ($departmentIds as $key => $val) {
                    if ($key == 0) {
                        $query->whereJsonContains('department', $val);
                    } else {
                        $query->orWhereJsonContains('department', $val);
                    }
                }
            })->get();
        }
        return !empty($users) ? $users->toArray() : [];
    }

    /**
     * 获取部门主管信息
     * @param $departmentId
     * @return array
     */
    public function getDepartmentUser($departmentId): array
    {
        $user = \App\Model\TchipBi\UserModel::query()->whereJsonContains('department', $departmentId)
            ->whereJsonContains('is_leader_in_dept', 1)->first();
        return $user ? $user->toArray() : [];
    }

    /**
     * 指定部门需要写报周的成员
     * @param array $departmentIds
     * @return array
     */
    public function assignDepartmentDoReportUserList(array $departmentIds): array
    {
        $users = $this->userModel::query()->where('status', 1)
            ->where('do_report', 1)
            ->where(function ($query) use ($departmentIds) {
                if ($departmentIds) {
                    $departmentIds = is_array($departmentIds) ? $departmentIds : explode(',', $departmentIds);
                    foreach ($departmentIds as $key => $val) {
                        if ($key == 0) {
                            $query->whereJsonContains('department', (int)$val);
                        } else {
                            $query->orWhereJsonContains('department', (int)$val);
                        }
                    }
                }
            })->get();
        return !empty($users) ? $users->toArray() : [];
    }

    /**
     * 根据角色码获取用户列表
     * @return void
     */
    public function getUsersByRole($role)
    {
        $groupId = \App\Model\TchipBi\AuthGroupModel::query()->where('role', $role)->value('id');
        return $groupId ? $this->getAccessUsersByGroupId($groupId) : [];
    }

    /**
     * 根据角色ID获取用户列表
     * @return void
     */
    public function getAccessUsersByGroupId($groupId)
    {
        $users = $this->userModel::query()->whereJsonContains('group_access_ids', (int)$groupId)->get();
        return $users ? $users->toArray() : [];
    }

    /**
     * 根据角色ID获取用户ID列表
     * @return void
     */
    public function getUserIdsByGroupId($groupId)
    {
        $users = $this->getAccessUsersByGroupId($groupId);
        return array_values($users, 'id');
    }

    /**
     * 根据user_third.thrid_user_id，获取bi的用户信息
     * @param $thirdUserId
     * @param $platform
     * @param $getField
     * @return array|\Hyperf\Utils\HigherOrderTapProxy|mixed|HigherOrderTapProxy|void|null
     */
    public function userInfoByThirdUserId($thirdUserId, $platform = 'redmine', $getField = null)
    {
        $result = $this->userModel::query()->selectRaw('bi_user.*, bi_user_third.third_user_id, bi_user_third.platform')
            ->leftJoin('user_third', 'user.id', '=', 'user_third.user_id')
            ->where('user_third.third_user_id', $thirdUserId)->where('user_third.platform', $platform)->first();
        return $getField && !empty($result->$getField) ? $result->$getField : ($result ? $result->toArray() : null);
    }

    /**
     * 根据多个名称获取用户信息
     * @param array $name
     * @param $platform
     * @param $getField
     * @return \Hyperf\Utils\HigherOrderCollectionProxy|mixed[]|null
     */
    public function userInfoByThirdByNames(array $name, $platform = 'redmine', $getField = null)
    {
        $result = $this->userModel::query()->selectRaw('bi_user.*, bi_user_third.third_user_id, bi_user_third.platform')
            ->leftJoin('user_third', 'user.id', '=', 'user_third.user_id')
            ->whereIn('user.name', $name)->where('user_third.platform', $platform)->get();
        return $getField && !empty($result->$getField) ? $result->$getField : ($result ? $result->toArray() : null);
    }

    /**
     * 根据多个user_third.thrid_user_id，获取多个bi的user_id，按顺序返回
     * @param $thirdUserIds
     * @param $platform
     * @return array
     */
    public function biUserIdByThirdUserId($thirdUserIds, $platform = 'redmine')
    {
        $result = $this->userModel::query()->selectRaw('bi_user.*, bi_user_third.third_user_id, bi_user_third.platform')
            ->leftJoin('user_third', 'user.id', '=', 'user_third.user_id')
            ->whereIn('user_third.third_user_id', $thirdUserIds)->where('user_third.platform', $platform)->get();
        $result =  $result->toArray() ?? [];

        // 构建映射关系
        $mapping = [];
        foreach ($result as $user) {
            $mapping[$user['third_user_id']] = $user['id'];
        }

        // 按照传入的顺序构建结果数组
        $orderedResult = [];
        foreach ($thirdUserIds as $thirdUserId) {
            $orderedResult[] = $mapping[$thirdUserId] ?? null;
        }

        return $orderedResult;
    }

    /**
     * 根据部门的名称来获取用户id
     * @param $departmentName
     * @return array|mixed[]
     */
    public function getUserByDepartmentName($departmentName)
    {
        $departmentId = UserDepartmentModel::query()->where('name',$departmentName)->value('id');
        if(!$departmentId)return [];
        return $this->assignDepartmentUserIds([$departmentId]);
    }

    /**
     * 根据用户名获取用户id
     * @param $name
     * @return \Hyperf\Utils\HigherOrderTapProxy|int|mixed|HigherOrderTapProxy
     */
    public function getUserIdByUserName($name)
    {
        return $this->userModel::query()->where('name', $name)->value('id') ?: 0;
    }

    /**
     * 发送密码重置链接
     * @param string $email
     * @return array
     * @throws AppException
     */
    public function forgetPasswordSendResetLink(string $email): array

    {
        // 验证邮箱是否存在
        $user = $this->userModel::query()
            ->where(function ($query) use ($email) {
                $query->where('email', $email)->orWhere('biz_mail', $email);
            })
            ->where('status', 1)
            ->where('bi_status', 1)
            ->first();

        if (!$user) {
            throw new AppException(StatusCode::VALIDATION_ERROR, '该邮箱未注册或账户已被禁用');
        }

        // 检查是否有未过期的令牌
        $emailCacheKey = self::FORGET_PASSWORD_EMAIL_CACHE_PREFIX . md5($email);
        $existingToken = getCache($emailCacheKey);
        
        if ($existingToken) {
            // 删除旧令牌
            delCache(self::FORGET_PASSWORD_CACHE_PREFIX . $existingToken);
        }

        // 生成新的重置令牌
        $token = Random::alnum(64);
        $tokenData = [
            'user_id' => $user->id,
            'email' => $email,
            'created_at' => time()
        ];

        // 缓存令牌信息
        $tokenCacheKey = self::FORGET_PASSWORD_CACHE_PREFIX . $token;
        setCache($tokenCacheKey, $tokenData, self::FORGET_PASSWORD_TOKEN_EXPIRE_TIME);
        setCache($emailCacheKey, $token, self::FORGET_PASSWORD_TOKEN_EXPIRE_TIME);

        // 发送重置邮件
        $this->forgetPasswordSendResetEmail($user, $token);

        return [
            'message' => '密码重置链接已发送',
            'expires_in' => self::FORGET_PASSWORD_TOKEN_EXPIRE_TIME
        ];
    }

        /**
     * 验证重置令牌
     * @param string $token
     * @return array
     * @throws AppException
     */
    public function forgetPasswordVerifyResetToken(string $token): array
    {
        if (empty($token)) {
            throw new AppException(StatusCode::VALIDATION_ERROR, '重置令牌不能为空');
        }

        $tokenCacheKey = self::FORGET_PASSWORD_CACHE_PREFIX . $token;
        $tokenData = getCache($tokenCacheKey);

        if (!$tokenData) {
            throw new AppException(StatusCode::VALIDATION_ERROR, '重置链接已失效，请重新申请');
        }

        // 验证用户是否仍然有效
        $user = $this->userModel::query()->find($tokenData['user_id']);
        if (!$user || $user->status != 1 || $user->bi_status != 1) {
            throw new AppException(StatusCode::VALIDATION_ERROR, '用户账户已被禁用');
        }

        return [
            'valid' => true,
            'email' => $tokenData['email'],
            'expires_at' => $tokenData['created_at'] + self::FORGET_PASSWORD_TOKEN_EXPIRE_TIME
        ];
    }

    /**
     * 重置密码
     * @param string $token
     * @param string $newPassword
     * @return array
     * @throws AppException
     */
    public function forgetPasswordResetPassword(string $token, string $newPassword): array
    {
        // 验证令牌并获取缓存数据
        $this->forgetPasswordVerifyResetToken($token);
        
        $tokenCacheKey = self::FORGET_PASSWORD_CACHE_PREFIX . $token;
        $cachedData = getCache($tokenCacheKey);
        
        if (!$cachedData) {
            throw new AppException(StatusCode::VALIDATION_ERROR, '重置链接已失效');
        }

        // 重置密码
        $result = $this->resetPassword($newPassword, $cachedData['user_id'], false);
        
        if (!$result) {
            throw new AppException(StatusCode::VALIDATION_ERROR, '密码重置失败，请重试');
        }

        // 清除缓存的令牌
        delCache($tokenCacheKey);
        $emailCacheKey = self::FORGET_PASSWORD_EMAIL_CACHE_PREFIX . md5($cachedData['email']);
        delCache($emailCacheKey);

        return [
            'message' => '密码重置成功',
            'reset_at' => time()
        ];
    }

    /**
     * 发送重置邮件
     * @param $user
     * @param string $token
     */
    private function forgetPasswordSendResetEmail($user, string $token): void
    {
        $resetUrl = env('BI_FRONTEND_HOST').'/#/change-password?token=' . $token;
        
        $content = 
        <<<EOT
            <div style="max-width: 600px; margin: 0 auto; padding: 40px 20px; background-color: #f8f9fa;">
                <!-- Header with logo and title -->
                <div style="text-align: center; margin-bottom: 40px;">
                    <div style="display: inline-flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                        <img src="http://bi.t-firefly.com:2101/files/2025/06/250619074458_logo-tchip-blue.png" alt="Logo" style="width: 40px; height: 40px; margin-right: 12px;" />
                        <h1 style="color: #333; font-size: 24px; font-weight: 600; margin: 0;">数字天启找回密码</h1>
                    </div>
                </div>
                
                <!-- Main content -->
                <div style="background-color: white; padding: 40px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <p style="color: #333; font-size: 22px; margin-bottom: 20px;font-weight:bolder;">Hi，{$user->name}</p>
                    
                    <p style="color: #666; font-size: 18px; line-height: 1.6; margin-bottom: 30px;">
                        您的找回密码的请求已经收到，请在30分钟之内点击以下按钮验证后进行修改密码操作：
                    </p>
                    
                    <div style="text-align: left; margin: 40px 0;">
                        <a href="{$resetUrl}" 
                           style="background-color: #3977F7; color: white; padding: 15px 40px; text-decoration: none; border-radius: 8px; display: inline-block; font-size: 16px; font-weight: 500;">
                            修改密码
                        </a>
                    </div>
                    
                    <p style="color: #999; font-size: 16px; line-height: 1.5;">
                        如果按钮无法点击，请点击或复制制以下链接到浏览器地址栏打开：
                        <a href="{$resetUrl}" style="color: #3977F7; word-break: break-all;">{$resetUrl}</a>
                    </p>
                    <p style="color: #999; font-size: 16px; text-align: center; margin-top: 30px;">
                        本邮件由系统自动发送，请勿回复。
                    </p>
                </div>
            </div>
        EOT;

        sendEmail('密码重置请求', $content, $user->biz_mail ?: $user->email);
    }


}