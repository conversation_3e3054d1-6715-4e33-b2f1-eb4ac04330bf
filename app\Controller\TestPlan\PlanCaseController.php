<?php

namespace App\Controller\TestPlan;

use App\Annotation\ControllerNameAnnotation;
use App\Controller\BaseController;
use App\Core\Services\TestPlan\TestPlanCaseService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;

/**
 * @ControllerNameAnnotation("测试计划用例")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class PlanCaseController extends BaseController
{
    /**
     * @var TestPlanCaseService
     * @Inject()
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("事项列表")
     * @Middleware(AuthMiddleware::class)
     */
    public function getIssueList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getIssueList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }
    /**
     * @ControllerNameAnnotation("测试计划用例-新增结果")
     * @Middleware(AuthMiddleware::class)
     */
    public function addCaseResult()
    {
        $id = (int) $this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->addCaseResult($id, $params);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("测试计划用例-导出")
     * @Middleware(AuthMiddleware::class)
     */
    public function exportCase()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->export($filter, $op, $sort, $order);
        return $result;
    }

    //获取测试计划用例结果
    public function getTestResult()
    {
        $id = (int) $this->request->input('id', 0);
        $result = $this->service->getTestResult($id);
        return $this->response->success($result);
    }

    //获取测试计划用例步骤结果
    public function getLatestStepResult()
    {
        $id = (int) $this->request->input('id', 0);
        $result = $this->service->getLatestStepResult($id);
        return $this->response->success($result);
    }

    //获取测试计划用例日志
    public function getTestPlanCaseLog()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getTestPlanCaseLog($filter, $op, $sort, $order,$limit);
        return $this->response->success($result);
    }

    public function getAllList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getAllList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }
}
