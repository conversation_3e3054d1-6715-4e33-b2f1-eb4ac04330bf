<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/17 下午6:21
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\MpWx;

use App\Constants\AutoUserCountCode;
use App\Constants\CommonCode;
use App\Constants\StatusCode;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\User\User;
use App\Model\User\UserDepartment;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\Guzzle\ClientFactory;

class MpWxBaseService
{
    /**
     * @var \Hyperf\Guzzle\ClientFactory
     */
    private $clientFactory;

    public function __construct(ClientFactory $clientFactory)
    {
        $options = [
            'base_uri' => 'https://api.weixin.qq.com/'
        ];
        $this->clientFactory = $clientFactory->create($options);
    }

    protected function sendRequest($uri, $options, $method = 'get')
    {
        Log::get()->warning($uri, $options);
        try {
            $response = $this->clientFactory->request($method, $uri, $options);
            $responseArr = json_decode($response->getBody()->getContents(), true);
            if(isset($responseArr['errcode']) && $responseArr['errcode'] !== 0){
                if($responseArr['errcode'] === 40001){
                    delCache(CommonCode::REDISKEY_MP_WX_STATIONPC_TOKEN);
                }
                throw new AppException(StatusCode::ERR_EXCEPTION, json_encode($responseArr));
            }
            return $responseArr;
        } catch (GuzzleException $e) {

            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
    }

    /**
     * 获取 token
     * @param $appid
     * @param $secret
     * @param $redisKey
     * @return mixed
     */
    public function getToken($appid, $secret, $redisKey)
    {
        $response = $this->sendRequest('cgi-bin/token', [
            'query'=>[
                'grant_type' => 'client_credential',
                'appid'      => $appid,
                'secret'     => $secret
            ]
        ]);
        setCache($redisKey, $response['access_token']);
        return $response;
    }

    public function mpList($mp)
    {
        $items = [
            'stationpc' => [
                'appid' => env('MP_STATIONPC_APPID'),
                'secret' => env('MP_STATIONPC_SECRET'),
                'redis_key' => CommonCode::REDISKEY_MP_WX_STATIONPC_TOKEN
            ],
        ];

        return $items[$mp] ?? '';
    }
}