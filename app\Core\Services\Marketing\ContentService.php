<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/23 上午11:41
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing;

use App\Model\StationPCManager\MarketingContentModel;
use Hyperf\Database\Model\Model;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;

/**
 * 服务类-推广内容
 */
class ContentService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var MarketingContentModel
     */
    protected $model;

    /**
     * 获取列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return mixed
     */
    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        return $query->orderBy($sort, $order)->with(['platform', 'channel'])->paginate($limit);
    }

}