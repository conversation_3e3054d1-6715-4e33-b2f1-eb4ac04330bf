<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\OaQcExamineNumCnfService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("QC报脸数量管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OaQcExamineNumCnfController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var OaQcExamineNumCnfService
     */
    protected $service;
}