<?php

namespace App\Core\Services\ExceptionRecord;

use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Exception\AppException;
use App\Model\TchipBi\ExceptionRecordRelationReplyModel;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\Di\Annotation\Inject;

class ExceptionRecordReplyService extends BusinessService
{
    /**
     * @Inject()
     * @var ExceptionRecordRelationReplyModel
     */
    protected $model;


    /**
     * 新增/编辑
     * @param int $id
     * @param array $values
     * @return bool|Builder|Model|int
     */
    public function doEdit(int $id, array $values)
    {
        if ($id > 0) {
            $row = $this->model::query()->find($id);
            if (!$row) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            }
            $result = $row->update($values);
        } else {
            if (empty($values['relation_id'])) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
            }
            empty($values['created_by']) && $values['created_by'] = auth()->id();
            $result = $this->model::query()->create($values);
        }
        return $result;
    }

    /**
     * 获取所有数据列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return mixed
     */
    public function getAllList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 999);
        $list = $query->with(['user'])->orderBy($sort, $order)->get();
        $list = $list ? $list->toArray() : [];
        return $list;
    }
}