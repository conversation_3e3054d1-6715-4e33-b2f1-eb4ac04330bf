<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/28 上午10:30
 * <AUTHOR> X
 * @Description
 */

namespace App\Controller\TchipBbs;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipBbs\BlockService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\BbsUserMiddleware;

/**
 * @ControllerNameAnnotation("文化幻灯片")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class BlockController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var BlockService
     */
    protected $service;

    public function getList()
    {
        return parent::getList(); // TODO: Change the autogenerated stub
    }

    /**
     * @Middleware(BbsUserMiddleware::class)
     */
    public function doEdit()
    {
        return parent::doEdit(); // TODO: Change the autogenerated stub
    }

    public function overView()
    {
        return parent::overView(); // TODO: Change the autogenerated stub
    }
}