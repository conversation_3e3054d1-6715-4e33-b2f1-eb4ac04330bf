<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/21 下午5:29
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Setting;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Setting\AuthMenuService;
use App\Middleware\AuthMiddleware;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Annotation\Middleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("路由管理")
 * @Controller()
 * @Middleware(AuthMiddleware::class)
 */
class RouterController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var AuthMenuService
     */
    protected $service;

    /**
     * @RequestMapping(path="/router/getList")
     * @return ResponseInterface
     */
    public function getList(): ResponseInterface
    {
        $routers = $this->service->getList();
        return $this->response->success($routers);
    }

    /**
     * @return void
     */
    public function menuRoleNameList()
    {
        $name = $this->request->input('name');
        $nameList = $this->service->menuRoleNameList($name);
        return $this->response->success($nameList);
    }
}