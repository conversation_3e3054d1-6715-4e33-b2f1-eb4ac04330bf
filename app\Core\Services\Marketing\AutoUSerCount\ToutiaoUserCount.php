<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/6 下午6:15
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing\AutoUSerCount;

use App\Constants\StatusCode;
use App\Exception\AppException;
use Nesk\Puphpeteer\Puppeteer;
use QL\Ext\Chrome;
use QL\QueryList;
use Swoole\Runtime;

/**
 * 采集头条频道用户数
 */
class ToutiaoUserCount implements AutoUserCountInterface
{
    /**
     * 获取头条粉丝数量
     * 参考资料：
     * https://github.com/hyperf/hyperf/issues/1557
     * https://querylist.cc/docs/guide/v4/Puppeteer
     * https://www.cnblogs.com/absoluteli/p/14295379.html
     * @param $channelId
     * @param $spaceUrl
     * @return int
     */
    public function getUserCount($channelId, $spaceUrl): int
    {
        Runtime::enableCoroutine(false);
        $ql = QueryList::getInstance();
        // 注册插件，默认注册的方法名为: chrome
        $ql->use(Chrome::class);
        $ql->chrome(function ($page, $browser) use ($spaceUrl) {
            $page->setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36');
            $page->goto($spaceUrl, [
                'waitUntil' => 'networkidle2'
            ]);
            $html = $page->content();
            $browser->close();
            return $html;
        }, [
            'executablePath' => '/usr/bin/chromium-browser',
            'args'           => [
                '--no-sandbox', '--headless', '--disable-gpu']
        ]);
        $text = $ql->find('.stat-item .num:eq(1)')->html();
        return intval($text);
    }
}