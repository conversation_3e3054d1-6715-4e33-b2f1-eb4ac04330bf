<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:59
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing;

use App\Constants\DataBaseCode;
use App\Constants\MarketingCode;
use App\Constants\StatusCode;
use App\Core\Services\TchipBi\ProductService;
use App\Core\Services\TchipSale\LinkageService;
use App\Core\Services\TchipSale\SaleService;
use App\Core\Utils\TimeUtils;
use App\Exception\AppException;
use App\Model\TchipBi\CategoryModel;
use App\Model\TchipBi\MarketingProductMonthReportModel;
use App\Model\TchipBi\ProductModel;
use App\Model\TchipSale\ClientSaleKyptTableModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use function _PHPStan_b8e553790\React\Promise\Stream\first;

/**
 * 品牌运营-产品销售数据
 */
class MarketingProductMonthReportService extends \App\Core\Services\BusinessService
{

    /**
     * @Inject()
     * @var MarketingProductMonthReportModel
     */
    protected $model;

    protected $kyptSaleSelect = "IFNULL(SUM( IFNULL(IF(curr='美元', 0, IF(ratemoney <> 0,ratemoney,money)), 0)), 0) as rmb, 
        IFNULL(SUM( IFNULL(IF(curr='美元', IF(ratemoney <> 0,ratemoney,money),0), 0)), 0) as dollar, 
        FROM_UNIXTIME(UNIX_TIMESTAMP(saledate),'%Y-%m') as date, 
        sale_id, 
        prod_id, 
        IFNULL(SUM(num), 0) as volume";

    protected $kyptSaleSelectByDay = "
        IFNULL(SUM( IFNULL(IF(curr='美元', 0, IF(ratemoney <> 0,ratemoney,money)), 0)), 0) as rmb, 
        IFNULL(SUM( IFNULL(IF(curr='美元', IF(ratemoney <> 0,ratemoney,money),0), 0)), 0) as dollar, 
        FROM_UNIXTIME(UNIX_TIMESTAMP(saledate),'%Y-%m-%d') as date,  -- 修改为按天分组
        sale_id, 
        prod_id, 
        IFNULL(SUM(num), 0) as volume
    ";

    // 分别统计 销售 退货
    protected $kyptSaleSelectByDayAsPosNeg = "
        IFNULL(SUM(IF(num > 0, IFNULL(IF(curr='美元', 0, IF(ratemoney <> 0, ratemoney, money)), 0), 0)), 0) as rmb, 
        IFNULL(SUM(IF(num < 0, IFNULL(IF(curr='美元', 0, IF(ratemoney <> 0, ratemoney, money)), 0), 0)), 0) as rmb_negative,
        
        IFNULL(SUM(IF(num > 0, IFNULL(IF(curr='美元', IF(ratemoney <> 0, ratemoney, money), 0), 0), 0)), 0) as dollar, 
        IFNULL(SUM(IF(num < 0, IFNULL(IF(curr='美元', IF(ratemoney <> 0, ratemoney, money), 0), 0), 0)), 0) as dollar_negative, 
        
        FROM_UNIXTIME(UNIX_TIMESTAMP(saledate), '%Y-%m-%d') as date,  -- 按天分组
        sale_id, 
        prod_id, 
        
        IFNULL(SUM(IF(num > 0, num, 0)), 0) as volume,
        IFNULL(SUM(IF(num < 0, num, 0)), 0) as volume_negative
    ";

    public function doEdit(int $id, array $values)
    {
        if ($id <= 0) {
            if ($this->model::query()->where('product_id', $values['product_id'])->where('date', $values['date'])->first()) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.Data_is_exist'));
            }
        }
        return parent::doEdit($id, $values); // TODO: Change the autogenerated stub
    }

    public function saveStatisticsProductSales($brandCode = 'stationpc', $date = null)
    {
        $brandProducts = ProductModel::query()->selectRaw('id, IFNULL(product_sale_name, product_name) as product_name')->where('brand', $brandCode)
            ->where('status', 1)
            ->where('is_marketing', 1)
            ->get();
        $brandProducts = $brandProducts ? $brandProducts->toArray() : [];
        if (count($brandProducts) <= 0) return false;
        $brandProductNames = array_column($brandProducts, 'product_name');
        $brandProducts = array_column($brandProducts, null, 'product_name');

        $saleProducts = make(LinkageService::class)->getProductsByNames($brandProductNames);
        if (!$saleProducts) return false;
        // 默认统计上一个月的
        $date = $date ? : TimeUtils::getPreviousMonth();
        $nowDate = TimeUtils::getMonthFirstandLast($date);
        foreach ($saleProducts as $productItem) {
            $amount = make(SaleService::class)->statisticsMoneyByDate($nowDate['starttime'], $nowDate['endtime'], null, ['prod_id' => $productItem['id']]);
            $volume = make(SaleService::class)->statisticsVolumeByDate($nowDate['starttime'], $nowDate['endtime'], null, ['prod_id' => $productItem['id']]);
            $save = [
                'brand_name'  => CategoryModel::query()->where('type', 'compay_brand')->where('keywords', $brandCode)->value('name'),
                'brand_code'  => $brandCode,
                'product_id'  => $brandProducts[$productItem['text']]['id'] ?? 0,
                'date'        => $date,
                'sale_amount' => $amount,
                'sale_volume' => $volume,
            ];
            $filter = [
                'brand_code' => $brandCode,
                'product_id' => $save['product_id'],
                'date' => $date,
            ];
            $this->model::updateOrCreate($filter, $save);
        }
        return true;
    }

    /**
     * 按月份统计产品销售情况(查本表)
     * @param $brandCode
     * @param $date
     * @return array
     */
    public function statisticsSalesvself($brandCode, $date)
    {
        $products = make(ProductService::class)->getBrandMarketProducts($brandCode);
        if (!$products) {
            return [];
        }
        $products = array_column($products, null, 'id');
        $dateArr  = explode(' - ', $date);

        if (count($dateArr) == 2) {
            $dateArr[0]         = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1]         = TimeUtils::getMonthLastday($dateArr[1]);
            $date               = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        } else {
            $month              = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0]         = $month['starttime'];
            $dateArr[1]         = $month['endtime'];
            $date               = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        }

        $filter = [
            'brand_code' => $brandCode,
            'date'       => "{$dateArr[0]} - $dateArr[1]}"
        ];
        $op     = [
            'date' => 'DATE',
        ];
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'date', 'desc', 9999, $this->model);
        $saleData = $query->select('*')->addSelect(DB::raw("FROM_UNIXTIME(UNIX_TIMESTAMP(date),'%Y-%m') as date_month"))->get();
        $saleData = $saleData ? $saleData->toArray() : [];

        $monthNum = TimeUtils::calculateMonthDifference($dateArr[0], $dateArr[1]);
        for ($i = 0; $i <= $monthNum; $i++) {
            $dateItem          = $i == 0 ? date('Y-m', strtotime($dateArr[0])) : date('Y-m', strtotime("+{$i} month", strtotime($dateArr[0])));
            $result[$dateItem] = [
                'rmb'    => 0,
                'dollar' => 0,
                'amount' => 0,
                'volume' => 0,
                'date'   => $dateItem,
            ];
            // 生成各平台的字段
            foreach ($products as $product) {
                $result[$dateItem]['amount_' . $product['id']] = 0;
                $result[$dateItem]['rmb_' . $product['id']]    = 0;
                $result[$dateItem]['dollar_' . $product['id']] = 0;
                $result[$dateItem]['volume_' . $product['id']] = 0;
            }
        }

        foreach ($saleData as $datum) {
            $result[$datum['date_month']]['rmb']    += $datum['sale_rmb'];
            $result[$datum['date_month']]['dollar'] += $datum['sale_dollar'];
            $result[$datum['date_month']]['volume'] += $datum['sale_volume'];
            // 当月总销额
            $result[$datum['date_month']]['amount'] += $datum['sale_amount'];
            // 指定销额
            $result[$datum['date_month']]['amount_' . $datum['product_id']] += $datum['sale_amount'];
            $result[$datum['date_month']]['rmb_' . $datum['product_id']]    += $datum['sale_rmb'];
            $result[$datum['date_month']]['dollar_' . $datum['product_id']] += $datum['sale_dollar'];
            $result[$datum['date_month']]['volume_' . $datum['product_id']] += $datum['sale_volume'];
        }
        return array_values(collect($result)->sortByDesc('date')->toArray());
    }

    /**
     * 统计指定日期范围内（按天）产品销售情况，并按月汇总返回结果
     *
     * @param string $brandCode 品牌代码，用于获取品牌相关产品
     * @param string $date 日期范围，格式为 "YYYY-MM-DD - YYYY-MM-DD"
     * @param string|null $productName 可选参数，产品名称，若指定则仅查询该产品的销售情况
     * @return array 按月汇总的产品销售统计数据
     */
    public function statisticsSalesByDay($brandCode, $date, $productName = null)
    {
        // 获取产品列表，优先根据产品名称查询，否则根据品牌代码查询
        if (!$productName) {
            // 如果没有指定产品名称，获取品牌的市场产品列表
            $products = make(ProductService::class)->getBrandMarketProducts($brandCode);
            if (!$products) {
                // 如果没有找到产品，直接返回空数组
                return [];
            }
        } else {
            // 如果指定了产品名称，则根据名称获取所有相关产品
            $products = make(ProductService::class)->getAllList(['product_name' => $productName]);
        }

        // 获取销售平台列表，只包含启用状态的平台
        $salePlatforms = make(MarketingPlatformService::class)->getAllList(['type' => MarketingCode::PLATFORM_TYPE_SALE, 'status' => 1]);
        if (!$salePlatforms) {
            // 如果没有找到销售平台，直接返回空数组
            return [];
        }

        // 提取销售平台和产品的ID数组，供后续查询使用
        $salesIds = array_column($salePlatforms, 'sale_platform_id');
        $productSaleIds = array_column($products, 'sale_product_id');

        // 构建筛选条件和操作符
        $filter = [
            // 'delete_time' => 0, // 不包含被删除的记录
            // 'replenish' => 0,   // 不包含补货记录
            'prod_id' => implode(',', $productSaleIds), // 产品ID列表
            'sale_id' => implode(',', $salesIds),       // 销售平台ID列表
        ];
        $op = [
            'prod_id' => 'IN',  // 产品ID匹配操作符
            'sale_id' => 'IN',  // 销售平台ID匹配操作符
        ];

        // 解析日期范围
        $dateArr = explode(' - ', $date);
        if (count($dateArr) == 2) {
            // 如果是日期范围，获取起始日和结束日
            $startDate = $dateArr[0];
            $endDate = $dateArr[1];
            $filter['saledate'] = $startDate . ' - ' . $endDate;
            $op['saledate'] = 'DATE';
        } else {
            // 如果只有单个日期，则使用当天作为起始和结束日期
            $startDate = $dateArr[0];
            $endDate = $startDate;
            $filter['saledate'] = $startDate . ' - ' . $endDate;
            $op['saledate'] = 'DATE';
        }

        $saleModel = make(ClientSaleKyptTableModel::class);
        $query = $saleModel->getKyptPjSaleSelect($saleModel->kyptCountSelect, $saleModel->pjCountSelect);

        // 构建查询参数并执行查询，获取销售数据（按天）
        list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 9999, $query);
        $saleData = $query->selectRaw($this->kyptSaleSelectByDayAsPosNeg) // 使用原生SQL选择字段
        ->groupBy('date')           // 按销售日期分组（按天）
        ->groupBy('prod_id')            // 按产品ID分组
        ->orderBy('date', 'desc')   // 按销售日期降序排列
        ->get();                        // 执行查询并获取结果
        if ($saleData) {
            $saleData = dbToArray($saleData);
        }

        // 初始化汇总结果数组
        $result = [];

        // 初始化汇率数组
        $rate = [];

        // 初始化结果数组，包含指定时间段内的每月数据
        $result   = [];
        $monthNum = TimeUtils::calculateMonthDifference($dateArr[0], $dateArr[1]);
        for ($i = 0; $i <= $monthNum; $i++) {
            $dateItem = $i == 0 ? date('Y-m', strtotime($dateArr[0])) : date('Y-m', strtotime("+{$i} month", strtotime($dateArr[0])));
            // 初始化基础字段
            $result[$dateItem] = [
                'rmb'             => 0,  // 人民币销售额
                'rmb_negative'    => 0,  // 人民币负销售额
                'dollar'          => 0,  // 美元销售额
                'dollar_negative' => 0,  // 美元负销售额
                'amount'          => 0,  // 总销售额（以人民币计算）
                'volume'          => 0,  // 销售量
                'volume_negative' => 0,  // 负销售量
                'date'            => $dateItem,  // 当前月份
            ];

            // 初始化各平台的销售额和销售量字段
            foreach ($salesIds as $saleId) {
                $result[$dateItem]['amount_' . $saleId] = 0;               // 各平台的销售额
                $result[$dateItem]['amount_' . $saleId . '_negative'] = 0; // 各平台的负销售额
                $result[$dateItem]['volume_' . $saleId] = 0;               // 各平台的销售量
                $result[$dateItem]['volume_' . $saleId . '_negative'] = 0; // 各平台的负销售量
            }
        }

        // 遍历查询结果，按天计算销售数据，并按月汇总
        foreach ($saleData as $datum) {
            // 获取当前日期的年份和月份
            $monthKey = date('Y-m', strtotime($datum['date']));

            //// 初始化当前月份的数据结构
            //if (!isset($result[$monthKey])) {
            //    $result[$monthKey] = [
            //        'rmb' => 0,
            //        'dollar' => 0,
            //        'amount' => 0,
            //        'volume' => 0,
            //        'date' => $monthKey,
            //    ];
            //    // 初始化每个产品的销售数据
            //    foreach ($products as $product) {
            //        if (!empty($product['sale_product_id'])) {
            //            $result[$monthKey]['amount_' . $product['sale_product_id']] = 0;
            //            $result[$monthKey]['volume_' . $product['sale_product_id']] = 0;
            //        }
            //    }
            //}

            // 获取当前日期的美元汇率，若不存在则计算并缓存
            if (empty($rate[$datum['saledate']])) {
                $rate[$datum['saledate']] = nowUsdRate($datum['saledate']);
            }
            // 确保当前月份和产品ID的初始值（包含negative字段）
            $result[$monthKey]['rmb'] = $result[$monthKey]['rmb'] ?? 0;
            $result[$monthKey]['rmb_negative'] = $result[$monthKey]['rmb_negative'] ?? 0;
            $result[$monthKey]['dollar'] = $result[$monthKey]['dollar'] ?? 0;
            $result[$monthKey]['dollar_negative'] = $result[$monthKey]['dollar_negative'] ?? 0;
            $result[$monthKey]['volume'] = $result[$monthKey]['volume'] ?? 0;
            $result[$monthKey]['volume_negative'] = $result[$monthKey]['volume_negative'] ?? 0;
            $result[$monthKey]['amount'] = $result[$monthKey]['amount'] ?? 0;
            $result[$monthKey]['amount_' . $datum['prod_id']] = $result[$monthKey]['amount_' . $datum['prod_id']] ?? 0;
            $result[$monthKey]['amount_' . $datum['prod_id'] . '_negative'] = $result[$monthKey]['amount_' . $datum['prod_id'] . '_negative'] ?? 0;
            $result[$monthKey]['volume_' . $datum['prod_id']] = $result[$monthKey]['volume_' . $datum['prod_id']] ?? 0;
            $result[$monthKey]['volume_' . $datum['prod_id'] . '_negative'] = $result[$monthKey]['volume_' . $datum['prod_id'] . '_negative'] ?? 0;

            // 累加当前日期的人民币销售额
            $result[$monthKey]['rmb'] += $datum['rmb'];
            $result[$monthKey]['rmb_negative'] += $datum['rmb_negative']; // 累加negative的人民币销售额

            // 累加当前日期的美元销售额
            $result[$monthKey]['dollar'] += $datum['dollar'];
            $result[$monthKey]['dollar_negative'] += $datum['dollar_negative']; // 累加negative的美元销售额

            // 累加当前日期的销售量
            $result[$monthKey]['volume'] += $datum['volume'];
            $result[$monthKey]['volume_negative'] += $datum['volume_negative']; // 累加negative的销售量

            // 计算并累加当前日期的总销额（人民币+美元折算成人民币）
            $result[$monthKey]['amount'] += ($datum['rmb'] + $datum['rmb_negative']) + ($datum['dollar'] + $datum['dollar_negative']) * $rate[$datum['saledate']];

            // 累加指定产品的销额和销售量
            $result[$monthKey]['amount_' . $datum['prod_id']] += $datum['rmb'] + $datum['dollar'] * $rate[$datum['saledate']];
            $result[$monthKey]['amount_' . $datum['prod_id'] . '_negative'] += $datum['rmb_negative'] + $datum['dollar_negative'] * $rate[$datum['saledate']];

            $result[$monthKey]['volume_' . $datum['prod_id']] += $datum['volume'];
            $result[$monthKey]['volume_' . $datum['prod_id'] . '_negative'] += $datum['volume_negative']; // 累加negative的销售量

        }

        foreach ($result as $monthKey => &$data) {
            // 对人民币、美元、销售量及其负值进行四舍五入保留两位小数
            $data['rmb'] = round($data['rmb'], 2);
            $data['rmb_negative'] = round($data['rmb_negative'], 2);
            $data['dollar'] = round($data['dollar'], 2);
            $data['dollar_negative'] = round($data['dollar_negative'], 2);
            $data['volume'] = round($data['volume'], 2);
            $data['volume_negative'] = round($data['volume_negative'], 2);

            // 对总销额和其他计算值进行四舍五入
            $data['amount'] = round($data['amount'], 2);

            // 处理每个带有 prod_id 的 amount 和 volume 相关字段，保留两位小数
            foreach ($data as $key => $value) {
                if (strpos($key, 'amount_') === 0 || strpos($key, 'volume_') === 0) {
                    $data[$key] = round($value, 2);
                }
            }

            // 计算rmb和rmb_negative的汇总值
            $data['rmb_sum'] = $data['rmb'] + $data['rmb_negative'];

            // 计算dollar和dollar_negative的汇总值
            $data['dollar_sum'] = $data['dollar'] + $data['dollar_negative'];

            // 计算volume和volume_negative的汇总值
            $data['volume'] = $data['volume'] + $data['volume_negative'];

            // 处理各prod_id的amount和volume汇总
            foreach ($data as $key => $value) {
                if (strpos($key, 'amount_') === 0 && strpos($key, '_negative') === false) {
                    // 计算带有prod_id的amount汇总值
                    $negative_key = $key . '_negative';
                    $data[$key . '_sum'] = $value + ($data[$negative_key] ?? 0);
                }

                if (strpos($key, 'volume_') === 0 && strpos($key, '_negative') === false) {
                    // 计算带有prod_id的volume汇总值
                    $negative_key = $key . '_negative';
                    $data[$key . '_sum'] = $value + ($data[$negative_key] ?? 0);
                }
            }
        }


        // 返回按日期降序排列的汇总结果
        return array_values(collect($result)->sortByDesc('date')->toArray());
    }


    /**
     * 月份统计产品销售情况(查询销售系统表kypt)[[2024-06,...],[2024-05,...],[2024-04,...]]
     * @param $brandCode
     * @param $date
     * @return array
     */
    public function statisticsSales($brandCode, $date, $productName = null)
    {
        if (!$productName) {
            $products     = make(ProductService::class)->getBrandMarketProducts($brandCode);
            if (!$products) {
                return [];
            }
        } else {
            $products =  make(ProductService::class)->getAllList(['product_name' => $productName]);
        }

        $salePlatforms = make(MarketingPlatformService::class)->getAllList(['type' => MarketingCode::PLATFORM_TYPE_SALE, 'status' => 1]);
        if (!$salePlatforms) {
            return [];
        }
        $salesIds     = array_column($salePlatforms, 'sale_platform_id');
        $productSaleIds = array_column($products, 'sale_product_id');
        // 目前只统计人民币与美元，港元包含在人民币里
        // $select = "(SUM( IF(curr='美元', 0, IF(ratemoney <> 0,ratemoney,money)))) as rmb, (SUM( IF(curr='美元', IF(ratemoney <> 0,ratemoney,money),0))) as dollar, FROM_UNIXTIME(UNIX_TIMESTAMP(saledate),'%Y-%m') as date, sale_id, prod_id, SUM(num) as volume";
        $filter  = [
            // 'delete_time' => 0,
            // 'replenish'   => 0,
            'prod_id'     => implode(',', $productSaleIds),
            'sale_id'     => implode(',', $salesIds),
        ];
        $op      = [
            'prod_id' => 'IN',
            'sale_id' => 'IN',
        ];
        $dateArr = explode(' - ', $date);
        if (count($dateArr) == 2) {
            $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
            $date       = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        } else {
            $month              = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0]         = $month['starttime'];
            $dateArr[1]         = $month['endtime'];
            $date               = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        }

        $saleModel = make(ClientSaleKyptTableModel::class);
        $query = $saleModel->getKyptPjSaleSelect($saleModel->kyptCountSelect, $saleModel->pjCountSelect);

        list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 9999, $query);
        $saleData = $query->selectRaw($this->kyptSaleSelect)->groupBy('date')->groupBy('prod_id')->orderBy('saledate', 'desc')->get();
        // $saleData = $saleData ? array_column($saleData->toArray(), null, 'prod_id') : [];
        if ($saleData) {
            $saleData = dbToArray($saleData);
        }

        $monthNum = TimeUtils::calculateMonthDifference($dateArr[0], $dateArr[1]);
        for ($i = 0; $i <= $monthNum; $i++) {
            $dateItem = $i == 0 ? date('Y-m', strtotime($dateArr[0])) : date('Y-m', strtotime("+{$i} month", strtotime($dateArr[0])));
            $result[$dateItem] = [
                'rmb'    => 0,
                'dollar' => 0,
                'amount' => 0,
                'volume' => 0,
                'date'   => $dateItem,
            ];
            // 生成各平台的字段
            foreach ($products as $product) {
                if (!empty($product['sale_product_id'])) {
                    $result[$dateItem]['amount_' . $product['sale_product_id']] = 0;
                    $result[$dateItem]['volume_' . $product['sale_product_id']] = 0;
                }
            }
        }

        // 汇率数组
        $rate = [];
        foreach ($saleData as $datum) {
            if (empty($rate[$datum['date']])) {
                $rate[$datum['date']] = nowUsdRate($datum['date']);
            }
            $result[$datum['date']]['rmb']    += $datum['rmb'];
            $result[$datum['date']]['dollar'] += $datum['dollar'];
            $result[$datum['date']]['volume'] += $datum['volume'];
            // 当月总销额
            $result[$datum['date']]['amount'] += $datum['rmb'] + $datum['dollar'] * $rate[$datum['date']];
            // 指定销额
            $result[$datum['date']]['amount_' . $datum['prod_id']] += $datum['rmb'] + $datum['dollar'] * $rate[$datum['date']];
            $result[$datum['date']]['volume_' . $datum['prod_id']] += $datum['volume'];
        }
        return array_values(collect($result)->sortByDesc('date')->toArray());
    }


    /**
     * 获取品牌各产品销售情况
     * @param $brandCode
     * @param $date
     * @return array
     */
    public function monthProductReport($brandCode, $date)
    {
        $salePlatforms = make(MarketingPlatformService::class)->getAllList(['type' => MarketingCode::PLATFORM_TYPE_SALE, 'status' => 1]);
        if (!$salePlatforms) {
            return [];
        }
        $products     = make(ProductService::class)->getBrandMarketProducts($brandCode);
        if (!$products) {
            return [];
        }

        $salesIds     = array_column($salePlatforms, 'sale_platform_id');
        $productSaleIds = array_column($products, 'sale_product_id');

        // 目前只统计人民币与美元，港元包含在人民币里
        // $select = "(SUM( IF(curr='美元', 0, IF(ratemoney <> 0,ratemoney,money)))) as rmb, (SUM( IF(curr='美元', IF(ratemoney <> 0,ratemoney,money),0))) as dollar, FROM_UNIXTIME(UNIX_TIMESTAMP(saledate),'%Y-%m') as date, sale_id, prod_id, SUM(num) as volume";
        $filter  = [
            // 'delete_time' => 0,
            // 'replenish'   => 0,
            'prod_id'     => implode(',', $productSaleIds),
            'sale_id'     => implode(',', $salesIds),
        ];
        $op      = [
            'prod_id' => 'IN',
            'sale_id' => 'IN',
        ];
        $dateArr = explode(' - ', $date);
        if (count($dateArr) == 2) {
            $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
            $date       = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        } else {
            $month              = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0]         = $month['starttime'];
            $dateArr[1]         = $month['endtime'];
            $date               = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        }

        $saleModel = make(ClientSaleKyptTableModel::class);
        $query = $saleModel->getKyptPjSaleSelect($saleModel->kyptCountSelect, $saleModel->pjCountSelect);

        list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 9999, $query);
        $saleData = $query->selectRaw($this->kyptSaleSelect)->groupBy('date')->groupBy('prod_id')->orderBy('saledate', 'desc')->get();
        $saleData = $saleData ? array_column(dbToArray($saleData), null, 'prod_id') : [];
        $rate = [];
        foreach ($products as &$product) {
            if (!empty($saleData[$product['sale_product_id']])) {
                $saleDatum = $saleData[$product['sale_product_id']];
                if (empty($rate[$saleDatum['date']])) {
                    $rate[$saleDatum['date']] = nowUsdRate($saleDatum['date']);
                }
                $product = array_merge($product, $saleDatum);
                $product['amount'] = round($product['rmb'] + ($product['dollar'] * $rate[$saleDatum['date']]), 2);
            } else {
                $product['rmb'] = 0;
                $product['dollar'] = 0;
                $product['amount'] = 0;
                $product['volume'] = 0;
            }
        }

        return $products;
    }

    /**
     * 统计指定月份的产品销售情况
     * @param $brandCode
     * @param $date
     * @return array
     */
    public function monthReport($brandCode, $date)
    {
        $products     = make(ProductService::class)->getBrandMarketProducts($brandCode);
        if (!$products) {
            return [];
        }
        $dateArr = explode(' - ', $date);
        if (count($dateArr) == 2) {
            $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
            $date       = implode(' - ', $dateArr);
        } else {
            $month              = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0]         = $month['starttime'];
            $dateArr[1]         = $month['endtime'];
            $date               = implode(' - ', $dateArr);
        }
        $filter = [
            'brand_code' => $brandCode,
            'date' => $date,
            'delete_time' => 0,
            'replenish'   => 0,
        ];
        $op = [
            'date' => 'DATE',
        ];
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 99999);
        $rows = $query->groupBy('product_id')->get();
        $rows = $rows ? array_column($rows->toArray(), null, 'product_id') : [];
        foreach ($products as &$product) {
            if (!empty($rows[$product['id']])) {
                $product['amount'] = $rows[$product['id']]['amount'];
                $product['volume'] = $rows[$product['id']]['volume'];
            } else {
                $product['amount'] = 0;
                $product['volume'] = 0;
            }
        }
        return $products;
    }

    /**
     * 获取对应品牌产品销售量
     * @param string $brandCode 品牌code
     * @param string $date 日期
     * @return array
     */
    public function statisticsProductSales(string $brandCode, string $date, $byDay = false) :array
    {
        $defaultResult = ['amount' => 0.00, 'volume' => 0];
        $products     = make(ProductService::class)->getBrandMarketProducts($brandCode);
        if (!$products) {
            return $defaultResult;
        }
        $productSaleIds = array_column($products, 'sale_product_id');

        // 只统计指定平台
        $salePlatforms = make(MarketingPlatformService::class)->getAllList(['type' => MarketingCode::PLATFORM_TYPE_SALE, 'status' => 1]);
        $salesIds     = array_column($salePlatforms, 'sale_platform_id');

        $filter = [
            'prod_id' => implode(',', $productSaleIds),
            // 'delete_time' => 0,
            // 'replenish'   => 0,
            'sale_id'    => implode(',', $salesIds),
        ];
        $op = [
            'prod_id' => 'IN',
            'sale_id' => 'IN',
        ];

        $dateArr = explode(' - ', $date);
        if (count($dateArr) == 2) {
            $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
            $date       = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        } else {
            $month              = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0]         = $month['starttime'];
            $dateArr[1]         = $month['endtime'];
            $date               = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        }
        $saleModel = make(ClientSaleKyptTableModel::class);
        $query = $saleModel->getKyptPjSaleSelect($saleModel->kyptCountSelect, $saleModel->pjCountSelect);
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 99999, $query);
        $sales = $query->selectRaw($byDay ? $this->kyptSaleSelectByDay : $this->kyptSaleSelect)
            ->first();
        if ($sales) {
            // $sales = $sales->toArray();
            $sales = dbToArray($sales);
            $sales['amount'] = $sales['rmb'] + $sales['dollar'] * nowUsdRate($sales['date']);
        }
        return $sales ? : $defaultResult;
    }

    /**
     * 获取对应品牌产品销售量
     * @param string $brandCode 品牌code
     * @param string $date 日期
     * @return array
     */
    public function statisticsProductSalesLeft(string $brandCode, string $date) :array
    {
        $dateArr = explode(' - ', $date);
        $filter['brand_code'] = $brandCode;
        $op = [];
        if (count($dateArr) == 2) {
            $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
            $date       = implode(' - ', $dateArr);
            $filter['date'] = $date;
            $op['date']     = 'DATE';
        } else {
            $month              = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0]         = $month['starttime'];
            $dateArr[1]         = $month['endtime'];
            $date               = implode(' - ', $dateArr);
            $filter['date'] = $date;
            $op['date']     = 'DATE';
        }
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 99999);
        $sales = $query->selectRaw('IFNULL(SUM(IFNULL(amount, 0)), 0.00) as amount, IFNULL(SUM(IFNULL(volume, 0)), 0) as volume')
            ->first();
        return $sales ? $sales->toArray() : ['amount' => 0.00, 'volume' => 0];
    }

    /**
     * 指定产品指定时间销售数据
     * @param string $brandCode 品牌code
     * @param string $date 日期
     * @return array
     */
    public function assignProductSales(string $productName, string $date) :array
    {
        $product = make(ProductService::class)->overViewByName($productName);
        if (!$product) return [];

        $dateArr = explode(' - ', $date);
        $filter['prod_id'] = $product['sale_product_id'];

        // $filter['delete_time'] = 0; // 不包含被删除的记录
        // $filter['replenish'] = 0; // 不包含补货记录

        $op = [];
        if (count($dateArr) == 2) {
            $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
            $date       = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        } else {
            $month              = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0]         = $month['starttime'];
            $dateArr[1]         = $month['endtime'];
            $date               = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        }

        $saleModel = make(ClientSaleKyptTableModel::class);
        $query = $saleModel->getKyptPjSaleSelect($saleModel->kyptCountSelect, $saleModel->pjCountSelect);

        // list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 99999, ClientSaleKyptTableModel::query());
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 99999, $query);
        $sales = $query->selectRaw("(SUM( IF(curr='美元', 0, IF(ratemoney <> 0,ratemoney,money)))) as rmb, (SUM( IF(curr='美元', IF(ratemoney <> 0,ratemoney,money),0))) as dollar, FROM_UNIXTIME(UNIX_TIMESTAMP(saledate),'%Y-%m') as date, prod_id, SUM(num) as volume");

        if ($productName == 'Station M2' || $productName == 'Station M3') {
            //20240829 stationM2 stationM3 查询条件 只有电商仓 或 dwin_user_main_table.part_group_id = 53里所有的user_id
            $userIds = DB::connection(DataBaseCode::TCHIP_SALE)
                ->table('user_main_table')
                ->where('part_group_id', 53) // 该组暂定名称为‘天猫’
                ->pluck('user_id')
                ->toArray();

            $sales = $sales->where(function ($query) use ($userIds) {
                $query
                    ->where('cargo_space', '=', '027')
                    ->orWhereIn('user_id', $userIds); // 这里使用之前获取的user_id数组
            });
        }

        $sales = $sales->first();
        if ($sales) {
            $sales = dbToArray($sales);
            $sales['amount'] = $sales['rmb'] + $sales['dollar'] * nowUsdRate($sales['date']);
        } else {
            $sales = ['amount' => 0.00, 'volume' => 0];
        }
        return array_merge($product, $sales);
    }
}