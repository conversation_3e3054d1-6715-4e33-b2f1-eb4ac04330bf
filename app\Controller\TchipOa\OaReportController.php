<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\OaReportService;
use App\Request\Project\OverViewRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("周报管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OaReportController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var OaReportService
     */
    protected $service;

    public function getWeekWorkDate()
    {
        $id     = $this->request->input('id', '');
        $type   = $this->request->input('type', '');
        $result = $this->service->getWeekWorkDate($id, $type);
        return $this->response->success($result);
    }

    public function statistics()
    {
        $date = $this->request->input('report_date');
        $dateType = $this->request->input('date_type', 1);

        // 默认个人周报
        $reportType = $this->request->input('report_type', 2);
        $department = $this->request->input('department_id', null);
        $result = $this->service->statistics($date, $dateType, $reportType, $department);
        return $this->response->success($result);
    }

    /**
     * 发送填写周报通知
     * @return ResponseInterface
     */
    public function noticeReport(): ResponseInterface
    {
        $result = $this->service->informUserReport();
        return $this->response->success($result);
    }

    public function toDoReportUser()
    {
        $date = $this->request->input('report_date', date('Y-m-d'));
        // $dateType = $this->request->input('date_type', 1);
        //
        // // 默认个人周报
        // $reportType = $this->request->input('report_type', 2);
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->toDoReportUser($date, $filter, $op);
        return $this->response->success($result);
    }
}