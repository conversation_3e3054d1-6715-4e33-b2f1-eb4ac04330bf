<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/21 下午5:29
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Setting;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Setting\AuthGroupService;
use App\Core\Services\Setting\AuthMenuService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("角色管理")
 * @AutoController(prefix="/roleManagement")
 */
class RoleManagementController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var AuthGroupService
     */
    protected $service;

}