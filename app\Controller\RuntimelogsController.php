<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date      2023/4/9
 * <AUTHOR> @Description
 *
 */

namespace App\Controller;

use App\Core\Services\Crontab\RuntimeLogsService;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Http\Message\ResponseInterface;
use Hyperf\Di\Annotation\Inject;
use App\Annotation\CrotabLogsAnnotation;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * 系统日志文件
 * @AutoController()
 */
class RuntimelogsController extends BaseController
{
    /**
     * @Inject()
     * @var RuntimeLogsService
     */
    protected $service;


    //public function clean()
    //{
    //    make(RuntimeLogsService::class)->clean();
    //    return $this->response->success();
    //}

    /**
     * 获取日志内容
     * @return ResponseInterface
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function get()
    {
        $params = $this->request->all();
        validate($params,
            [
                'filepath'  => 'required|string', //日志路径
                'start'     => 'int|min:0',//开始行数
                'limit'     => 'int|min:1',//获取行数
                'direction' => 'required|in:footer,head',//获取方向，head:顶部,footer：底部
            ]
        );
        $file_path = $params['filepath'];
        $start     = $params['start'] ?? 0;
        $limit     = $params['limit'] ?? 300;
        $direction = $params['direction'];
        if (!file_exists($file_path)) {
            return $this->response->error("file not exist. $file_path ");
        }
        $res = $this->service->catFile($file_path, $start, $limit, $direction);
        return $this->response->success($res);
    }

    public function list()
    {
        $params = $this->request->all();
        validate($params,
            [
                'type'     => 'required|in:system,sql,app,crontab',
                'tasktype' => 'string',
            ]
        );
        $type = $params['type'];
        $filterArr = [];
        //定时任务格式日志
        if ($params['type'] == 'crontab') {
            $tasktype = $params['tasktype'] ?? '';
            if ($tasktype) {
                $path = RuntimeLogsService::Logs_path . '/crontab/' . $tasktype;
                $res  = $this->service->logFiles($path);
                foreach ($res as $key => $val) {
                    $isMatched = preg_match('/' . str_replace('/', '\/', $path) . '\/\d{4}\-\d{2}\/\d{4}\-\d{2}\-\d{2}\.log/', $val, $matches);
                    if ($isMatched) {
                        $filterArr[] = $val;
                    }
                }
            }
        } else {
            //系统日志 app,system,sql
            $path = RuntimeLogsService::Logs_path;
            $res  = $this->service->logFiles($path);
            foreach ($res as $key => $val) {
                $isMatched = preg_match('/' . str_replace('/', '\/', $path) . '\/\d{4}\-\d{2}\/' . $type . '\-\d{4}\-\d{2}\-\d{2}\.log/', $val, $matches);
                if ($isMatched) {
                    $filterArr[] = $val;
                }
            }
        }
        $res = $filterArr;
        return $this->response->success($res);
    }
}