<?php

namespace App\Core\Services\ExcelAnalyze\Writer;

use App\Core\Services\ExcelAnalyze\Html\HtmlWriter;
use Exception;
use Hyperf\HttpMessage\Stream\SwooleStream;
use Hyperf\HttpServer\Contract\ResponseInterface;
use InvalidArgumentException;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;


class ExcelWriter
{
    const SUPPORT_EXT = [
        'Xlsx',
        'Xls',
        'Csv',

    ];
    public static $autoSize = false;
    public $alignForMerge = [
        'horizontal' => Alignment::HORIZONTAL_LEFT,
        'vertical'   => Alignment::VERTICAL_CENTER,
        //        'wrapText' => true
    ];
    public $align = [
        'horizontal' => Alignment::HORIZONTAL_LEFT,
        'vertical'   => Alignment::VERTICAL_CENTER,
        //        'wrapText' => true
    ];
    public $borders = [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
            //            'color' => ['argb' => '00000000'],
        ]
    ];
    public $numberFormat = [
        'formatCode' => NumberFormat::FORMAT_GENERAL
    ];
    public $fill = [
//        'startColor' => [
////            'argb' => 'FFffd050',
//            'rgb' => 'ffffff',
//        ],
//        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,

//        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_GRADIENT_LINEAR,
//        'rotation' => 90,
//        'startColor' => [
//            'argb' => 'FFA0A0A0',
//        ],
//        'endColor' => [
//            'argb' => 'FFFFFFFF',
//        ],
    ];
    public $font = [
//        'name' => 'Arial',
//        'bold' => true,
//        'italic' => false,
//        'underline' => Font::UNDERLINE_DOUBLE,
//        'strikethrough' => false,
//        'color' => [
//            'rgb' => '000000'
//        ]
    ];
    private $filename;
    private $spreadsheet;
    private $writerType;
    private $activeSheet;
    private $lastRow = 0;
    private $sameCol = [];
    private $isClose = false;

    /**
     * ExcelWriter constructor.
     * @param string $filename 文件名
     * @param bool|string $sheetName sheet名称
     * @param null $writerType 导出文件类型【默认自动解析】
     * @param bool $cover 是否覆盖原文件【覆盖则完全丢失原文件信息，否则就在原文件基础进行修改】
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     * @throws Exception
     */
    function __construct($filename, $sheetName = false, $writerType = null, $cover = true)
    {
        $this->filename = $filename;
        if (!$writerType) {
            $this->writerType = $this::getReaderTypeFromExtension($filename);
        }
        if (!in_array($this->writerType, ExcelWriter::SUPPORT_EXT)) {
            responseLanguageException(0, '不支持的文件格式');
        }
        // excel导出类初始化
        if (!$cover && is_file($filename)) {
            $this->spreadsheet = IOFactory::load($filename);
        } else {
            $this->spreadsheet = new Spreadsheet();
        }
        // 获取当前工作部
        if ($sheetName) {
            $this->activeSheet = $this->spreadsheet->getSheetByName($sheetName);
            if ($this->activeSheet == null) {
                $this->activeSheet = $this->spreadsheet->getActiveSheet();
                $this->activeSheet->setTitle($sheetName);
            }
        } else {
            $this->activeSheet = $this->spreadsheet->getActiveSheet();
        }

        return $this;
    }

    /**
     * 自动解析文件类型
     * @param $filename
     * @return null|string
     */
    private static function getReaderTypeFromExtension($filename)
    {
        $pathinfo = pathinfo($filename);
        if (!isset($pathinfo['extension'])) {
            return null;
        }

        switch (strtolower($pathinfo['extension'])) {
            case 'xlsx': // Excel (OfficeOpenXML) Spreadsheet
            case 'xlsm': // Excel (OfficeOpenXML) Macro Spreadsheet (macros will be discarded)
            case 'xltx': // Excel (OfficeOpenXML) Template
            case 'xltm': // Excel (OfficeOpenXML) Macro Template (macros will be discarded)
                return 'Xlsx';
            case 'xls': // Excel (BIFF) Spreadsheet
            case 'xlt': // Excel (BIFF) Template
                return 'Xls';
//            case 'ods': // Open/Libre Offic Calc
//            case 'ots': // Open/Libre Offic Calc Template
//                return 'Ods';
//            case 'slk':
//                return 'Slk';
//            case 'xml': // Excel 2003 SpreadSheetML
//                return 'Xml';
//            case 'gnumeric':
//                return 'Gnumeric';
//            case 'htm':
//            case 'html':
//                return 'Html';
            case 'csv':
                // Do nothing
                // We must not try to use CSV reader since it loads
                // all files including Excel files etc.
                return 'Csv';
            default:
                return null;
        }
    }

    /**
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public static function exDemo()
    {
        // 开始缓存机制 -- 在导出大数据时必须启动，目前测试效果：导出速度会变慢，但不会溢出
//        ExcelWriter::cacheByMemcache();

        // 初始化
        $filename = "demo.xls";
        $sheetName = '我的sheet';
//        $sheetName = false;
        $excelWriter = new ExcelWriter($filename, $sheetName);

        // 数据组织
        $data = [];
        $line = [
            'a' => '1',
            'b' => '2',
            'c' => '3',
            'd' => '4',
            'e' => '5'
        ];
        for ($i = 0; $i < 50; $i++) {
            if ($i > 20 && $i < 45) {
                $line['c'] = $line['c'] + 1;
            }
            $line['d'] = $line['d'] + 1;
            $data[] = $line;
        }
        $data[] = [
            'a' => '111',
            'b' => '2',
            'c' => '3',
            'd' => '4',
            'e' => '5',
            'f' => '6'
        ];

        // 每一列的值，取$data里指定的下标
        $template = [
            'a',
            'b',
            'c',
            'd',
            'e'
        ];
        // 合并行，当列的值相同 【需要合并列 => 前置合并列】
        $mergeRowIfSameCol = [
            'a' => false,
            'b' => 'a',
            'c' => 'b',
            'd' => 'c',
            'e' => 'a',

        ];
        $mergeCol = [
            'a' => 3,
            // 下标为a的占3列
            'c' => 2,
            // 下标为c的占2列
        ];

        $titleData = [
            [
                '标题1',
                '标题2',
                '标题3',
                '标题4',
                '标题5'
            ],
            [
                '标题1',
                '标题2',
                '标题33333',
                '标题4',
                '标题5'
            ],
            [
                '标题1',
                '标题2',
                [
                    '1',
                    '只有2列，则第3列为空'
                ],
                '标题4',
                '标题5'
            ],
            [
                '标题1',
                '标题2',
                [
                    '1',
                    '233',
                    '3'
                ],
                '标题44',
                [
                    '标题5_1',
                    '标题5_2'
                ]
            ],
            [
                '标题1',
                '标题2',
                [
                    '1',
                    '233',
                    '第四列溢出将不会填充',
                    '4'
                ],
                '标题44',
                "=HYPERLINK(\"http://baidu.com\", \"链接\")"
            ],
        ];
        $titleMergeRowIfSameCol = [
            0 => false,
            1 => false,
            3 => false,
            4 => false,

        ];
        $titleMergeCol = [
            0 => 3,
            // 下标为a的占3列
            2 => 2,
            // 下标为c的占2列
        ];
        $titleStyle = [
            0 => [
                'backgroundColor' => 'b4c6e7',
                'bold'            => true,
                'color'           => 'ff2500'
            ],
            1 => [
                'backgroundColor' => 'b4c6e7',
                'bold'            => true,
                'color'           => 'ff2500'
            ],
            2 => [
                'backgroundColor' => 'b4c6e7',
                'bold'            => true,
                'color'           => 'ff2500'
            ],
            3 => [
                'backgroundColor' => 'b4c6e7',
                'bold'            => true,
                'color'           => 'ff2500'
            ],
            4 => [
                'backgroundColor' => 'b4c6e7',
                'bold'            => true,
                'color'           => 'blue',
                'underline' =>true
            ],
        ];

//        $excelWriter->addData($titleData, [], [], [], []);
        $excelWriter->addData($titleData, [], $titleMergeRowIfSameCol, $titleMergeCol, $titleStyle);

        $style = [];
        $excelWriter->addData($data, $template, $mergeRowIfSameCol, $mergeCol, $style);
//        $excelWriter->addData($data, $template, $mergeRowIfSameCol, $mergeCol);


        // 写入文件
//        $savekey = '/' . ltrim('test.xls', '/');
//        $uploadDir = substr($savekey, 0, strripos($savekey, '/') + 1);
//        $fileName = substr($savekey, strripos($savekey, '/') + 1);
//        $destDir = BASE_PATH . '/public' . str_replace('/', DIRECTORY_SEPARATOR, $uploadDir) . $fileName;
//        $excelWriter->save($destDir);
        // 下载
//        return $excelWriter->download();
        // 展示表格html
        $html =  $excelWriter->show();
        $response = make(ResponseInterface::class);
        return $response
            ->withHeader('Content-Type', 'text/html; charset=UTF-8')
            ->withBody(new SwooleStream($html));

        $excelWriter->close();
    }

    /**
     * 填充数据
     * @param array $data 导出数据
     * @param array $template 数据列
     * @param array $mergeRowIfSameCol 合并行，当列的值相同 【前置条件：前一列有合并】
     * @param array $mergeCol 列合并
     * @param array $styleOption
     * @param null $excelStartRow 开始行
     */
    public function addData($data, $template = [], $mergeRowIfSameCol = [], $mergeCol = [], $styleOption = [], $excelStartRow = null)
    {
        foreach ($mergeCol as &$mc) {
            $mc--;
        }
//        if($this->writerType == 'Csv') {
//            $mergeRowIfSameCol = [];
//            $mergeCol = [];
//            $styleOption = [];
//        }

        if ($excelStartRow === null) {
            $excelStartRow = $this->lastRow;
        } else {
            $this->lastRow = $excelStartRow;
        }
        // 合并记录
        $mergeLog = [];
        // 最后合并行
        $lastMergeRow = [];
        // 等待合并列
        $waitMergeCol = [];
        // 有否输出模板
        $isNullTemplate = true;
        if ($template) {
            $isNullTemplate = false;
        }
        // excel导出类初始化
        $needMergeCol = [];
        try {
            // 遍历数据行
            $row = 0;
            foreach ($data as $line) {
                $col = 0;
                $targetCol = 0;
                if ($isNullTemplate) {
                    $lastColKey = null;
                    // 没有输出模板 -> 直接遍历数组
                    foreach ($line as $colKey => $item) {
                        $this->_buildCell($data, $row, $col, $mergeRowIfSameCol, $lastColKey, $colKey, $needMergeCol, $mergeLog, $lastMergeRow, $excelStartRow, $mergeCol, $targetCol, $waitMergeCol, $styleOption);
                        $lastColKey = $colKey;
                        $col++;
                    }
                } else {
                    $lastColKey = null;
                    // 有输出模板 -> 根据输出模板来生成
                    foreach ($template as $titleName => $titleKey) {
                        $this->_buildCell($data, $row, $col, $mergeRowIfSameCol, $lastColKey, $titleKey, $needMergeCol, $mergeLog, $lastMergeRow, $excelStartRow, $mergeCol, $targetCol, $waitMergeCol, $styleOption);
                        $lastColKey = $titleKey;
                        $col++;
                    }
                }
                $row++;
                $this->lastRow++;
            }
            // 处理等待合并队列
            foreach ($needMergeCol as $needColKey => $needOne) {
                $needRow = $needOne['row'];
                $needCol = $needOne['targetCol'];
                $theCol = $needCol + 1;
                // 需要合并列
                if (isset($mergeCol[$needColKey])) {
                    $theCol = $needCol + $mergeCol[$needColKey] + 1;
                }
//                echo 'Last Merge：', $excelStartRow+$needRow+1, ',', $needCol + 1, ',', $excelStartRow+$row, ',', $theCol, '<br>';
                // 进行合并
                self::_mergeCell($excelStartRow + $needRow + 1, $needCol + 1, $excelStartRow + $row, $theCol, $needColKey, $styleOption);
                $mergeLog[$needCol][$needRow] = $row;
                $lastMergeRow[$needCol] = $row;
                // 从等待合并队列里面：清除已合并
                unset($needMergeCol[$needColKey]);
                if (isset($waitMergeCol[$needColKey]) && $waitMergeCol[$needColKey][0] === $excelStartRow + $needRow + 1) {
                    unset($waitMergeCol[$needColKey]);
                }
            }

            // 处理等待合并列队列
            foreach ($waitMergeCol as $colKey => $one) {
                // 进行合并列
                self::_mergeCell($one[0], $one[1], $one[2], $one[3], $colKey, $styleOption);
                unset($waitMergeCol[$colKey]);
            }

        } catch (Exception $e) {
//            p($e->getMessage());
        }
    }

    /**
     * 新建单元格
     * @param $data
     * @param $row
     * @param $col
     * @param $mergeRowIfSameCol
     * @param $lastColKey
     * @param $colKey
     * @param $needMergeCol
     * @param $mergeLog
     * @param $lastMergeRow
     * @param $excelStartRow
     * @param $mergeCol
     * @param $targetCol
     * @return bool
     */
    protected function _buildCell(&$data, &$row, &$col, &$mergeRowIfSameCol, &$lastColKey, &$colKey, &$needMergeCol, &$mergeLog, &$lastMergeRow, $excelStartRow, $mergeCol, &$targetCol, &$waitMergeCol, $styleOption)
    {
        // 如果没有值，就不用写
        if (!isset($data[$row][$colKey])) {
            $item = '';
        } else if (is_numeric($data[$row][$colKey])) {
            $item = (string)$data[$row][$colKey];
        } else {
            $item = $data[$row][$colKey];
        }

        if ($mergeRowIfSameCol && isset($mergeRowIfSameCol[$colKey])) {
            $prevColKey = $mergeRowIfSameCol[$colKey];
            $lastItem = (isset($data[$row - 1][$colKey])) ? $data[$row - 1][$colKey] : '';
            $lastTargetCol = (isset($mergeCol[$lastColKey])) ? $targetCol - $mergeCol[$lastColKey] - 1 : $targetCol - 1;

            // 需要检测合并行
            // - 非第一行
            // - 当前值 等于 上一行的同一列值
            // - 当前列的最后合并列不是前一列
            // - 没有前置列需求 或者 前置列当前有合并
            if ($row > 0
                && $lastItem == $item
                && (!isset($lastMergeRow[$targetCol]) || $lastMergeRow[$targetCol] != $row)
                && (!$prevColKey || isset($needMergeCol[$prevColKey]))
            ) {
                // 需要合并
                if (!isset($needMergeCol[$colKey])) {
                    // 还没有等待合并的队列里
                    $needMergeCol[$colKey] = [
                        'row'       => $row - 1,
                        //                        'colKey' => $colKey,
                        'targetCol' => $targetCol

                    ];
                }
            } else {
                // 不需要合并行

                $excelRow = $excelStartRow + $row + 1;
                if (isset($waitMergeCol[$colKey]) && $one = $waitMergeCol[$colKey]) {
                    if ($one[2] >= $row) {
                        // 进行合并列
                        self::_mergeCell($one[0], $one[1], $one[2], $one[3], $colKey, $styleOption);
                    }
                    unset($waitMergeCol[$colKey]);
                }

                // 需要合并列
                if (isset($mergeCol[$colKey])) {
                    $theCol = $targetCol + $mergeCol[$colKey] + 1;
                    $waitMergeCol[$colKey] = [
                        $excelRow,
                        $targetCol + 1,
                        $excelRow,
                        $theCol
                    ];
                }
                // 写值
                $this->activeSheet->setCellValueByColumnAndRow($targetCol + 1, $excelRow, is_array($item) ? implode(',', $item) : $item);


                $option = $this->getCellOption($styleOption, $colKey);
                $this->_setStyle($targetCol + 1, $excelRow, null, null, $option);

                // 自动合并
                self::_mergeAuto($colKey, $needMergeCol, $mergeLog, $lastMergeRow, $mergeCol, $excelStartRow, $row, $waitMergeCol, $mergeRowIfSameCol, $styleOption);

            }
        } else {
            $excelRow = $excelStartRow + $row + 1;

            if (is_array($item)) {
                // 需要合并列 - 数组就直接填充
                if (isset($mergeCol[$colKey])) {
                    for ($mi = 0; $mi <= $mergeCol[$colKey]; $mi++) {
                        if (isset($item[$mi])) {
                            // 写值
                            $this->activeSheet->setCellValueByColumnAndRow($targetCol + $mi + 1, $excelRow, $item[$mi]);
                        }
                        $option = $this->getCellOption($styleOption, $colKey);
                        $this->_setStyle($targetCol + $mi + 1, $excelRow, null, null, $option);
                    }
                } else {
                    // 写值
                    $this->activeSheet->setCellValueByColumnAndRow($targetCol + 1, $excelRow, implode(',', $item));
                    $option = $this->getCellOption($styleOption, $colKey);
                    $this->_setStyle($targetCol + 1, $row + 1, null, null, $option);
                }
            } else {
                // 需要合并列
                if (isset($mergeCol[$colKey])) {
                    $theCol = $targetCol + $mergeCol[$colKey] + 1;
//                    echo 'Merge Col：', $excelRow, ',', $targetCol + 1, ',', $excelRow, ',', $theCol, '<br>';
                    // 进行合并
                    self::_mergeCell($excelRow, $targetCol + 1, $excelRow, $theCol, $colKey, $styleOption);
                    unset($waitMergeCol[$colKey]);
                }
                // 写值
                $this->activeSheet->setCellValueByColumnAndRow($targetCol + 1, $excelRow, $item);
                $option = $this->getCellOption($styleOption, $colKey);
                $this->_setStyle($targetCol + 1, $excelRow, null, null, $option);
            }

        }

        // 需要合并列
        if (isset($mergeCol[$colKey])) {
            $targetCol += $mergeCol[$colKey] + 1;
        } else {
            $targetCol += 1;
        }

        return true;
    }

    /**
     * 合并单元格
     * @param $row1
     * @param $col1
     * @param $row2
     * @param $col2
     */
    protected function _mergeCell($row1, $col1, $row2, $col2, $colKey, $styleOption)
    {
        try {
            $this->activeSheet->mergeCellsByColumnAndRow($col1, $row1, $col2, $row2);
            $option = $this->getCellOption($styleOption, $colKey);
            $this->_setStyle($col1, $row1, $col2, $row2, $option);

        } catch (Exception $e) {
        }

    }

    protected function getCellOption($styleOption = [], $colKey)
    {
        if (isset($styleOption[$colKey])) {
            return $styleOption[$colKey];
        }

        return [];
    }

    /**
     * 设置样式
     * @param $col
     * @param $row
     * @param null $columnIndex2
     * @param null $row2
     * @param array $option
     */
    public function _setStyle($col, $row, $columnIndex2 = null, $row2 = null, $option = [])
    {
//        echo $col, ',', $row, ',', $columnIndex2, ',', $row2, '<br>';
        $styleArray = $this->buildStyle($option);
        $s = $this->activeSheet->getStyleByColumnAndRow($col, $row, $columnIndex2, $row2);
//        $s->getAlignment()->setWrapText(true);
        $s->applyFromArray($styleArray);
    }

    public function buildStyle($option = [])
    {
        $font = $this->font;
        if (isset($option['size']) && $option['size']) {
            $font['size'] = $option['size'];
        }
        if (isset($option['color']) && $option['color']) {
            $font['color']['rgb'] = $option['color'];
        }
        if (isset($option['underline']) && $option['underline']) {
            $font['underline'] = $option['underline'];
        }
        if (isset($option['bold']) && $option['bold']) {
            $font['bold'] = true;
        }
        if (isset($option['fontType']) && $option['fontType']) {
            $font['name'] = $option['fontType'];
        }
        $fill = $this->fill;
        if (isset($option['backgroundColor']) && $option['backgroundColor']) {
            $fill['startColor']['rgb'] = $option['backgroundColor'];
            $fill['fillType'] = Fill::FILL_SOLID;
        }

        $styleArray = [
            'alignment'    => isset($option['alignment']) ? $option['alignment'] : $this->align,
            'borders'      => isset($option['borders']) ? $option['borders'] : $this->borders,
            'numberFormat' => isset($option['numberFormat']) ? $option['numberFormat'] : $this->numberFormat,
            'fill'         => $fill,
            'font'         => $font
        ];
        return $styleArray;
    }

    /**
     * 自动合并
     * - 合并指定列key
     * - 自动合并前置列为当前指定列的相关单元格
     * @param $needColKey
     * @param $needMergeCol
     * @param $mergeLog
     * @param $lastMergeRow
     * @param $mergeCol
     * @param $excelStartRow
     * @param $row
     */
    protected function _mergeAuto($needColKey, &$needMergeCol, &$mergeLog, &$lastMergeRow, $mergeCol, $excelStartRow, $row, &$waitMergeCol, $mergeRowIfSameCol, $styleOption)
    {
        if (isset($needMergeCol[$needColKey])) {
            $needOne = $needMergeCol[$needColKey];
            $needRow = $needOne['row'];
            $needCol = $needOne['targetCol'];
            $theCol = $needCol + 1;
            // 需要合并列
            if (isset($mergeCol[$needColKey])) {
                $theCol = $needCol + $mergeCol[$needColKey] + 1;
            }
//            echo 'Merge Auto：', $excelStartRow+$needRow+1, ',', $needCol + 1, ',', $excelStartRow+$row, ',', $theCol, '<br>';
            // 合并单元格
            self::_mergeCell($excelStartRow + $needRow + 1, $needCol + 1, $excelStartRow + $row, $theCol, $needColKey, $styleOption);
            // 记录合并历史
            $mergeLog[$needCol][$needRow] = $row;
            // 记录最后合并行
            $lastMergeRow[$needCol] = $row;
            // 从等待合并队列里面：清除已合并
            unset($needMergeCol[$needColKey]);
            if (isset($waitMergeCol[$needColKey]) && $waitMergeCol[$needColKey][0] === $excelStartRow + $needRow + 1) {
                unset($waitMergeCol[$needColKey]);
            }
            // 如果等待合并队列里 存在 当前列为前提的 合并
            foreach ($mergeRowIfSameCol as $nextColKey => $prevColKey) {
                if ($prevColKey == $needColKey) {
//                    echo 'Auto Next：', $nextColKey, ' - Prev:', $needColKey, '<br>';
                    self::_mergeAuto($nextColKey, $needMergeCol, $mergeLog, $lastMergeRow, $mergeCol, $excelStartRow, $row, $waitMergeCol, $mergeRowIfSameCol, $styleOption);
                }
            }
        }
    }

    public function download(?string $filename = null)
    {
        if (!$filename) {
            $filename = $this->filename;
        }
        $filename = pathinfo($filename)['basename'];

        // Auto fit column width if needed
        if (ExcelWriter::$autoSize) {
            $this->autoFitColumnWidthToContent();
        }

        // Create writer
        $writer = IOFactory::createWriter($this->spreadsheet, $this->writerType);

        // Prepare headers
        switch ($this->writerType) {
            case 'Xlsx':
                $contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                break;
            case 'Xls':
                $contentType = 'application/vnd.ms-excel    ';
                break;
            case 'Csv':
                $contentType = 'application/csv';
                $writer->setUseBOM(true);
                break;
            default:
                throw new InvalidArgumentException("Unsupported writer type: {$this->writerType}");
        }

        // Set headers
        $response = make(ResponseInterface::class);
        return $response->withHeader('content-description', 'File Transfer')
            ->withHeader('content-type', $contentType)
            ->withHeader('content-disposition', "attachment; filename={$filename}; filename*=UTF-8''" . rawurlencode($filename))
            ->withHeader('content-transfer-encoding', 'binary')
            ->withHeader('pragma', 'public')
            ->withHeader('Cache-Control', 'max-age=0')
            ->withHeader('Access-Control-Expose-Headers', 'Content-Disposition')
            ->withBody(new SwooleStream($this->getExcelOutput($writer)));
//        return $response
//            ->withHeader('Content-Type', $contentType)
//            ->withHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
//            ->withHeader('Cache-Control', 'max-age=0')
//            ->withBody(new SwooleStream($this->getExcelOutput($writer)));
    }

    function autoFitColumnWidthToContent()
    {
        $maxCol = $this->activeSheet->getColumnDimension($this->activeSheet->getHighestColumn())->getColumnIndex();

        for ($i = 'A', $iNo = 1; $i <= $maxCol; $i++, $iNo++) {
            $this->activeSheet->getColumnDimension($i)->setAutoSize(true);
        }
        $this->activeSheet->calculateColumnWidths();
    }

    protected function getExcelOutput($writer): string
    {
        ob_start();
        $writer->save('php://output');
        return ob_get_clean();
    }

    /**
     * 写入文件
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function save($filename = null)
    {
        if (!$filename) {
            $filename = $this->filename;
        }
        ExcelWriter::$autoSize && $this->autoFitColumnWidthToContent();
        $writer = IOFactory::createWriter($this->spreadsheet, $this->writerType);
        $writer->save($filename);
        unset($writer);
    }

    /**
     * 启动缓存机制
     * - 全局
     * - 在导出大数据时必须启动，目前测试效果：导出速度会变慢，但不会溢出
     */
//    public static function cacheByMemcache()
//    {
//        try {
//            set_time_limit(0);
//            $client = new \Memcache();
//            $client->connect('localhost', 11211);
//            $pool = new MemcacheCachePool($client);
//            $simpleCache = new SimpleCacheBridge($pool);
//            \PhpOffice\PhpSpreadsheet\Settings::setCache($simpleCache);
//        } catch (\Exception $e) {
//
//        }
//    }
    /**
     * 关闭并清空缓存
     */
    public function close()
    {
        if ($this->isClose) return false;
        $this->isClose = true;
        $this->spreadsheet->disconnectWorksheets();
        unset($this->spreadsheet);
        unset($writer);
    }

    /**
     * 简单导出方法
     * @param string $filename 文件名
     * @param array $data 导出数据
     * @param array $template 数据列
     * @param array $mergeRowIfSameCol 合并行，当列的值相同 【前置条件：前一列有合并】
     * @param array $mergeCol 列合并
     * @param null $excelStartRow 开始行
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public static function ex($filename, $data, $template = [], $mergeRowIfSameCol = [], $mergeCol = [], $excelStartRow = null)
    {
        // 初始化
        $excelWriter = new ExcelWriter($filename);
        // 填充数据
        $excelWriter->addData($data, $template, $mergeRowIfSameCol, $mergeCol, $excelStartRow);
        // 存储
        $excelWriter->save();
        // 关闭释放缓存
        $excelWriter->close();
    }


    public function show()
    {
        ExcelWriter::$autoSize && $this->autoFitColumnWidthToContent();
        $writer = new HtmlWriter($this->spreadsheet);

        $html = '';
//        $html .= $writer->generateHTMLHeader();
        $html .= '<div class="excelTable">';
        $html .= $writer->generateStyles();
//        $html .= $writer->generateSheetData(); // all
        $html .= $writer->generateSheetDataBySheetObject($this->activeSheet); // One Sheet
        $html .= '</div>';
//        $html .= $writer->generateHTMLFooter();
        return $html;
    }

    public function setWrapText($pCellCoordinate)
    {
        $this->activeSheet->getStyle($pCellCoordinate)->getAlignment()->setWrapText(true);
    }

    function addSheet($sheetName)
    {
        $sheet = $this->spreadsheet->createSheet();
        $sheet->setTitle($sheetName);
        $this->spreadsheet->setActiveSheetIndexByName($sheetName);
        $this->activeSheet = $this->spreadsheet->getActiveSheet();
        $this->gotoTargetSheet($sheetName);
    }

    public function gotoTargetSheet($sheetName)
    {
        $this->activeSheet = $this->spreadsheet->getSheetByName($sheetName);
        $this->lastRow = 0;
        if ($this->activeSheet == null) {
            $this->activeSheet = $this->spreadsheet->createSheet();
            $this->activeSheet->setTitle($sheetName);
        }
        return $this;
    }

    function changeToSheet($sheetName)
    {
        $this->activeSheet = $this->spreadsheet->getSheetByName($sheetName);
        if ($this->activeSheet == null) {
            throw new Exception("不存在的工作簿");
        }
    }

    function setColumnWidthFromList($list)
    {
        foreach ($list as $column => $width) {
            $this->setColumnWidth($column, $width);
        }
    }

    function setColumnWidth($column, $width)
    {
        $this->activeSheet->getColumnDimensionByColumn($column)->setWidth($width);
    }
    /**
     * 下载
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
//    public function download($filename = null)
//    {
//        if (!$filename) {
//            $filename = $this->filename;
//        }
//        $filename = pathinfo($filename)['basename'];
//        ExcelWriter::$autoSize && $this->autoFitColumnWidthToContent();
//        if (ob_get_contents()) {
//            ob_end_clean();
//        }
//        $writer = IOFactory::createWriter($this->spreadsheet, $this->writerType);
//        switch ($this->writerType) {
//            case 'Xlsx':
//                header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
//                header('Content-Disposition: attachment;filename="' . $this->filename . '"');
//                header('Cache-Control: max-age=0');
//                break;
//            case 'Xls':
//                header('Content-Type: application/vnd.ms-excel');
//                header('Content-Disposition: attachment;filename="' . $this->filename . '"');
//                header('Cache-Control: max-age=0');
//                break;
//            case 'Csv':
//                header('Content-Type: application/download');
//                header("Content-type:text/csv;");
//                header("Content-Disposition:attachment;filename=" . $this->filename);
//                header('Cache-Control:must-revalidate,post-check=0,pre-check=0');
//                header('Expires:0');
//                header('Pragma:public');
//                $writer->setUseBOM(true);
//                break;
//        }
//
//        $writer->save('php://output');
//        unset($writer);
//    }

//    public function download($filename = null)
//    {
//        if (!$filename) {
//            $filename = $this->filename;
//        }
//        $filename = pathinfo($filename)['basename'];
//        ExcelWriter::$autoSize && $this->autoFitColumnWidthToContent();
//
//        switch ($this->writerType) {
//            case 'Xlsx':
//                $contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
//                break;
//            case 'Xls':
//                $contentType = 'application/vnd.ms-excel';
//                break;
//            case 'Csv':
//                $contentType = 'application/csv';
//                break;
//            default:
//                throw new \Exception('Unsupported writer type');
//        }
//
//        $response = make(ResponseInterface::class);
//        return $response->withHeader('content-description', 'File Transfer')
//            ->withHeader('content-type', $contentType)
//            ->withHeader('content-disposition', "attachment; filename={$filename}; filename*=UTF-8''" . rawurlencode($filename))
//            ->withHeader('content-transfer-encoding', 'binary')
//            ->withHeader('pragma', 'public')
//            ->withBody(new SwooleStream($this->show()));
//        $response = $response->withHeader('Content-Type', $contentType)
//            ->withHeader('Content-Disposition', 'attachment;filename="' . $filename . '"')
//            ->withHeader('Cache-Control', 'max-age=0')
//            ->withBody(new SwooleStream($this->show()));
//
//        return $response;
//    }


    public function stringFromIndex($index)
    {
        return Coordinate::stringFromColumnIndex($index);
    }

    public function getImageObject()
    {
        $drawing = new Drawing();
        return $drawing;
    }

    public function clearSameCol()
    {
        $this->sameCol = [];
        return $this;
    }

    public function insertData($rows, $option = [])
    {
        $mergeCols = isset($option['mergeCols']) ? $option['mergeCols'] : [];
        $mergeRows = isset($option['mergeRows']) ? $option['mergeRows'] : [];
        $lastRow = isset($option['lastRow']) ? $option['lastRow'] : $this->lastRow;
        $style = isset($option['style']) ? $option['style'] : [];
        $height = isset($option['height']) ? $option['height'] : null;

        $sameCol = $this->sameCol;
        foreach ($rows as $rowIndex => $row) {
            $lastRow++;
            $col = 1;
            $colRaw = 0;
            foreach ($row as $key => $item) {
                $colTip = $col - 1;
                $colNew = $col + 1;

                // 数组自动转字符串
                if (is_array($item)) {
                    $item = implode(',', $item);
                }

                // 记录相同列 下标-值-终止行-起始行
                if (isset($sameCol[$colTip][$item][$lastRow])) {

                } else if (isset($sameCol[$colTip][$item][$lastRow - 1])) {
                    $sameCol[$colTip][$item][$lastRow] = $sameCol[$colTip][$item][$lastRow - 1];
                    unset($sameCol[$colTip][$item][$lastRow - 1]);
                } else {
                    $sameCol[$colTip][$item][$lastRow] = $lastRow;
                }


                $itemStyle = isset($style[$key]) ? $style[$key] : [];

                if (isset($mergeCols[$colRaw]) && $mergeCols[$colRaw] > 1) {
                    $this->mergeCells($col - 1, $lastRow, $col + $mergeCols[$colRaw] - 2, $lastRow);
                    $colNew = $col + $mergeCols[$colRaw];
                    $this->_setStyle($col, $lastRow, $col + $mergeCols[$colRaw] - 1, $lastRow, $itemStyle);
                } else {
                    $this->_setStyle($col, $lastRow, null, null, $itemStyle);
                }

                // 写值
                $this->activeSheet->setCellValueByColumnAndRow($col, $lastRow, $item);


                $col = $colNew;
                $colRaw++;
            }

            if (isset($height[$rowIndex]) && $height[$rowIndex]) {
                $this->setRowHeight($lastRow, $height[$rowIndex]);
            }
        }

        if ($mergeRows) {
            foreach ($mergeRows as $needMergeRowCol => $prevMergeCol) {
                if (!isset($sameCol[$needMergeRowCol])) {
                    continue;
                }
                if ($prevMergeCol || $prevMergeCol == '0') {
                    if (!isset($sameCol[$prevMergeCol])) {
                        continue;
                    }

                    foreach ($sameCol[$prevMergeCol] as $prevItem) {
                        foreach ($prevItem as $prevEndRow => $prevStartRow) {
                            foreach ($sameCol[$needMergeRowCol] as $item) {
                                foreach ($item as $endRow => $startRow) {
                                    if ($startRow > $prevEndRow) {
                                        continue;
                                    }
                                    if ($endRow < $prevStartRow) {
                                        continue;
                                    }
                                    $min = $startRow > $prevStartRow ? $startRow : $prevStartRow;
                                    $max = $endRow < $prevEndRow ? $endRow : $prevEndRow;
                                    $this->mergeCells($needMergeRowCol, $min, $needMergeRowCol, $max);
                                }
                            }
                        }
                    }
                } else {
                    foreach ($sameCol[$needMergeRowCol] as $item) {
                        foreach ($item as $endRow => $startRow) {
                            $this->mergeCells($needMergeRowCol, $startRow, $needMergeRowCol, $endRow);
                        }
                    }
                }
            }
        }

        $this->lastRow = $lastRow;
        $this->sameCol = $sameCol;

    }

    public function mergeCells($columnIndex1, $row1, $columnIndex2, $row2)
    {
        $this->activeSheet->mergeCellsByColumnAndRow($columnIndex1 + 1, $row1, $columnIndex2 + 1, $row2);
    }

    public function setRowHeight($row, $height)
    {
        $this->activeSheet->getRowDimension($row)->setRowHeight($height);
        return $this;
    }

    public function merge($mergeRows)
    {
        $sameCol = $this->sameCol;
        $this->mergeBySameColumn($sameCol, $mergeRows);
    }

    protected function mergeBySameColumn(&$sameCol, $mergeRows)
    {

        foreach ($sameCol as $col => $group) {
            if (isset($mergeRows[$col])) {
                $preCol = $mergeRows[$col];
                if ($preCol !== false) {
                    if (!isset($sameCol[$preCol])) continue;
                }
                foreach ($sameCol[$col] as $name => $nameGroup) {
                    foreach ($nameGroup as $endRow => $startRow) {
                        $this->mergeRange($col, $startRow, $endRow, $sameCol, $preCol, $name);
                    }
                }
            }
        }
    }

    protected function mergeRange($col, $startRow, $endRow, &$sameCol, $preCol, $name)
    {
        if ($startRow < $endRow) {
            if ($preCol !== false) {
                foreach ($sameCol[$preCol] as $preName => $preNameGroup) {
                    foreach ($preNameGroup as $preEndRow => $preStartRow) {
                        if ($startRow > $preEndRow) {
                            break;
                        }
                        if ($endRow < $preStartRow) {
                            continue;
                        }

                        if ($startRow != $preStartRow || $endRow != $preEndRow) {
                            if (isset($sameCol[$col][$name][$endRow])) {
                                unset($sameCol[$col][$name][$endRow]);
                            }
                        }

                        if ($startRow <= $preStartRow) {
                            if ($startRow < $preStartRow) {
                                // 补充一个新的区间
                                $sameCol[$col][$name][$preStartRow - 1] = $startRow;

                                $this->mergeRange($col, $startRow, $preStartRow - 1, $sameCol, $preCol, $name);
                            }
                            if ($endRow > $preEndRow) {
                                // 补充一个新的区间
                                $sameCol[$col][$name][$preEndRow] = $preStartRow;
                                $this->mergeCells($col, $preStartRow, $col, $preEndRow);
                                // 补充一个新的区间
                                $sameCol[$col][$name][$endRow] = $preEndRow + 1;
                                $this->mergeRange($col, $preEndRow + 1, $endRow, $sameCol, $preCol, $name);
                            } else {
                                // 补充一个新的区间
                                $sameCol[$col][$name][$preEndRow] = $preStartRow;
                                $this->mergeCells($col, $preStartRow, $col, $preEndRow);
                            }
                        } else {
                            if ($endRow > $preEndRow) {
                                // 补充一个新的区间
                                $sameCol[$col][$name][$preEndRow] = $startRow;
                                $this->mergeCells($col, $startRow, $col, $preEndRow);
                                // 补充一个新的区间
                                $sameCol[$col][$name][$endRow] = $preEndRow + 1;
                                $this->mergeRange($col, $preEndRow + 1, $endRow, $sameCol, $preCol, $name);
                            } else {
                                // 补充一个新的区间
                                $sameCol[$col][$name][$preEndRow] = $startRow;
                                $this->mergeCells($col, $startRow, $col, $preEndRow);
                            }
                        }
                    }
                }
            } else {
                $this->mergeCells($col, $startRow, $col, $endRow);
            }
        }

    }

    /**
     * @param Drawing $drawing
     * @return ExcelWriter
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function insertImage($drawing)
    {
        $drawing->setWorksheet($this->activeSheet);
        return $this;
    }

    public function emptyRow($rows)
    {
        $this->lastRow += $rows;
    }

    /**
     * 列显示隐藏
     * @param $columns
     * @param $visible
     */
    public function visibleColumns($columns, $visible)
    {
        if (is_string($columns) || is_int($columns)) {
            $columns = [$columns];
        }
        foreach ($columns as $column) {
            $this->activeSheet->getColumnDimensionByColumn($column)->setVisible($visible);
        }
    }

    /**
     *
     * @param $formatCode
     * @param $columnIndex1
     * @param $row1
     * @param null $columnIndex2
     * @param null $row2
     */
    public function setNumberFormatCode($formatCode, $columnIndex1, $row1, $columnIndex2 = null, $row2 = null)
    {
        $this->activeSheet
            ->getStyleByColumnAndRow($columnIndex1, $row1, $columnIndex2, $row2)
            ->getNumberFormat()
            ->setFormatCode($formatCode);
    }

    public function getLastRow()
    {
        return $this->lastRow;
    }

    public function printA4()
    {
        // 设置页面方向为横向
        $this->activeSheet->getPageSetup()->setOrientation(PageSetup::ORIENTATION_LANDSCAPE);
        // 设置页面大小为 A4
        $this->activeSheet->getPageSetup()->setPaperSize(PageSetup::PAPERSIZE_A4);
        // 设置打印区域为整个工作表
        $this->activeSheet->getPageMargins()->setTop(0.75);
        $this->activeSheet->getPageMargins()->setRight(0.25);
        $this->activeSheet->getPageMargins()->setLeft(0.25);
        $this->activeSheet->getPageMargins()->setBottom(0.75);
        $this->activeSheet->getPageSetup()->setFitToWidth(1);
    }

    public function activeSheet()
    {
        return $this->activeSheet;
    }
}