<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/21 下午4:51
 * <AUTHOR> X
 * @Description
 */

namespace App\Core\Services\Index;

use App\Core\Utils\Tree;
use App\Model\TchipBi\AuthGroupModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\IndexFastEntranceModel;
use App\Core\Services\AuthService;
use Qbhy\HyperfAuth\AuthManager;
use Hyperf\Di\Annotation\Inject;

class FastEntranceService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * @Inject()
     * @var AuthService
     */
    protected $authService;

    /**
     * @Inject()
     * @var IndexFastEntranceModel
     */
    protected $model;

    public function doEdit(int $id, array $values)
    {
        if (isset($values['sort'])) {
            $values['sort'] = (int) $values['sort'];
        }
        return parent::doEdit($id, $values); // TODO: Change the autogenerated stub
    }

    /**
     * 获取角色的列表
     * @return void
     */
    public function roleFastEntranceList($roleId = null)
    {
        if ($roleId) {
            $group = AuthGroupModel::query()->find($roleId);
            $views = $group->index_fast ?? [];
        } else {
            if ($this->authService->isSuper()) {
                $views = 'all';
            } else {
                $groups  = $this->authService->getRole();
                $views   = $groups ? array_filter(array_column($groups, 'index_fast')) : [];
                $views   = $views ? array_unique(array_merge([], ...$views)) : [];
            }
        }

        if ($views) {
            if ($views === 'all') {
                $views   = $this->model::query()->where('status', 1)->get();
                $views = $views ? $views->toArray() : [];
            } else {
                $views   = $this->model::query()->whereIn('id', $views)->where('status', 1)->orderBy('sort', 'desc')->get();
                $views   = $views ? $views->toArray() : [];
                $pids    = array_column($views, 'pid');
                $parents = $this->model::query()->whereIn('id', $pids)->where('status', 1)->orderBy('sort', 'desc')->get();
                $views   = array_merge($views, ($parents ? $parents->toArray() : []));
            }
        }

        if ($views) {
            $pids    = array_column($views, 'pid');
            $minId   = min($pids);
            $views   = make(Tree::class)->getTreeList($views, $minId, 'pid');
        }
        return $views;
    }

    public function myFastEntrance()
    {
        $uid = $this->auth->user()->getId();
        $myFast = UserModel::query()->where('id', $uid)->where('status', 1)->value('index_fast');
        $isSupter = $this->authService->isSuper($uid);

        if (empty($myFast) || !$isSupter) {
            $groups      = $this->authService->getRole($uid);
            // 角色可以显示的
            $defaultFast = $groups ? array_filter(array_column($groups, 'index_fast')) : [];
            $defaultFast = $defaultFast ? array_unique(array_merge([], ...$defaultFast)) : [];
            $myFast = !$myFast ? $defaultFast : ($isSupter ? $myFast : array_intersect($myFast, $defaultFast));
        }
        // 最多显示6个
        // $myFast  = array_splice($myFast, 0, 6);
        // 按数组顺序进行排序
        $viewsSort = array_flip($myFast);
        if ($myFast) {
            $myFast = $this->model::query()->whereIn('id', $myFast)->where('status', 1)->get();
            if ($myFast) {
                $myFast = $myFast->toArray();
                $myFast = array_column($myFast, null, 'id');
                foreach ($viewsSort as $key => &$view) {
                    if (!empty($myFast[$key])) {
                        $view = $myFast[$key];
                    } else {
                        unset($viewsSort[$key]);
                    }
                }
            } else {
                $viewsSort = [];
            }
            unset($myFast);
        }
        return !empty($viewsSort) ? array_values($viewsSort) : [];
    }

    /**
     * 树形列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return array
     */
    public function getTreeList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 0);
        $rows = $query->orderBy($sort, $order)->get();
        $rows = $rows ? $rows->toArray() : [];
        $rows = make(Tree::class)->getTreeList($rows, 0, 'pid');
        return $rows;
    }

    public function doEditMyFast($ids)
    {
        $user = UserModel::query()->find( $this->auth->user()->getId());
        if (!$user) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.User_exception'));
        }
        $ids = is_array($ids) ? $ids : explode(',', $ids);
        foreach ($ids as &$id) {
            $id = (int) $id;
        }
        $user->index_fast = $ids;
        return $user->save();

    }
}