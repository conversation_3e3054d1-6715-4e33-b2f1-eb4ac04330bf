<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/10/14 上午10:17
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipSale;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipSale\LinkageService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @ControllerNameAnnotation("销售产品管理")
 * @AutoController()
 */
class ProductController extends \App\Controller\BaseController
{
    /**
     * @Inject
     * @var \App\Core\Services\TchipSale\ErpService
     */
    protected $erpService;

    /**
     * @Inject
     * @var LinkageService
     */
    protected $service;

    /**
     * @ControllerNameAnnotation("同步ERP产品状态")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function syncErpProductStatus()
    {
        $result = $this->erpService->syncErpProductStatus();
        return $this->response->success($result);
    }

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }
}