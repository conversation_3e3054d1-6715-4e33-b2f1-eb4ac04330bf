<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/16 下午7:31
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\WorkWx;

use App\Annotation\WorkWxTokenAnnotation;
use App\Constants\AutoUserCountCode;
use App\Controller\BaseController;
use App\Core\Services\WorkWx\WorkWxApprovalService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middlewares;
use Hyperf\HttpServer\Annotation\Middleware;
use Psr\Http\Message\ResponseInterface;
use App\Annotation\ControllerNameAnnotation;

/**
 * @ControllerNameAnnotation("企微审批管理")
 * @AutoController()
 */
class ApprovalController extends BaseController
{
    /**
     * @Inject()
     * @var WorkWxApprovalService
     */
    protected $service;

}