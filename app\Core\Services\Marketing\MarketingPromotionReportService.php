<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:59
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing;

use App\Constants\CommonCode;
use App\Constants\MarketingCode;
use App\Constants\StatusCode;
use App\Core\Services\TchipBi\ProductService;
use App\Core\Services\TchipSale\LinkageService;
use App\Core\Services\TchipSale\SaleService;
use App\Core\Utils\TimeUtils;
use App\Exception\AppException;
use App\Model\Marketing\MarketingPlatform;
use App\Model\TchipBi\CategoryModel;
use App\Model\TchipBi\MarketingPlatformModel;
use App\Model\TchipBi\MarketingPromotionReportModel;
use App\Model\TchipBi\ProductModel;
use App\Model\TchipSale\ClientSaleKyptTableModel;
use App\Model\TchipSale\LinkageModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

/**
 * 品牌运营-产品销售数据
 */
class MarketingPromotionReportService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var MarketingPromotionReportModel
     */
    protected $model;

    public function getAllList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        $platformType = null;
        if (!empty($filter['platform_type'])) {
            $platformType = $filter['platform_type'];
            unset($filter['platform_type']);
        }
        if (empty($filter['platform_id']) && $platformType) {
            $platforms = MarketingPlatformModel::query()->where('type', $platformType)->get();
            $platforms = $platforms ? array_column($platforms->toArray(), null, 'id') : [];
            $filter['platform_id'] = implode(',', array_column($platforms, 'id'));
            $op['platform_id'] = 'IN';
        }
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 999);
        $rows = $query->orderBy($sort, $order)->get();

        if ($rows) {
            $rows = $rows->toArray();
            if (empty($platforms)) {
                $platformIds = array_column($rows, 'platform_id');
                $platforms = MarketingPlatformModel::query()->whereIn('id', $platformIds)->get();
                $platforms = $platforms ? array_column($platforms->toArray(), null, 'id') : [];
            }
            foreach ($rows as &$row) {
                $row['platform_name'] = $platforms[$row['platform_id']]['name'] ?? '';
            }
        }

        return $rows;
    }

    /**
     * 品牌数据统计
     * @param string $brandCode 品牌
     * @param string $date 日期范围
     * @return array 统计结果
     */
    public function brandStatistics(string $brandCode, string $date): array
    {
        $dateArr              = explode(' - ', $date);
        $filter['brand_code'] = $brandCode;
        $op                   = [];

        // 处理时间筛选逻辑
        if (count($dateArr) == 2) {
            $startDate = $dateArr[0];
            $endDate = $dateArr[1];
        } else {
            $startDate = TimeUtils::getMonthFirstday($dateArr[0]);
            $endDate = TimeUtils::getMonthFirstday($dateArr[0]);
        }

        // 构建查询参数，移除对日期的筛选
        list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 9999);

        // 在查询中加入时间筛选
        $query->whereBetween('date', [$startDate, $endDate]);

        $select = "brand_name, brand_code, date, 
           SUM(IFNULL(users_count, 0)) as users_count, 
           SUM(IFNULL(interact_count, 0)) as interact_count, 
           SUM(IFNULL(exposure_count, 0)) as exposure_count, 
           SUM(IFNULL(click_rate, 0)) as click_rate, 
           SUM(IFNULL(click_conversion_rate, 0)) as click_conversion_rate, 
           SUM(IFNULL(invest_return_rate, 0)) as invest_return_rate, 
           SUM(IFNULL(expect_amount, 0)) as expect_amount, 
           SUM(IFNULL(practical_amount, 0)) as practical_amount, 
           SUM(IFNULL(expect_display, 0)) as expect_display, 
           SUM(IFNULL(practical_display, 0)) as practical_display, 
           SUM(IFNULL(practical_click, 0)) as practical_click, 
           SUM(IFNULL(expect_relay, 0)) as expect_relay, 
           SUM(IFNULL(practical_relay, 0)) as practical_relay, 
           SUM(IFNULL(expect_invest_return, 0)) as expect_invest_return, 
           SUM(IFNULL(practical_invest_return, 0)) as practical_invest_return, 
           SUM(IFNULL(expect_users, 0)) as expect_users, 
           SUM(IFNULL(practical_users, 0)) as practical_users, 
           SUM(IFNULL(expect_interact, 0)) as expect_interact, 
           SUM(IFNULL(practical_interact, 0)) as practical_interact, 
           SUM(IFNULL(expect_exposure, 0)) as expect_exposure, 
           SUM(IFNULL(practical_exposure, 0)) as practical_exposure";
        $info   = $query->selectRaw($select)->first();
        $info   = $info ? $info->toArray() : [];

        list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 9999, $this->model);
        $query->whereBetween('date', [$startDate, $endDate]);

        $platformNum = $query->selectRaw('count(platform_id) as platform_num')->groupBy('brand_code')->value('platform_num');

        if ($info && $platformNum > 1) {
            $info['expect_relay_avg']            = number_format($info['expect_relay'] / $platformNum, 2);
            $info['practical_relay_avg']         = number_format($info['practical_relay'] / $platformNum, 2);
            $info['practical_invest_return_avg'] = number_format($info['practical_invest_return'] / $platformNum, 2);

            $totalClicks = 0;
            $totalConversions = 0;

            $clickRateAvg = 0;
            $totalExposureCount = 0;
            $clickConversionRateAvg = 0;
            $newInvestReturnRateAvg = 0;
            $newExpectInvestReturnAvg = 0;

            list($query2, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 99999999);
            $query2->whereBetween('date', [$startDate, $endDate]);

            $platforms = $query2->select('platform_id', 'practical_amount', 'expect_amount',
                'invest_return_rate', 'expect_invest_return', 'exposure_count', 'click_rate', 'click_conversion_rate', 'created_at')
                ->get();

            $totalPracticalAmount = $platforms->sum('practical_amount');
            $totalExpectAmount = $platforms->sum('expect_amount');

            foreach ($platforms as $platform) {
                $clicks = 0; // 初始化 $clicks 变量

                // 只考虑非负数
                if ($platform->click_rate >= 0 && $platform->exposure_count >= 0) {
                    $clicks = $platform->exposure_count * $platform->click_rate / 100;
                    $totalClicks += $clicks;
                    $totalExposureCount += $platform->exposure_count;
                }

                if ($platform->click_conversion_rate >= 0 && $clicks > 0) {
                    $conversions = $clicks * $platform->click_conversion_rate / 100;
                    $totalConversions += $conversions;
                }
            }

            foreach ($platforms as $platform) {
                if ($platform->invest_return_rate >= 0 && $totalPracticalAmount > 0 && $platform->practical_amount > 0) {
                    $weight = $platform->practical_amount / $totalPracticalAmount;
                    $newInvestReturnRateAvg += $weight * $platform->invest_return_rate;
                }

                if ($platform->expect_invest_return >= 0 && $totalExpectAmount > 0 && $platform->expect_amount > 0) {
                    $weightExpect = $platform->expect_amount / $totalExpectAmount;
                    $newExpectInvestReturnAvg += $weightExpect * $platform->expect_invest_return;
                }
            }

            if ($totalClicks > 0 && $totalExposureCount > 0) {
                $clickRateAvg = $totalClicks / $totalExposureCount * 100;
            }

            if ($totalClicks > 0 && $totalConversions > 0) {
                $clickConversionRateAvg = $totalConversions / $totalClicks * 100;
            }

            $info['click_rate_avg']            = number_format($clickRateAvg, 2);
            $info['click_conversion_rate_avg'] = number_format($clickConversionRateAvg, 2);
            $info['invest_return_rate_avg']    = number_format($newInvestReturnRateAvg, 2);
            $info['expect_invest_return_avg']  = number_format($newExpectInvestReturnAvg, 2);
        }

        return $info;
    }

    /**
     * 品牌数据统计
     * @param $brandCode
     * @param $date
     * @return void
     */
    public function brandReport(string $brandCode, string $date, $platformId = null)
    {
        $dateArr              = explode(' - ', $date);
        $dateArr = explode(' - ', $date);
        if (count($dateArr) == 2) {
            $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
            $date       = implode(' - ', $dateArr);
        } else {
            $month              = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0]         = $month['starttime'];
            $dateArr[1]         = $month['endtime'];
            $date               = implode(' - ', $dateArr);
        }
        $filter = [
            'date'       => $date,
            'brand_code' => $brandCode,
        ];
        $op     = [
            'date' => 'DATE',
        ];
        if ($platformId) {
            $filter['platform_id'] = $platformId;
        }

        list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 9999);
        $select = "brand_name, brand_code, date, SUM(IFNULL(users_count, 0)) as users_count, SUM(IFNULL(interact_count, 0)) as interact_count, SUM(IFNULL(exposure_count, 0)) as exposure_count, SUM(IFNULL(click_rate, 0)) as click_rate, SUM(IFNULL(click_conversion_rate, 0)) as click_conversion_rate, SUM(IFNULL(invest_return_rate, 0)) as invest_return_rate, SUM(IFNULL(expect_amount, 0)) as expect_amount, SUM(IFNULL(practical_amount, 0)) as practical_amount, SUM(IFNULL(expect_display, 0)) as expect_display, SUM(IFNULL(practical_display, 0)) as practical_display, SUM(IFNULL(practical_click, 0)) as practical_click, SUM(IFNULL(expect_relay, 0)) as expect_relay, SUM(IFNULL(practical_relay, 0)) as practical_relay, SUM(IFNULL(expect_invest_return, 0)) as expect_invest_return, SUM(IFNULL(practical_invest_return, 0)) as practical_invest_return, SUM(IFNULL(expect_users, 0)) as expect_users, SUM(IFNULL(practical_users, 0)) as practical_users, SUM(IFNULL(expect_interact, 0)) as expect_interact, SUM(IFNULL(practical_interact, 0)) as practical_interact, SUM(IFNULL(expect_exposure, 0)) as expect_exposure, SUM(IFNULL(practical_exposure, 0)) as practical_exposure";
        $rows   = $query->selectRaw($select)->groupBy('date')->orderBy('date', 'asc')->get();
        $monthNum = TimeUtils::calculateMonthDifference($dateArr[0], $dateArr[1]);
        $result = [];
        for ($i = 0; $i <= $monthNum; $i++) {
            $dateItem = $i == 0 ? date('Y-m-01', strtotime($dateArr[0])) : date('Y-m-01', strtotime("+{$i} month", strtotime($dateArr[0])));
            $dateItemText = $i == 0 ? date('m', strtotime($dateArr[0])) : date('m', strtotime("+{$i} month", strtotime($dateArr[0]))) . '月';
            $result[$dateItem] = [
                'users_count'    => 0,
                'interact_count' => 0,
                'exposure_count' => 0,
                'click_rate' => 0,
                'date'   => $dateItem,
                'date_text'   => $dateItemText,
            ];
        }

        if ($rows) {
            $rows = $rows->toArray();
            foreach ($rows as $row) {
                if (!empty($result[$row['date']])) {
                    $result[$row['date']]['users_count'] += $row['users_count'];
                    $result[$row['date']]['interact_count'] += $row['interact_count'];
                    $result[$row['date']]['exposure_count'] += $row['exposure_count'];
                    $result[$row['date']]['click_rate'] += $row['click_rate'];
                }
            }
        }
        // if ($rows) {
        //     $rows   = $rows->toArray();
        //     $platformNum = count(array_unique(array_filter(array_column($rows, 'platform_id'))));
        //     var_dump($platformNum);
        //     // 计需要的平均值
        //     foreach ($rows as &$row) {
        //         $row['click_rate_avg']              = number_format($row['click_rate'] / $platformNum, 2);
        //         $row['click_conversion_rate_avg']   = number_format($row['click_conversion_rate'] / $platformNum, 2);
        //         $row['invest_return_rate_avg']      = number_format($row['invest_return_rate'] / $platformNum, 2);
        //         $row['expect_relay_avg']            = number_format($row['expect_relay'] / $platformNum, 2);
        //         $row['practical_relay_avg']         = number_format($row['practical_relay'] / $platformNum, 2);
        //         $row['expect_invest_return_avg']    = number_format($row['expect_invest_return'] / $platformNum, 2);
        //         $row['practical_invest_return_avg'] = number_format($row['practical_invest_return'] / $platformNum, 2);
        //     }
        //
        // }
        return $result;
    }

    /**
     * 各月份平台推广数据
     * @param $brandCode
     * @param $date
     * @return array
     */
    public function monthPlatformPromotion($brandCode, $date, $platformId = null)
    {
        $dateArr = explode(' - ', $date);
        if (count($dateArr) == 2) {
            $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
            $date       = implode(' - ', $dateArr);
        } else {
            $month      = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0] = $month['starttime'];
            $dateArr[1] = $month['endtime'];
            $date       = implode(' - ', $dateArr);
        }
        $filter = [
            'brand_code' => $brandCode,
            'date'       => $date,
        ];
        $op     = [
            'date' => 'DATE',
        ];

        $platformFilter = ['status' => 1];
        if ($platformId) {
            $filter['platform_id'] = $platformId;
            $op['platform_id']     = 'IN';
            $platformFilter['id']  = $platformId;
        }
        $salePlatforms = make(MarketingPlatformService::class)->getAllList($platformFilter);
        if (!$salePlatforms) {
            return [];
        }
        $platformNum = count(array_unique(array_filter(array_column($salePlatforms, 'id'))));

        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 99999);
        $rows = $query->get();

        $monthNum = TimeUtils::calculateMonthDifference($dateArr[0], $dateArr[1]);
        $result   = [];
        for ($i = 0; $i <= $monthNum; $i++) {
            $dateItem          = $i == 0 ? date('Y-m-01', strtotime($dateArr[0])) : date('Y-m-01', strtotime("+{$i} month", strtotime($dateArr[0])));
            $dateItemText      = $i == 0 ? date('m', strtotime($dateArr[0])) : date('m', strtotime("+{$i} month", strtotime($dateArr[0]))) . '月';
            $result[$dateItem] = [
                'users_count'           => 0,
                'interact_count'        => 0,
                'exposure_count'        => 0,
                'click_rate'            => 0,
                'click_conversion_rate' => 0,
                'invest_return_rate'    => 0,
                'date'                  => $dateItem,
                'date_text'             => $dateItemText,
            ];
            // 生成各平台的字段
            foreach ($salePlatforms as $platform) {
                $result[$dateItem]['users_count_' . $platform['id']]           = 0;
                $result[$dateItem]['interact_count_' . $platform['id']]        = 0;
                $result[$dateItem]['exposure_count_' . $platform['id']]        = 0;
                $result[$dateItem]['click_rate_' . $platform['id']]            = 0;
                $result[$dateItem]['click_conversion_rate_' . $platform['id']] = 0;
                $result[$dateItem]['invest_return_rate_' . $platform['id']]    = 0;
            }
        }

        if ($rows) {
            $rows = $rows->toArray();
            foreach ($rows as $row) {
                if (!empty($result[$row['date']])) {
                    $result[$row['date']]['users_count']                                  += $row['users_count'];
                    $result[$row['date']]['interact_count']                               += $row['interact_count'];
                    $result[$row['date']]['exposure_count']                               += $row['exposure_count'];
                    $result[$row['date']]['click_rate']                                   += $row['click_rate'];
                    $result[$row['date']]['click_conversion_rate']                        += $row['click_conversion_rate'];
                    $result[$row['date']]['invest_return_rate']                           += $row['invest_return_rate'];
                    $result[$row['date']]['users_count_' . $row['platform_id']]           += $row['users_count'];
                    $result[$row['date']]['interact_count_' . $row['platform_id']]        += $row['interact_count'];
                    $result[$row['date']]['exposure_count_' . $row['platform_id']]        += $row['exposure_count'];
                    $result[$row['date']]['click_rate_' . $row['platform_id']]            += $row['click_rate'];
                    $result[$row['date']]['click_conversion_rate_' . $row['platform_id']] += $row['click_conversion_rate'];
                    $result[$row['date']]['invest_return_rate_' . $row['platform_id']]    += $row['invest_return_rate'];
                }
            }
        }
        // 计算平均点击转发，投资回投
        foreach ($result as &$res) {
            $res['click_conversion_rate'] = number_format($res['click_conversion_rate'] / $platformNum, 2);
            $res['invest_return_rate']    = number_format($res['invest_return_rate'] / $platformNum, 2);
        }
        return array_values(collect($result)->sortByDesc('date')->toArray());
    }

    public function getBrandPromtion($brandCode, $date) : array
    {
        $brand    = $this->model::query()->where('brand_code', $brandCode)->where('date', $date)->first();
        $platform = MarketingPlatformModel::query()->where('status', 1)->orderBy('type', 'asc')->get();
        $result   = [];
        if ($platform) {
            $platform         = $platform->toArray();
            $platformIds      = array_column($platform, 'id');
            $promotionService = make(MarketingPromotionReportService::class);
            $promotionRows    = $promotionService->getAllList(['platform_id' => implode(',', $platformIds), 'date' => $date, 'brand_code' => $brandCode], ['platform_id' => 'IN']);
            $promotionRows    = $promotionRows ? array_column($promotionRows, null, 'platform_id') : [];


            foreach ($platform as $key => &$item) {
                $result[$key] = [
                    'brand_name'    => $brand->brand_name ?? '',
                    'brand_code'    => $brandCode,
                    'platform_id'   => $item['id'],
                    'platform_name' => $item['name'],
                    'date'          => $date,
                    'platform_type' => $item['type'],
                ];
                if (!empty($promotionRows[$item['id']])) {
                    $result[$key]['id'] = $item['id'];
                    $result[$key]['users_count']           = $promotionRows[$item['id']]['users_count'];
                    $result[$key]['interact_count']        = $promotionRows[$item['id']]['interact_count'];
                    $result[$key]['exposure_count']        = $promotionRows[$item['id']]['exposure_count'];
                    $result[$key]['click_rate']            = $promotionRows[$item['id']]['click_rate'];
                    $result[$key]['click_conversion_rate'] = $promotionRows[$item['id']]['click_conversion_rate'];
                    $result[$key]['invest_return_rate']    = $promotionRows[$item['id']]['invest_return_rate'];
                    $result[$key]['stars']                 = $promotionRows[$item['id']]['stars'];
                } else {
                    $promotionService->initData($result[$key]);
                }
            }
        }
        return $result;
    }

    public function saveBrandPromtion(array $items)
    {
        if (count($items) <= 0) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
        }
        Db::beginTransaction();
        try {
            foreach ($items as $item) {
                $condition = [
                    'brand_code' => $item['brand_code'],
                    'platform_id' => $item['platform_id'],
                    'date' => $item['date'],
                ];
                $this->model::updateOrCreate($condition, $item);
            }
            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
        return true;
    }

    public function doEdit(int $id, array $values)
    {
        if ($id <= 0) {
            // 先判断该月的数据是否已存在
            if ($this->model::query()->where('brand_code', $values['brand_code'])->where('platform_id', $values['platform_id'])->where('date', $values['date'])->first()) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.Data_is_exist'));
            }
        }
        return parent::doEdit($id, $values); // TODO: Change the autogenerated stub
    }

    /**
     * 根据品牌，平台，月份创建或更新数据
     * @param string $brandCode
     * @param string $platformId
     * @param string $date
     * @param array $values
     * @return MarketingProductMonthReportModel|\Hyperf\Database\Model\Model
     */
    public function doEditFilter(string $brandCode, string $platformId, string $date, array $values)
    {
        $filter = [
            'brand_code' => $brandCode,
            'platform_id' => $platformId,
            'date' => $date,
        ];
        return $this->model::updateOrCreate($filter, $values);
    }

    public function saveReport($brandCode = 'station', $date = null)
    {
        $brandProducts = ProductModel::query()->selectRaw('id, IFNULL(product_sale_name, product_name) as product_name')->where('brand', $brandCode)
            ->where('status', 1)
            ->where('is_marketing', 1)
            ->get();
        $brandProducts = $brandProducts ? $brandProducts->toArray() : [];
        if (count($brandProducts) <= 0) return false;
        $brandProductNames = array_column($brandProducts, 'product_name');
        $brandProducts     = array_column($brandProducts, null, 'product_name');
        $saleProducts      = make(LinkageService::class)->getProductsByNames($brandProductNames);
        if (!$saleProducts) return false;
        // 默认统计上一个月的
        $date    = $date ?: date('Y-m-01', strtotime("-1 month", time()));
        $nowDate = TimeUtils::getMonthFirstandLast($date);
        foreach ($saleProducts as $productItem) {
            $amount = make(SaleService::class)->statisticsMoneyByDate($nowDate['starttime'], $nowDate['endtime'], null, ['prod_id' => $productItem['id']]);
            $volume = make(SaleService::class)->statisticsVolumeByDate($nowDate['starttime'], $nowDate['endtime'], null, ['prod_id' => $productItem['id']]);
            $save   = [
                'brand_name'  => CategoryModel::query()->where('type', 'compay_brand')->where('keywords', $brandCode)->value('name'),
                'brand_code'  => $brandCode,
                'product_id'  => $brandProducts[$productItem['text']]['id'] ?? 0,
                'date'        => $date,
                'sale_amount' => $amount,
                'sale_volume' => $volume,
            ];
            $filter = [
                'brand_code' => $brandCode,
                'product_id' => $save['product_id'],
                'date'       => $date,
            ];
            $this->model::updateOrCreate($filter, $save);
        }
        return true;
    }

    public function getAssignSummay($brandCode, $date)
    {
        $dateArr              = explode(' - ', $date);
        $filter['brand_code'] = $brandCode;
        $op                   = [];
        if (count($dateArr) == 2) {
            $filter['date'] = $date;
            $op['date']     = 'DATE';
        } else {
            $filter['date'] = TimeUtils::getMonthFirstday($dateArr[0]);
        }
        $fields = ['users_count', 'interact_count', 'exposure_count', 'click_rate', 'click_conversion_rate', 'invest_return_rate'];
        list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 9999);
        $rows = $query->select(array_merge(['platform_id'], $fields))->orderBy('date', 'desc')->get();
        $result = [];
        if ($rows) {
            $rows = $rows->toArray();
            $platformIds = array_filter(array_column($rows, 'platform_id'));
            foreach ($rows as $row) {
                $month = date('Y-m', strtotime($row['date']));
                if (empty($result[$month])) {
                    $result[$month]['month'] = $month;
                    foreach ($platformIds as $platformId) {
                        foreach ($fields as $field)
                        $result[$month][$platformId.'_'.$field] = 0;
                    }
                }
                if (!empty($row[$field])) {
                    $result[$month][$row['platform_id'].'_'.$field] = $row[$field];
                }
            }
        }
        return $result;
    }

    // /**
    //  * 保存
    //  * @return void
    //  */
    // public function saveSalePlatformStatisticsReport($brandCode = CommonCode::BRAND_STATIONPC, $date = null) :bool
    // {
    //     $salePlatforms = MarketingPlatform::query()->selectRaw('id, name, IFNULL(sales_name, name) as sales_name, type, status')
    //         ->where('type', MarketingCode::PLATFORM_TYPE_SALE)->where('status', 1)->get();
    //     if (!$salePlatforms) return false;
    //     $salePlatforms = $salePlatforms->toArray();
    //     $platformNames = array_column($salePlatforms->toArray(), 'sales_name');
    //     $salePlatforms = array_column($salePlatforms, null, 'sales_name');
    //
    //     $salesLinkage = LinkageModel::query()->whereIn('text', $platformNames)->get();
    //     if (!$salesLinkage) return false;
    //     $salesLinkage = $salesLinkage->toArray();
    //     $salesIds = array_column($salesLinkage, 'id');
    //     $salesLinkage = array_column($salesLinkage, null, 'text');
    //     $products = make(ProductService::class)->getBrandMarketProducts($brandCode);
    //     if (!$products) return false;
    //
    //     $date = $date ? : TimeUtils::getPreviousMonth();
    //     $nowDate = TimeUtils::getMonthFirstandLast($date);
    //     $brandName = CategoryModel::query()->where('type', 'compay_brand')->where('keywords', $brandCode)->value('name');
    //     foreach ($salesLinkage as $key => $saleItem) {
    //         if (empty($salePlatforms[$key])) continue;
    //         $filter = [
    //             'prod_id' => implode(',', $products),
    //             'sale_id' => $saleItem['id'],
    //         ];
    //         $amount = make(SaleService::class)->statisticsMoneyByDate($nowDate['starttime'], $nowDate['endtime'], null, $filter, ['prod_id' => 'IN']);
    //         $volume = make(SaleService::class)->statisticsVolumeByDate($nowDate['starttime'], $nowDate['endtime'], null, $filter, ['prod_id' => 'IN']);
    //         $save = [
    //             'brand_code' => $brandCode,
    //             'brand_name' => $brandName,
    //             'platform_id' => $salePlatforms[$key]['id'],
    //
    //         ];
    //     }
    // }

    public function initData(&$data)
    {
        $data['users_count']             = 0;
        $data['interact_count']          = 0;
        $data['exposure_count']          = 0;
        $data['click_rate']              = 0;
        $data['click_conversion_rate']   = 0;
        $data['invest_return_rate']      = 0;
        $data['expect_amount']           = 0;
        $data['practical_amount']        = 0;
        $data['expect_display']          = 0;
        $data['practical_display']       = 0;
        $data['expect_click']            = 0;
        $data['practical_click']         = 0;
        $data['expect_relay']            = 0;
        $data['practical_relay']         = 0;
        $data['expect_invest_return']    = 0;
        $data['practical_invest_return'] = 0;
        $data['expect_users']            = 0;
        $data['practical_users']         = 0;
        $data['expect_interact']         = 0;
        $data['practical_interact']      = 0;
        $data['expect_exposure']         = 0;
        $data['practical_exposure']      = 0;
        $data['stars']                   = 0;
    }


}