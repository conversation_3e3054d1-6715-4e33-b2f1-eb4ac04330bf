<?php
declare(strict_types=1);
namespace App\Controller\TchipBi;

use App\Core\Services\TchipBi\ProductService;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\Di\Annotation\Inject;

/**
 * @AutoController()
 */
class ProductController extends \App\Controller\BaseController
{
    /**
     * @Inject
     * @var ProductService
     */
    public $service;

    public function saveProductItems()
    {
        $items = $this->request->input('items');
        return $this->response->success($this->service->saveProductItems($items));
    }

}
