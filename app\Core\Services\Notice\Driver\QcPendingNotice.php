<?php 
namespace App\Core\Services\Notice\Driver;

use App\Core\Services\Notice\NoticeService;
use App\Core\Services\UserService;
use Hyperf\Di\Annotation\Inject;
use App\Core\Services\Notice\Driver\NoticeHandlerInterface;
use App\Model\TchipBi\UserModel;

class QcPendingNotice implements NoticeHandlerInterface
{
    /**
     * @Inject()
     * @var UserService
     */
    protected $userService;

    /**
     * @Inject()
     * @var NoticeService
     */
    protected $noticeService;

    public function handle(...$arguments)
    {
        [$qc] = $arguments;
        $users = $qc['followers'];
        $userInfo   = UserModel::query()->where('id', $qc['examine_user'])->first()->toArray();

        foreach ($users as $user) {
            $params = [
                'order_no' => $qc['order_no'], // 单号
                'prod_code' => $qc['prod_code'], // 料号
                'prod_name' => $qc['prod_name'], // 产品名
                'prod_spec' => $qc['prod_spec'], // 规格
                'result_remark' => $qc['result_remark'], // 检验结果
                'updated_at' => $qc['updated_at'], // 时间
                'examine_user' => $userInfo['name'] ?? '未知', // 检验人
            ];
            $this->noticeService->handleNotice('qcPending', $user, $params);
        }

        return true;
    }
}