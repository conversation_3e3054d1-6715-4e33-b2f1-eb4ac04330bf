<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/5/9 下午5:18
 * <AUTHOR>
 * @Description
 */


namespace App\Controller\TchipSale\Erp;

use App\Annotation\ControllerNameAnnotation;
use App\Constants\StatusCode;
use App\Core\Services\TchipSale\Erp\OrderPohService;
use App\Exception\AppException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;


/**
 * @ControllerNameAnnotation("ERP接口采购订单主表")
 * @AutoController()
 */
class OrderPohController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var OrderPohService
     */
    protected $service;

    public function overView()
    {
        $id = $this->request->input('id');
        $result = $this->service->overView($id);
        return $this->response->success($result);
    }

}