<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\OaProductBorrowService;
use App\Request\TchipOa\OaProductBorrow\ErpBorrowImportRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("产品借用管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OaProductBorrowController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var OaProductBorrowService
     */
    protected $service;

    public function getSpStatusList()
    {
        $result = $this->service->getSpStatusList();
        return $this->response->success($result);
    }

    public function getBorrowStatusList()
    {
        $result = $this->service->getBorrowStatusList();
        return $this->response->success($result);
    }

    public function overViewOneDetails()
    {
        $id = $this->request->input('id');
        $dId = $this->request->input('d_id');
        $result = $this->service->getOverViewOneDetails($id, $dId); // TODO: Change the autogenerated stub
        return $this->response->success($result);
    }

    public function getReport()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getReport($filter, $op);
        return $this->response->success($result);
    }

    public function getReportDetails()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getReportDetails($filter, $op);
        return $this->response->success($result);
    }

    public function migrateToDetails()
    {
        $result = $this->service->migrateToDetails();
        return $this->response->success($result);
    }

    public function erpBorrowImport(ErpBorrowImportRequest $request)
    {
        $validated = $request->validated();
        $result = $this->service->erpBorrowImport($validated['loc_name']);
        return $this->response->success($result);
    }
}