<?php

namespace App\Controller\WorkWx;

use App\Controller\BaseController;
use App\Core\Services\WorkWx\WorkWxApprovalService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;

/**
 * @AutoController()
 */
class OaTemplateController extends BaseController
{
    /**
     * @Inject()
     * @var WorkWxApprovalService
     */
    protected $workWxApprovalService;

    public function getTemplateDetail()
    {
        $templateId = $this->request->input('template_id');
        $result = $this->workWxApprovalService->getTemplateDetail($templateId);
        return $this->response->success($result);
    }
}