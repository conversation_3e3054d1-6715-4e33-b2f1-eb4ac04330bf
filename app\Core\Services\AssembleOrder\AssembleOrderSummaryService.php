<?php

namespace App\Core\Services\AssembleOrder;

use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Exception\AppException;
use App\Model\TchipBi\AssembleOrderModel;
use App\Model\TchipBi\AssembleOrderSummaryModel;
use App\Model\TchipBi\AttachmentModel;
use App\Model\TchipBi\UserModel;
use Exception;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class AssembleOrderSummaryService extends BusinessService
{
    /**
     * @Inject()
     * @var AssembleOrderSummaryModel
     */
    protected $model;

    public function getOverView($id, $assemble_order_id = 0)
    {
        $query = $this->model->query();
        if ($id) {
            $query->where('id', $id);
        } else {
            $query->where('assemble_order_id', $assemble_order_id);
        }

        $overView =  $query->first();
        $overView = $overView?$overView->toArray():[];
        if($overView){
            $order = AssembleOrderModel::query()->where('id', $overView['assemble_order_id'])->first();
            $overView['order_code'] = $order?$order->code:'';
            $assembleUser = UserModel::find($overView['assemble_user_id']);
            $testUser = UserModel::find($overView['test_user_id']);
            $overView['assemble_user_name'] = $assembleUser?$assembleUser->name:'';
            $overView['test_user_name'] = $testUser?$testUser->name:'';
            $overView['assemble_time_text'] = $overView['assemble_time']?date('Y-m-d H:i', strtotime($overView['assemble_time'])):'';
            $overView['test_time_text'] = $overView['test_time']?date('Y-m-d H:i', strtotime($overView['test_time'])):'';
            $overView['assemble_attachments'] = empty($overView['assemble_attachment_ids']) ? [] : AttachmentModel::query()->whereIn('id', $overView['assemble_attachment_ids'])->get()->toArray();
            $overView['test_attachments'] = empty($overView['test_attachment_ids']) ? [] : AttachmentModel::query()->whereIn('id', $overView['test_attachment_ids'])->get()->toArray();
        }
        return $overView;
    }

    public function doEdit(int $id, array $values)
    {
        Db::beginTransaction();
        try {
            if ($id > 0) {
                $row = $this->model::query()->find($id);
            } elseif (!empty($values['assemble_order_id'])) {
                $row = $this->model::query()->where('assemble_order_id', $values['assemble_order_id'])->first();
            }
            if ($row) {
                $result = $row->update($values);
            } else {
                if (empty($values['assemble_order_id'])) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, '参数错误');
                }
                $result = $this->model::query()->create($values);
            }
            //更新info的总结状态
            if (isset($values['status'])) {
                $infoEditData = [
                    'assemble_order_id'   => $values['assemble_order_id'],
                    'summary_finish_status' => $values['status']
                ];
                make(AssembleOrderInfoService::class)->doEdit(0, $infoEditData);
            }
            Db::commit();
            return $result;
        } catch (Exception $e) {
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }
}