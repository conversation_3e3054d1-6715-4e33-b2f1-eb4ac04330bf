<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/13 下午3:17
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller;

use App\Core\Services\BusinessService;
use App\Core\Services\Curd\IService;
use App\Core\Utils\Response;
use App\Model\Model;
use App\Request\IdsRequest;
use Hyperf\Database\Model\Builder;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\MenuMiddleware;

class BaseController extends AbstractController
{
    /**
     * @Inject()
     * @var Response
     */
    protected $response;

    /**
     * @Inject()
     * @var RequestInterface
     */
    protected $request;

    /**
     * @var \Hyperf\Database\Model\Model
     */
    protected $model;

    /**
     * @var BusinessService
     */
    protected $service;

    /**
     * @Middleware(MenuMiddleware::class)
     */
    public function overView()
    {
        $id = $this->request->input('id');
        $result = $this->service->getOverView($id);
        return $this->response->success($result);
    }

    /**
     * @Middleware(MenuMiddleware::class)
     */
    public function getList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
        return $this->response->success($result);
    }

    public function getAllList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getAllList($filter, $op, $sort, $order);
        return $this->response->success($result);
    }

    public function getTree()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getTree($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    public function detail()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->detail($filter, $op);
        return $this->response->success($result);
    }

    /**
     * @Middleware(MenuMiddleware::class)
     */
    public function doEdit()
    {
        $id = (int) $this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->doEdit($id, $params);
        return $this->response->success($result);
    }

    /**
     * @Middleware(MenuMiddleware::class)
     */
    public function doDelete()
    {
        $validated = make(IdsRequest::class)->validated();
        $result = $this->service->doDelete($validated['ids']);
        return $this->response->success($result);
    }

    /**
     * @Middleware(MenuMiddleware::class)
     */
    public function doMulti()
    {
        $ids = $this->request->input('ids', 0);
        $params = $this->request->input('params');
        $result = $this->service->doMulti($ids, $params);
        return $this->response->success($result);
    }

    /**
     * 构建查询所需参数
     * @return array
     */
    protected function getParams(): array
    {
        $filter        = $this->request->input('filter');
        $op            = $this->request->input('op');
        $sort          = $this->request->input('sort', 'id');
        $order         = $this->request->input('order', 'DESC');
        $limit         = $this->request->input('limit', '10');
        $search        = $this->request->input('search');
        $search_fields = $this->request->input('search_fields');
        if (!is_array($filter) && !is_array($op)) {
            $filter = json_decode($filter, true);
            $op = json_decode($op, true);
        }
        return [(array)$filter, (array)$op, $sort, $order, $limit, (array)$search, (array)$search_fields];
    }

    // /**
    //  * 构建查询所需条件、排序方式
    //  * @param Builder $query
    //  * @return array
    //  */
    // public function buildparams(Builder $query)
    // {
    //     $filter = $this->request->input('filter');
    //     $op = $this->request->input('op');
    //     $sort = $this->request->input('sort', 'id');
    //     $order = $this->request->input('order', 'DESC');
    //     $limit = $this->request->input('limit', '10');
    //
    //     if (!is_array($filter) && !is_array($op)) {
    //         $filter = json_decode($filter, true);
    //         $op = json_decode($op, true);
    //     }
    //     if ($filter) {
    //         foreach ($filter as $k => $v) {
    //             if (is_array($v)) {
    //                 $query = $query->whereHas($k, function ($query) use ($v, $op, $k) {
    //                     foreach ($v as $whereHasK => $whereHasV) {
    //                         if ($whereHasV) {
    //                             $sym = $op[$k][$whereHasK] ?? '=';
    //                             $query = $this->buildWhere($query, $whereHasK, $sym, $whereHasV);
    //                         }
    //                     }
    //                 });
    //             } else {
    //                 if ($v) {
    //                     $sym = $op[$k] ?? '=';
    //                     $query = $this->buildWhere($query, $k, $sym, $v);
    //                 }
    //
    //             }
    //         }
    //     }
    //
    //     return [$query, $limit, $sort, $order];
    // }
    //
    // public function buildWhere($query, $k, $sym, $v)
    // {
    //     switch ($sym) {
    //         case '=':
    //         case '<>':
    //             $query = $query->where($k, $sym, $v);
    //             break;
    //         case 'LIKE':
    //         case 'NOT LIKE':
    //         case 'LIKE %...%':
    //         case 'NOT LIKE %...%':
    //             $query = $query->where($k, $sym, "%{$v}%");
    //             break;
    //         case 'BETWEEN':
    //         case 'NOT BETWEEN':
    //             $v = array_slice(explode('-', $v), 0, 2);
    //             $query = $query->whereBetween($k, $v);
    //             break;
    //         default:
    //             break;
    //     }
    //     return $query;
    // }
}