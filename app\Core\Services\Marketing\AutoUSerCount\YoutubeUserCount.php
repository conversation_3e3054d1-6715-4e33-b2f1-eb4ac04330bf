<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/10 下午2:38
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing\AutoUSerCount;

use QL\Ext\Chrome;
use QL\QueryList;
use Swoole\Runtime;

class YoutubeUserCount implements AutoUserCountInterface
{

    public function getUserCount($channelId, $spaceUrl): int
    {
        Runtime::enableCoroutine(false);
        $ql = QueryList::getInstance();
        // 注册插件，默认注册的方法名为: chrome
        $ql->use(Chrome::class);
        $ql->chrome(function ($page, $browser) use ($spaceUrl) {
            $page->setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36');
            $page->goto($spaceUrl, [
                'waitUntil' => 'networkidle2'
            ]);
            $html = $page->content();
            $browser->close();
            return $html;
        }, [
            'executablePath' => '/usr/bin/chromium-browser',
            'args'           => [
                '--no-sandbox', '--headless', '--disable-gpu',
                '--proxy-server=socks5://172.16.0.253:7891']
        ]);
        $text = $ql->find('#subscriber-count')->html();
        return intval($text);
    }
}