<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/4
 * <AUTHOR>
 * @Description
 */

namespace App\Controller\Setting;

use App\Annotation\ControllerNameAnnotation;
use App\Constants\StatusCode;
use App\Core\Services\Setting\AttachmentService;
use App\Exception\AppException;
use App\Middleware\AuthMiddleware;
use App\Model\TchipBi\AttachmentModel;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use Monolog\Logger;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("附件管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class AttachmentController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var AttachmentService
     */
    protected $service;

    /**
     * @return ResponseInterface
     */
    public function upload()
    {
        $file = $this->request->file('file');
        if (!$file) {
            return $this->response->error(__('common.Missing_parameter'));
        }
        $attachment = $this->service->upload($file);
        return $this->response->success($attachment);
    }

    /**
     * @return ResponseInterface
     */
    public function download()
    {
        $id = $this->request->input('id', 0);
        $path = $this->request->input('path', '');
        if($id){
            $attachment = AttachmentModel::query()->find($id);
            if (!$attachment) {
                return $this->response->error(__('common.No_results_were_found'));
            }
            $filePath =  BASE_PATH . '/public' . $attachment['url'];
            $fileName = $attachment['filename'];
        }elseif ($path){
            $filePath = BASE_PATH . '/public' . $path;
            $fileName = basename($path);
        }else{
            return $this->response->error(__('common.Missing_parameter'));
        }
        return $this->response->download($filePath,$fileName);
    }
}