<?php

    declare(strict_types=1);
    /**
     * This file is part of Hyperf.
     *
     * @link     https://www.hyperf.io
     * @document https://hyperf.wiki
     * @contact  <EMAIL>
     * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
     */

    namespace App\Controller;

    use App\Constants\ProductCode;
    use App\Core\Services\Marketing\ChannelService;
    use App\Core\Services\Marketing\MarketingPlatformMonthReportService;
    use App\Core\Services\Marketing\MarketingProductService;
    use App\Core\Services\Marketing\MarketingPromotionReportService;
    use App\Core\Services\MpWx\MpWxArticleService;
    use App\Core\Services\Product\ProductProgressService;
    use App\Core\Services\Product\ProductService;
    use App\Core\Services\Project\ProjectService;
    use App\Core\Services\Project\ProjectsProgressService;
    use App\Core\Services\Redmine\AccountService;
    use App\Core\Services\Project\MemberService;
    use App\Core\Services\Project\CategoryService;
    use App\Core\Services\TchipBbs\UserService;
    use App\Core\Services\TchipSale\LinkageService;
    use App\Core\Services\WorkWx\WorkWxApprovalService;
    use App\Core\Utils\Random;
    use App\Core\Utils\TimeUtils;
    use App\Mail\ProductMail;
    use App\Mail\Redmine\ProductEditMail;
    use App\Model\Redmine\EmailAddressModel;
    use App\Model\Redmine\ProductModel;
    use App\Model\Redmine\ProductMemberModel;
    use App\Model\Redmine\ProjectsProgressModel;
    use App\Model\Redmine\ProjectsTrackerModel;
    use App\Model\Redmine\TokenModel;
    use App\Model\Redmine\UserModel;
    use Hyperf\Guzzle\ClientFactory;
    use Hyperf\HttpServer\Annotation\AutoController;
    use Hyperf\Di\Annotation\Inject;
    use App\Model\TchipSale;
    use App\Model\TchipBi;
    use App\Core\Services\WorkWx\WorkWxMessageService;
    use App\Annotation\WorkWxTokenAnnotation;
    use App\Core\Services\TchipSale\SaleService;
    use HyperfExt\Mail\Mail;
    use League\HTMLToMarkdown\HtmlConverter;
    use App\Model\Redmine\ProductProgressModel;
    use App\Core\Services\Queue\Redmine\ProductProgressWxQueue;
    use App\Core\Services\Queue\Redmine\ProductEditQueue;
    use Poliander\Cron\CronExpression;

    /**
     * @AutoController()
     * @WorkWxTokenAnnotation(type="agent_tchip_bi")
     */
    class IndexController extends BaseController
    {
        /**
         * @Inject
         * @var ClientFactory
         */
        protected $clientFactory;

        /**
         * @Inject
         * @var MemberService
         */
        public $service;

        /**
         * @Inject
         * @var SaleService
         */
        protected $saleService;

        /**
         * @Inject()
         * @var WorkWxMessageService
         */
        public $workWxMessageService;

        public function index()
        {
            $user = $this->request->input('user', 'Hyperf');
            $method = $this->request->getMethod();

            $result = [
                'method'  => $method,
                'message' => "Hello {$user}.",
            ];
            return $this->response->success($result);
        }

        public function test()
        {
            return make(\App\Core\Services\TchipSale\ProductService::class)->updateProductOutStockDay();
            $d1 ='2024-06-01';
            $d2 ='2024-06-30';
            $diff = TimeUtils::calculateMonthDifference($d1, $d2) + 1;
            $diff = $diff == 0 ? 1 : $diff;
            return [$diff, date('Y-m-d', strtotime("-{$diff} month", strtotime($d1))), date('Y-m-d', strtotime("-{$diff} month", strtotime($d2)))];
            return make(MarketingPlatformMonthReportService::class)->brandStatisticsSales('stationpc', '2024-04-01 - 2024-06-30');
            return make(MarketingPromotionReportService::class)->getBrandStatistics('stationpc', '2024-06-01');
            return make(MarketingProductService::class)->saveStatisticsProductSales('station', '2024-06-01');
            // return make(\App\Core\Services\Project\MemberService::class)->getProjectMembers('41');
            return make(\App\Core\Services\Project\MemberService::class)->getProjectFirstMembers('41', 4);
            $array1 = array(1,2,3,4,5);
            $array2 = array(1,2,3,4,5,6);
            return array_diff($array1, $array2);

            return make(ProductService::class)->migrateMembersToProject();
            return make(ProjectsProgressService::class)->migrateSaleFollowDetailsToProjectProgress();
            // make(\App\Core\Services\Product\ProductService::class)->removeMultiDescValue();
            // return 1;
            $m = <<<EOZ
<table><tr><td>123</td><td>456</td></tr><tr><td>abc</td><td>def</td></tr></table>
<a href="http://www.t-firefly.com">firefly</a>
EOZ;

            return $this->workWxMessageService->sendText($m, 'XiaoJiaJie', '', '');
            $productId = 351;
            if (!empty($productId)) {
                $product = ProductModel::query()->find($productId);
                $memberIds = ProductMemberModel::query()->where('product_id', $productId)->where('mail_notification', 1)->pluck('user_id')->toArray();
                if ($product && $memberIds) {
                    $users = \App\Model\TchipBi\UserModel::query()->select('user.*')->join('user_third', 'user.id', '=', 'user_third.user_id')
                        ->whereIn('user_third.third_user_id', $memberIds)->where('user.status', 1)->get();

                    foreach ($users as &$user) {
                        $user->product_name = $product->name;
                        $mail = make(ProductEditMail::class, [$user]);
                        $result = Mail::to($user->biz_mail)->send($mail);
                    }
                }
            }
            return 1;
            $user = UserModel::query()->first();
            $user->product_name = '测试产品';
            //发邮件
            $mail = make(ProductEditMail::class, [$user]);
            $result = Mail::to('<EMAIL>')->send($mail);
            return $result;
            $arr = [
                ['id' => 1,'name' => 'n1'],
                ['id' => 2,'name' => 'n2'],
            ];
            $arr = array_column($arr, null, 'id');
            return $arr;
            $week = date('N',1666575631);
            $week = reportPeriod(date('Y-m-d', 1666575631));
            return $week;
            $d = '[10]';
            $d = json_decode($d, true);;
            return $d;
            $a = ['a','b','c'];
            return date('Y', strtotime('2022-11-26')) - date('Y', strtotime('2022-11-02'));
            $d = '000138';
            return $d;
            return $d[strlen($d) - 1];
            $monthTarget = $this->saleService->getAchievement($this->saleService::TYPE_MONTH, date('Y-m-d'));
            $saleCount   = $this->saleService->statisticsMoneyByDate(TimeUtils::getMonthFirstday(), TimeUtils::getMonthLastday());

            $sale = $saleCount / $monthTarget;
            return [$saleCount, $monthTarget, $sale * 100];
            $arr = '';
            $d['value'] .= " -'1'";
            preg_match_all('/\'[0-9]*\'/', $d['value'], $arr);
            // $d = explode('- \'', $d['value']);
            $arr = !empty($arr[0]) ? $arr[0] : [];
            foreach ($arr as &$a) {
                $a = (int) str_replace('\'', '', $a);
            }
            return $arr;
            // $result = $this->service->memberRoleRow(3, 23);
            // $result = $this->service->getProductByMaterial('PAIO3359JD043210');
            // return $result;
            $newProjectsTrackerModel = make(ProjectsTrackerModel::class);
            // $clientFactory = make(ClientFactory::class);
            // $mpwxArticle = new MpWxArticleService($clientFactory);
            // $result = $mpwxArticle->getUserRead();
            // return $this->response->success($result);
            $channelService = make(ChannelService::class);
            $result = $channelService->collectUserCount();
            return $this->response->success($result);
        }

        public function redmineAccount()
        {
            $result = make(AccountService::class)->myAccount();
            return $this->response->success($result);
        }

        public function addRedmineAccount()
        {
            $user['login'] = 'test2';
            $user['salt'] = uniqid();
            $user['hashed_password'] = md5("${user['salt']}123456");
            $user['firstname'] = 'test';
            $user['lastname'] = '2';
            $user['type'] = 'User';

            $result = UserModel::query()->updateOrCreate(['login' => $user['login']], $user);

            $result['email'] = EmailAddressModel::query()->updateOrCreate(['user_id' => $result['id']], [
                'address'    => '<EMAIL>',
                'is_default' => 1,
                'notify'     => 0
            ]);

            $result['token'] = TokenModel::query()->updateOrCreate(['user_id' => $result['id']],
                [
                    'action' => 'api',
                    'value'  => Random::alnum(40)
                ]
            );
            return $this->response->success($result);

        }

        protected function request($url, $method = 'get', $param = [])
        {
            $options = [
                'base_uri' => env('SALE_ERP_API')
            ];
            $client = $this->clientFactory->create($options);
            $client->request($method, $url, $param);
        }

        protected function getMillisecond() {
            list($s1, $s2) = explode(' ', microtime());
            return (float)sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000);
        }

        protected function sendProgress()
        {
            $progress = ProductProgressModel::query()->with(['product', 'details'])->where('id', 2151)->first();
            $progress = $progress ? $progress->toArray() : [];
            if ($progress) {
                $workWxMessageService = make(WorkWxMessageService::class);
                $progress['workwx_user'] = !is_array($progress['workwx_user']) ? json_decode($progress['workwx_user'], true) : $progress['workwx_user'];
                $workwxUser = \App\Model\TchipBi\UserModel::query()->select('user.*')->join('user_third', 'user.id', '=', 'user_third.user_id')
                    ->whereIn('user_third.third_user_id', $progress['workwx_user'])->where('user_third.platform', 'redmine')
                    ->where('user.status', 1)->get();
                $description = $progress['description'] ?? '';
                $htmlToMarkDown = make(HtmlConverter::class);
                $isImg = false;
                if (strpos($description, '<img') !== false) {
                    $isImg = true;
                }
                // 正则除去图片
                $description = pregRemoveContent($description, '/<img.*?(?:>|\/>)/');

                // 旧html保存方式去掉
                if (hasHtmlTags($description)) {
                    $description = $htmlToMarkDown->convert($description);
                    $descriptionArr = explode(PHP_EOL, $description);
                    $description = '';
                    foreach ($descriptionArr as $ar) {
                        $stripAr = strip_tags($ar);
                        $description .= <<<EOT
    {$stripAr} \n
    
    EOT;
                    }
                }

                if ($isImg) {
                    $description .= "\n <font color=\"warning\">内容含有图片请登陆系统查看。</font>\n";
                }

                $title = $progress['type_text'];

                // 版本号
                $versionText = '';
                if (!empty($progress['version'])) {
                    $versionText = "前版本号:{$progress['version_pre']} \n";
                    $versionText .= "版本号:{$progress['version']} \n";
                }
                if (!empty($progress['modify_at'])) {
                    $versionText .= "下单时间: {$progress['modify_at']} \n";
                }
                $versionText = $versionText ? "{$versionText} \n" : $versionText;

                // 是否有修改属性
                $detailText = '';
                if (!empty($progress['details'])) {

                    // 是否修改标题
                    $propKey = array_column($progress['details'], 'prop_key');
                    if (array_intersect(ProductCode::DESC_FIELD, $propKey)) {
                        $title = '上线信息';
                    }

                    foreach ($progress['details'] as $detail) {
                        // 判断details类型是attr还是json
                        switch ($detail['property']) {
                            case 'json' :
                                if (!empty($detail['diff_value_text'][0]) && is_array($detail['diff_value_text'][0])) {
                                    foreach ($detail['diff_value_text'][0] as $dkey => &$dval) {
                                        if ($dval['text'] != $detail['diff_value_text'][1][$dkey]['text']) {
                                            // $detailText .= "{$dval['name']} 文案 由  <font color=\"info\">{$dval['text']}</font>  变更为 {$detail['diff_value_text'][1][$dkey]['text']}  \n";
                                            $detailText .= "{$dval['name']}更新为 {$detail['diff_value_text'][1][$dkey]['text']}  \n";
                                        }
                                        if ($detail['diff_value_text'][1][$dkey]['url']) {
                                            // $detailText .= "{$dval['name']} 地址 由  <font color=\"info\">{$dval['url']}</font>  变更为 {$detail['diff_value_text'][1][$dkey]['url']}  \n";
                                            $detailText .= "访问地址{$detail['diff_value_text'][1][$dkey]['url']}  \n";
                                        }
                                        // if (!empty($dval['ext']['color']) && $dval['ext']['color'] != $detail['diff_value_text'][1][$dkey]['ext']['color']) {
                                        //     $detailText .= "{$dval['name']} 颜色 作出了修改  \n";
                                        // }
                                    }
                                }
                                break;
                            default:
                                $detailText .= "属性 {$detail['prop_name']} 由  <font color=\"info\">{$detail['diff_value_text'][0]}</font>  变更为 {$detail['diff_value_text'][1]}  \n";

                        }
                        // $valText = "<font color=\"warning\">{$detail['value_text']}</font>";
                        // if (strpos($detail['prop_key'], '_link') > 0) {
                        //     $valText = "[{$detail['value_text']}]({$detail['value_text']})";
                        // }

                        // 如果是带属性地址的属性需要添加上
                        // $propKey = $detail['prop_key'] . '_link';
                        // if (!empty($product->$propKey)) {
                        //     $content .= "相关地址 [{$product->$propKey}]('.{$product->$propKey}.')  \n";
                        // }
                    }
                }


                // 地址
                $url = biFrontendHost() . '/project/productDetailsIndex?product_id='.$progress['product_id'];
                foreach ($workwxUser as &$workwx) {
                    $userName = $workwx['name'];
                    $content = <<<EOT
**{$progress['product']['name']} 有新的动态：**\n
{$versionText}
{$description}
{$detailText}
[点击查看详情]({$url})
EOT;
                    //var_dump($content);
                    $workWxMessageService->sendMarkdown($content, 'XiaoJiaJie', '', '');
                }
            }

        }

    }
