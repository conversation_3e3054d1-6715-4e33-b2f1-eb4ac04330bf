<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/4
 * <AUTHOR>
 * @Description
 */

namespace App\Controller\Setting;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Setting\CategoryService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("分类管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class CategoryController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var CategoryService
     */
    protected $service;

    public function tree()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getTree($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    public function lists()
    {
        $type = $this->request->input('type');
        if (!$type) {
            $this->response->error('type is not null');
        }
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $sort = $this->request->input('sort', 'sort');
        $order = $this->request->input('order', 'DESC');
        $result = $this->service->lists($type, $filter, $op, $sort, $order);
        return $this->response->success($result);
    }
}