<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/19 下午3:25
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\MpWx;

class MpWxUserService extends \App\Core\Services\MpWx\MpWxBaseService
{
    /**
     * 获取用户增减数据
     * @return mixed
     */
    public function getUserSummary($mp = 'stationpc')
    {
        $response = $this->sendRequest('datacube/getusersummary', [
            'query' => [
                'access_token' => getCache($this->mpList($mp)['redis_key']),
            ],
            'json'  => [
                'begin_date' => '2022-05-12',
                'end_date'   => '2022-05-18'
            ]
        ], 'post');
        return $response;
    }

    /**
     * 获取累计用户数据
     * @return mixed
     */
    public function getUserCumulate($mp = 'stationpc', $beginDate = '2022-08-01', $endDate = '2022-08-05')
    {
        $response = $this->sendRequest('datacube/getusercumulate', [
            'query' => [
                'access_token' => getCache($this->mpList($mp)['redis_key']),
            ],
            'json'  => [
                'begin_date' => $beginDate,
                'end_date'   => $endDate
            ]
        ], 'post');
        return $response;
    }
}