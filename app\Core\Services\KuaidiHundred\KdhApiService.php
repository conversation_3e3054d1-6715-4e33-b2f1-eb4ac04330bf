<?php

namespace App\Core\Services\KuaidiHundred;

use Hyperf\Di\Annotation\Inject;

class KdhApiService extends KdhBaseService
{

    /**
     *  post提交数据
     * @param  string $url 请求Url
     * @param  array $datas 提交的数据
     * @return url响应返回的html
     */
    public function detocr($datas)
    {
        $result = $this->sendPost('elec/detocr', ['param' => $datas]);
        return $result;
    }

    public function query($datas)
    {
        $datas['customer'] = env('KDH_CUSTOMER');
        $result = $this->sendPost('elec/query', ['param' => $datas]);
        return $result;
    }

}