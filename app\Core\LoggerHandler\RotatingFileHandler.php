<?php declare(strict_types=1);

namespace App\Core\LoggerHandler;

use InvalidArgumentException;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Monolog\Utils;


class RotatingFileHandler extends \Monolog\Handler\RotatingFileHandler
{
    const dateDirFormat = "Y-m";

    protected function getTimedFilename(): string
    {
        $fileInfo      = pathinfo($this->filename);
        $timedFilename = str_replace(
            ['{filename}', '{date}'],
            [$fileInfo['filename'], date($this->dateFormat)],
            $fileInfo['dirname'] . '/' . $this->getDateDir() . "/" . $this->filenameFormat
        );

        if (isset($fileInfo['extension'])) {
            $timedFilename .= '.' . $fileInfo['extension'];
        }

        return $timedFilename;
    }

    protected function getDateDir($refresh = false)
    {
        return date(self::dateDirFormat);
    }

    // protected $currentDateDir="";

    protected function getGlobPattern(): string
    {
        $fileInfo = pathinfo($this->filename);
        $glob     = str_replace(
            ['{filename}', '{date}'],
            [$fileInfo['filename'], '[0-9][0-9][0-9][0-9]*'],
            $fileInfo['dirname'] . '/' . $this->getDateDir() . "/" . $this->filenameFormat
        );
        if (isset($fileInfo['extension'])) {
            $glob .= '.' . $fileInfo['extension'];
        }
        return $glob;
    }


}
