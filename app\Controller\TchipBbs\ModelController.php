<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/28 上午10:30
 * <AUTHOR> X
 * @Description
 */

namespace App\Controller\TchipBbs;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipBbs\ModelService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("文化文章模型")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ModelController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ModelService
     */
    protected $service;

    public function getList()
    {
        return parent::getList(); // TODO: Change the autogenerated stub
    }
}