<?php

namespace App\Controller\WorkWx;

use App\Core\Utils\Log;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use App\Controller\AbstractController;
use \App\Core\Services\WorkWx\WorkWxJsSdkService;
use App\Annotation\WorkWxTokenAnnotation;
use App\Core\Utils\Response;

/**
 * 企业微信
 * @AutoController()
 * @WorkWxTokenAnnotation(type="agent_tchip_bi")
 */
class JssdkController extends AbstractController
{
    /**
     * @Inject()
     * @var Response
     */
    protected $response;

    /**
     * @Inject()
     * @var WorkWxJsSdkService
     */
    protected $service;

    public function getConfigSignature()
    {
        $url = $this->request->input('url');

        return $this->response->success($this->service->getConfigSignature($url));
    }
}