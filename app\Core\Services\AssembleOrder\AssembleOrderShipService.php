<?php

namespace App\Core\Services\AssembleOrder;

use App\Constants\AssembleOrderCode;
use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Exception\AppException;
use App\Model\TchipBi\AssembleOrderInfoModel;
use App\Model\TchipBi\AssembleOrderShipModel;
use Exception;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class AssembleOrderShipService extends BusinessService
{
    /**
     * @Inject()
     * @var AssembleOrderShipModel
     */
    protected $model;

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        return parent::getList($filter, $op, $sort, $order, $limit);
    }

    public function getOverView($id, $assemble_order_id = 0)
    {
        $query = $this->model->query();
        if ($id) {
            $query->where('id', $id);
        } else {
            $query->where('assemble_order_id', $assemble_order_id);
        }

        $overView = $query->first();
        $overView = $overView ? $overView->toArray() : [];
        return $overView;
    }

    /**
     * 删除
     * @param $ids
     * @param array $params
     * @return int
     */
    public function doDelete($ids, array $params = []): int
    {
        Db::beginTransaction();
        try {
            $ids = explode(',', $ids);

            //回滚状态
            $row = $this->model::query()->whereIn('id', $ids)->first();
            $this->rollbackDataStatus($row['assemble_order_id'] ?? 0);

            $result = $this->model::destroy($ids);
            Db::commit();
            return $result;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    public function rollbackDataStatus($assembleOrderId)
    {
        $info = AssembleOrderInfoModel::query()->where('assemble_order_id', $assembleOrderId)->first();
        //更新info的发货状态
        if (!empty($info['ship_data_status']) && $info['ship_data_status'] == AssembleOrderCode::DATA_STATUS_UPLOADED) {
            $infoEditData = [
                'assemble_order_id' => $assembleOrderId,
                'ship_data_status'  => AssembleOrderCode::DATA_STATUS_WAIT
            ];
            make(AssembleOrderInfoService::class)->doEdit(0, $infoEditData);
        }
    }

    public function doEdit(int $id, array $values)
    {
        Db::beginTransaction();
        try {
            if ($id > 0) {
                $row = $this->model::query()->find($id);
            }
            if ($row) {
                $result = $row->update($values);
            } else {
                if (empty($values['assemble_order_id'])) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, '参数错误');
                }
                $result = $this->model::query()->create($values);
            }
            $assembleOrderId = $row['assemble_order_id'] ?? $values['assemble_order_id'];
            $this->rollbackDataStatus($assembleOrderId);
            Db::commit();
            return $result;
        } catch (Exception $e) {
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }
}