<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date      2022/5/19 下午4:56
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Crontab;

use App\Core\Utils\Log;
use App\Exception\AppException;
use Hyperf\Di\Annotation\Inject;
use App\Annotation\CrotabLogsAnnotation;
use SplFileObject;

/**
 * runtime 日志服务
 */
class RuntimeLogsService
{
    const Logs_path = BASE_PATH . '/runtime/logs';

    /**
     * 定时器任务。 定期清理日志
     * @CrotabLogsAnnotation(type="cleanlogs")
     * @return void
     */
    public function clean()
    {
        //清理文件
        $this->cleanUpFiles(self::Logs_path, time() - 86400 * 31);
        //清理空目录
        $this->cleanUpEmptyDir(self::Logs_path);
    }

    /**
     * 循环获取日志文件
     */
    public function logFiles($path)
    {
        $result = [];
        $files  = scandir($path);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                if (is_dir($path . '/' . $file)) {
                    $result = array_merge($result, $this->logFiles($path . '/' . $file));
                } else {
                    $result[] = $path . '/' . $file;
                }
            }
        }
        return $result;
    }

    /**
     * 从第$start开始 获取 $limit条数据
     * @param $file_path
     * @param $start
     * @param $limit
     * @param $direction
     * @return string
     */
    public function catFile($file_path, $start=0, $limit=300,$direction='head' )
    {
        if($direction=='footer' && $start<$limit){
            $start=$limit;
        }

        # 从第300行开始，显示100行（即显示300~399行）
        //cat filename | tail -n +300 | head -n 100

        $direction=$direction!='head'?'-':'+';
        $n=$direction.$start;
        $handle = popen("cat $file_path | tail -n $n | head -n $limit 2>&1", 'r');
        $res='';
        while (!feof($handle)) {
            $buffer = fgets($handle);
            $res.=$buffer;
            //echo "$buffer\n";
            flush();
        }
        pclose($handle);
        return $res;
    }
    //
    ///**
    // * 获取最后Limit条数据
    // * @param $file_path
    // * @param $limit
    // * @return string
    // */
    //public function tailFile($file_path, $limit=1000)
    //{
    //    $handle = popen("tail -n $limit $file_path 2>&1", 'r');
    //    $res='';
    //    while (!feof($handle)) {
    //        $buffer = fgets($handle);
    //        $res.=$buffer;
    //        //echo "$buffer\n";
    //        flush();
    //    }
    //    pclose($handle);
    //    return $res;
    //}

    private function cleanUpFiles($dir, int $expire_time = 0)
    {
        if ($expire_time === 0) {
            $expire_time = time() - 86400 * 31;
        }
        $logs = $this->logFiles($dir);
        foreach ($logs as $file) {
            $createtime = filectime($file);
            //var_dump($file.'_'.date('Y-m-d H:i:s',$createtime));
            if ($createtime < $expire_time) {
                //if(strpos('cleanlogs',$file)!==false ) continue;
                $res = unlink($file);
                if (!$res) {
                    Log::get('system', 'error')->info('file unlink Err. filename:' . $file);
                } else {
                    Log::get('system', 'success')->info('file unlink success. filename:' . $file);
                }
                //var_dump($file.'_'.$createtime);
            }
        }
    }

    private function cleanUpEmptyDir($path)
    {
        //$this->logDirs($path);
        //var_dump($path);
        if (is_dir($path) && ($handle = opendir($path)) !== false) {
            while (($file = readdir($handle)) !== false) { // 遍历文件夹
                if ($file != '.' && $file != '..') {
                    $curfile = $path . '/' . $file; // 当前目录
                    if (is_dir($curfile)) {               // 目录
                        $this->cleanUpEmptyDir($curfile); // 如果是目录则继续遍历
                        if (count(scandir($curfile)) == 2) { // 目录为空,=2是因为. 和 ..存在
                            $res = rmdir($curfile);          // 删除空目录
                            if (!$res) {
                                Log::get('system', 'error')->info('rmdir Err. dir:' . $curfile);
                            } else {
                                Log::get('system', 'success')->info('rmdir success. dir:' . $curfile);
                            }
                        }
                    }
                }
            }
            closedir($handle);
        }
    }
}