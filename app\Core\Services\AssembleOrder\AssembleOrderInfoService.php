<?php

namespace App\Core\Services\AssembleOrder;

use App\Constants\AssembleOrderCode;
use App\Constants\ProductionCode;
use App\Constants\StatusCode;
use App\Constants\WorkFlowSceneCode;
use App\Core\Services\BusinessService;
use App\Core\Services\MacAddress\MacAddressService;
use App\Core\Services\Notice\NoticeService;
use App\Core\Services\Setting\AttachmentService;
use App\Core\Services\SnCode\SnCodeService;
use App\Core\Services\UserService;
use App\Core\Services\WorkFlow\WorkFlowService;
use App\Exception\AppException;
use App\Model\TchipBi\AssembleOrderInfoModel;
use App\Model\TchipBi\AssembleOrderModel;
use App\Model\TchipBi\MacAddressModel;
use App\Model\TchipBi\OrderMacSnBatchModel;
use App\Model\TchipBi\SnCodeModel;
use App\Model\TchipBi\WorkStatusModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use PhpOffice\PhpSpreadsheet\IOFactory;


class   AssembleOrderInfoService extends BusinessService
{
    const SCENE_TYPE = WorkFlowSceneCode::SCENE_ASSEMBLE_ORDER;
    /**
     * @Inject()
     * @var AssembleOrderInfoModel
     */
    protected $model;

    public function doEdit(int $id, array $values,$isCli=false)
    {
        DB::beginTransaction();
        try {
            $row = [];
            if ($id > 0) {
                $row = $this->model::query()->find($id);
            } elseif (!empty($values['assemble_order_id'])) {
                $row = $this->model::query()->where('assemble_order_id', $values['assemble_order_id'])->first();
            }
            $oldRow = $row ? $row->toArray() : [];
            if ($row) {//更新
                //关联mac/sn，判断是为了避免单个属性更新时再走一遍逻辑
                if (isset($values['mac_sn_attachment_id'])) {
                    $values['mac_address_range'] = $this->filterAndTrimCodeRange($values['mac_address_range'])?:null;
                    $values['sn_no_range'] = $this->filterAndTrimCodeRange($values['sn_no_range'])?:null;
                    $this->relateMacSn($row, $values);
                }
                //核准日期
                if (isset($values['approve_status']) && $values['approve_status'] != $row['approve_status']) {
                    if(!$isCli){
                        $userId = auth()->user()->getId();
                        $userInfo = make(UserService::class)->getUserInfo($userId, false);
                        if(!(in_array('Admin',$userInfo['roles']) || $userId == $row['order_user_id'])){
                            throw new AppException(StatusCode::ERR_EXCEPTION, '非订单负责人不允许修改核准状态');
                        }
                    }
                    if (in_array($values['approve_status'], AssembleOrderCode::APPROVE_STATUS_FINISHED)) {
                        $values['approve_date'] = date('Y-m-d H:i:s');
                    } else {
                        $values['approve_date'] = null;
                    }
                }
                //天启和常规备货时，发货状态自动通过
//                $changeShipmentPlace = !empty($values['shipment_place']) && $values['shipment_place'] != $row['shipment_place'];
//                $changeAssembleType = !empty($values['assemble_type']) && $values['assemble_type']!= $row['assemble_type'];
//                if($changeAssembleType || $changeShipmentPlace){
//                    $assembleType = $values['assemble_type']?? $row['assemble_type'];
//                    $shipmentPlace = $values['shipment_place']?? $row['shipment_place'];
//                    if($assembleType == AssembleOrderCode::ASSEMBLE_TYPE_COMMON && $shipmentPlace == AssembleOrderCode::SHIPMENT_PLACE_SELF){
//                        $values['ship_data_status'] = 2;
//                    }else{
//                        $values['ship_data_status'] = 1;
//                    }
//                }

                //如果首件资料都上传完成，实际上线日期为当前日期
                $assembleChanged = 0;
                $softChanged = 0;
                if(!empty($values['first_assemble_data_status']) && $values['first_assemble_data_status'] != $row['first_assemble_data_status']){
                    $assembleChanged = $values['first_assemble_data_status'];
                }
                if(!empty($values['first_soft_data_status']) && $values['first_soft_data_status']!= $row['first_soft_data_status']){
                    $softChanged = $values['first_soft_data_status'];
                }
                if($assembleChanged == 2 && $row['first_soft_data_status'] >= 2){
                    $values['actual_online_date'] = date('Y-m-d H:i:s');
                }elseif ($softChanged == 2 && $row['first_assemble_data_status'] >= 2){
                    $values['actual_online_date'] = date('Y-m-d H:i:s');
                }elseif ($assembleChanged == 2 && $softChanged ==2){
                    $values['actual_online_date'] = date('Y-m-d H:i:s');
                }elseif ($assembleChanged == 1 || $softChanged == 1){
                    $values['actual_online_date'] = null;
                }

                //如果附件状态改为上传完成同时全部文件都审核完成，则更新状态为完成
                $dataKeys = AssembleOrderCode::ATTACHMENT_STATUS_KEYS;
                $fieldKey = array_keys($dataKeys);
                //组件不需要审核，过滤掉
                $fieldKey = array_diff($fieldKey, ['assemble_data_status']);
                foreach ($fieldKey as $field) {
                    if (isset($values[$field]) && $values[$field] != $row[$field] && $values[$field] == AssembleOrderCode::DATA_STATUS_UPLOADED) {
                        if (make(AssembleOrderAttachmentService::class)->checkCategoryHasAudit($row['assemble_order_id'], $dataKeys[$field])) {
                            $values[$field] = AssembleOrderCode::DATA_STATUS_FINISHED;
                        }
                    }
                }

                $result = $row->update($values);
                //更新工作流状态
//                make(WorkFlowService::class, [self::SCENE_TYPE])->changeStatus($row['work_status_id'], $row,$isCli);
                $newRow = $this->model::query()->find($row->id);
                $this->doAfterEdit($oldRow, $newRow->toArray());
            } else {//新建基本信息
                if (empty($values['assemble_order_id'])) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, '参数错误');
                }
                //关联mac/sn，判断是为了避免单个属性更新时再走一遍逻辑
                if (isset($values['mac_sn_attachment_id'])) {
                    $values['mac_address_range'] = $this->filterAndTrimCodeRange($values['mac_address_range'])?:null;
                    $values['sn_no_range'] = $this->filterAndTrimCodeRange($values['sn_no_range'])?:null;
                    $this->relateMacSn(null, $values);
                }
                //天启出货以及常规组装不需要
//                if(!empty($values['shipment_place'])&&$values['shipment_place'] == AssembleOrderCode::SHIPMENT_PLACE_SELF){
//                    if(!isset($values['assemble_type']) || $values['assemble_type'] == AssembleOrderCode::ASSEMBLE_TYPE_COMMON){
//                        $values['ship_data_status'] = AssembleOrderCode::DATA_STATUS_UPLOADED;
//                    }
//                }

                $result = $this->model::query()->create($values);
                //获取工作流的初始状态
                $this->setFirstWorkStatus($result->id, $isCli?0:auth()->id());
//                $row = $this->model::query()->find($result->id);
//                if($values['is_complete']==1){
//                    make(WorkFlowService::class, [self::SCENE_TYPE])->changeStatus($row['work_status_id'], $row,$isCli);
//                }
                $newRow = $this->model::query()->find($result->id);
                $this->doAfterEdit($oldRow, $newRow->toArray());
            }

            Db::commit();
            return $result;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }

    }

    /**
     * 编辑后发送信息
     * @param $oldRow
     * @param $newRow
     * @return void
     */
    public function doAfterEdit($oldRow, $newRow, $isCli=false)
    {
        $workField = [
            'is_complete',
            'completeness_status',
            'assemble_data_status',
            'soft_data_status',
            'first_assemble_data_status',
            'first_soft_data_status',
            'summary_finish_status',
            'product_finished_data_status',
            'product_soft_data_status',
//            'ship_data_status'
        ];
        if (!$oldRow) {
            //新增时，仅有完善信息时才会更改工作流状态
            if (isset($newRow['is_complete']) && $newRow['is_complete'] == 1) {
                make(WorkFlowService::class, [self::SCENE_TYPE])->changeStatus($newRow['work_status_id'], $newRow,$isCli);
            }
        } else {
            $changeData = [];
            $hasChangeDworkField = false;
            foreach ($newRow as $key => $value) {
                if ($value != $oldRow[$key]) {
                    $changeData[] = [
                        'field'     => $key,
                        'old_value' => $oldRow[$key],
                        'new_value' => $value,
                    ];
                    if(in_array($key,$workField)){
                        $hasChangeDworkField = true;
                    }
                }
            }
            if($hasChangeDworkField){
                make(WorkFlowService::class, [self::SCENE_TYPE])->changeStatus($oldRow['work_status_id'], $newRow,$isCli);
            }
            if ($changeData) {
                $this->sendCommonNotice($newRow, $changeData);
            }
        }
    }

    /**
     * 设置工作流初始状态
     * @param $id
     * @param $createdBy
     * @return void
     */
    public function setFirstWorkStatus($id, $createdBy = 0)
    {
        //添加工作状态
        make(WorkFlowService::class, [self::SCENE_TYPE])->setFirstStatus($id, $createdBy);
    }

    public function changeWorkStatus(int $id, array $values)
    {
        $row = [];
        if (empty($values['work_status_id'])) {
            throw new AppException(StatusCode::VALIDATION_ERROR, __('common.Missing_parameter'));
        }
        if ($id > 0) {
            $row = $this->model::query()->find($id);
        } elseif (!empty($values['assemble_order_id'])) {
            $row = $this->model::query()->where('assemble_order_id', $values['assemble_order_id'])->first();
        }
        if (!$row) {
            throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
        }
        make(WorkFlowService::class, [self::SCENE_TYPE])->changeStatusManually($row->toArray(), $values['work_status_id']);
    }

    /**
     * 发送企业推送
     * @param $info
     * @param $changeData
     * @return void
     */
    public function sendCommonNotice($info, $changeData)
    {
        $order = AssembleOrderModel::find($info['assemble_order_id']);
        $params = $this->getCommonNoticeParams($order);
        $notice = [];
        foreach ($changeData as $item) {
            switch ($item['field']) {
                case 'other_remark':
                    $msg = "客户备注已更新:\n" . $item['new_value'];
                    $wareUser = make(UserService::class)->getUsersByRole(AssembleOrderCode::WARE_ROLE);
                    $wareUser = array_column($wareUser, 'id');
                    $sendUser = [
                        $info['assemble_user_id'],
                        $info['software_user_id'],
                        $order['sales_user_id'],
                        $info['order_user_id'],
                    ];
                    $sendUser = array_merge($sendUser, $wareUser);
                    $notice[] = [
                        'msg'       => $msg,
                        'send_user' => $sendUser
                    ];
                    break;
                case 'soft_data_status':
                    if ($item['new_value'] == 2) {
                        //上传完成后，有客户固件上传才需要审核
                        $isSend = make(AssembleOrderAttachmentService::class)->checkHasCustomer($info['assemble_order_id']) && $order['stock_order_code'] && $order['sales_user_id'];
                        if($isSend){
                            $msg = '软件资料已上传完成，请及时审核';
                            $sendUser = $order['sales_user_id'];
                            $notice[] = [
                                'msg'       => $msg,
                                'send_user' => $sendUser
                            ];
                        }
                    }
                    break;
                case 'first_assemble_data_status':
                    if ($item['new_value'] == 2) {
                        $msg = '首件的组装文件已上传完成，请及时审核';
                        $sendUser = $info['assemble_user_id'];
                        $notice[] = [
                            'msg'       => $msg,
                            'send_user' => $sendUser
                        ];
                    }
                    break;
                case 'first_soft_data_status':
                    if ($item['new_value'] == 2) {
                        $msg = '首件的软件文件已上传完成，请及时审核';
                        $sendUser = $info['software_user_id'];
                        $notice[] = [
                            'msg'       => $msg,
                            'send_user' => $sendUser
                        ];
                    }
                    break;
                case 'product_finished_data_status':
                    if ($item['new_value'] == 2) {
                        $msg = '生产的成品文件已上传完成，请及时审核';
                        $sendUser = $info['assemble_user_id'];
                        $notice[] = [
                            'msg'       => $msg,
                            'send_user' => $sendUser
                        ];
                    }
                    break;
                case 'product_soft_data_status':
                    if ($item['new_value'] == 2) {
                        $msg = '生产的软件文件已上传完成，请及时审核';
                        $sendUser = $info['software_user_id'];
                        $notice[] = [
                            'msg'       => $msg,
                            'send_user' => $sendUser
                        ];
                    }
                    break;
                default:
                    break;
            }
        }
        foreach ($notice as $item) {
            $tempParams = $params;
            $tempParams['notice_msg'] = $item['msg'];
            make(NoticeService::class)->commonAssembleOrderNotice($item['send_user'], $tempParams);
        }
    }

    /**
     * 工作状态改变消息推送
     * @param $oldStatusId
     * @param $newStatusId
     * @param $data
     * @return void
     */
//    public function sendNoticeByWorkStatus($oldStatusId, $newStatusId, $data)
//    {
//        $order = AssembleOrderModel::find($data['assemble_order_id']);
//        $params = $this->getCommonNoticeParams($order);
//        $oldStatusKey = WorkStatusModel::query()->where('id', $oldStatusId)->value('name');
//        $newStatusKey = WorkStatusModel::query()->where('id', $newStatusId)->value('name');
//        $params['notice_msg'] = "组装订单状态由【{$oldStatusKey}】转变为【{$newStatusKey}】,请知晓";
//
//        $sendUser = make(UserService::class)->getUsersByRole(AssembleOrderCode::PRODUCTION_ROLE);
//        $sendUser = array_column($sendUser, 'id');
//        make(NoticeService::class)->commonAssembleOrderNotice($sendUser, $params);
//    }

    public function sendNoticeByWorkStatus($oldStatusId, $newStatusId, $data)
    {
        $order = AssembleOrderModel::find($data['assemble_order_id']);
        $oldStatusKey = WorkStatusModel::query()->where('id', $oldStatusId)->value('name');
        $newStatusKey = WorkStatusModel::query()->where('id', $newStatusId)->value('name');
        $params = $this->getCommonNoticeParams($order);
        Logger()->info('-----------status', [
            $oldStatusKey,
            $newStatusKey
        ]);
        $sendUser = [];
        switch ($newStatusKey) {
            case '订单确认':
                $productionUser = make(UserService::class)->getUsersByRole(AssembleOrderCode::PRODUCTION_ROLE);
                $sendUser = array_column($productionUser, 'id');
                $params['notice_msg'] = "组装订单状态由【{$oldStatusKey}】转变为【{$newStatusKey}】,请及时前往完善订单信息";
                break;
            case '生产准备':
                $wareUser = make(UserService::class)->getUsersByRole(AssembleOrderCode::WARE_ROLE);
                $wareUser = array_column($wareUser, 'id');
                $sendUser = [
                    $data['assemble_user_id'],
                    $data['software_user_id'],
                    $order['sales_user_id'],
                ];
                $sendUser = array_merge($sendUser, $wareUser);
                $params['notice_msg'] = "组装订单状态由【{$oldStatusKey}】转变为【{$newStatusKey}】,请及时前往完成资料填写";
                break;
            case '组装首件':
                $sendUser = [
                    $data['test_user_id'],
                ];
                $params['notice_msg'] = "组装订单状态由【{$oldStatusKey}】转变为【{$newStatusKey}】,请及时前往完成首件资料填写";
                break;
            case '生产中':
                $sendUser = [
                    $data['test_user_id'],
                ];
                $params['notice_msg'] = "组装订单状态由【{$oldStatusKey}】转变为【{$newStatusKey}】,请及时前往完成生产资料填写";
                break;
            case '组装总结':
                $sendUser = [
                    $data['test_user_id'],
                    $data['assemble_user_id'],
                ];
                $params['notice_msg'] = "组装订单状态由【{$oldStatusKey}】转变为【{$newStatusKey}】,请及时前往完成总结填写";
                break;
            case '发货':
                $sendUser = [
                    $order['sales_user_id'],
                ];
                $params['notice_msg'] = "组装订单状态由【{$oldStatusKey}】转变为【{$newStatusKey}】,请及时前往完成发货信息填写";
                break;
            case '完成':
                if ($data['approve_status'] == AssembleOrderCode::APPROVE_STATUS_WAIT) {
                    $sendUser = [
                        $data['order_user_id'],
                    ];
                    $params['notice_msg'] = "组装订单状态由【{$oldStatusKey}】转变为【{$newStatusKey}】,请及时前往完成订单核准";
                }
                break;
            default:
                $params['notice_msg'] = "组装订单状态由【{$oldStatusKey}】转变为【{$newStatusKey}】";
                break;
        }
        $sendUser = array_unique(array_filter($sendUser));
        make(NoticeService::class)->commonAssembleOrderNotice($sendUser, $params);
    }

    /**获取公共参数
     * @param $order
     * @return array
     */
    public function getCommonNoticeParams($order): array
    {
        $params = [
            'code'             => $order['code'] ?? '',
            'product_name'     => $order['product_name'] ?? '',
            'num'              => $order['num'] ?? 0,
            'product_code'     => $order['product_code'] ?? '',
            'assemble_address' => $order['assemble_address'] ?? '',
            'host'             => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
            'order_id'         => $order['id'] ?? 0
        ];
        return $params;
    }

    /**
     * 编辑时根据mac/sn附件或文本来关联mac/sn
     * @param $raw
     * @param $values
     * @return void
     */
    public function relateMacSn($raw, $values){
        $orderType = ProductionCode::CODE_USED_TYPE_ASSEMBLE;
        $orderId = $raw['assemble_order_id'] ?? $values['assemble_order_id'];

        $order = AssembleOrderModel::find($orderId);
        $relateOrder = [
            'used_type'      => $orderType,
            'used_relate_id' => $orderId,
            'used_no'        => $order['code'] ?? '',
            'used_product'   => $order['product_name'] ?? '',
            'used_product_code'   => $order['product_code'] ?? ''
        ];
        if ($raw) {
            $conditions = [
                'mac_sn_attachment_id' => $raw['mac_sn_attachment_id'] != $values['mac_sn_attachment_id'],
                'mac_text_origin_type' => $raw['mac_text_origin_type'] != $values['mac_text_origin_type'],
                'mac_address_text'     => $raw['mac_address_text'] != $values['mac_address_text'],
                'sn_text_origin_type'  => $raw['sn_text_origin_type'] != $values['sn_text_origin_type'],
                'sn_no_text'           => $raw['sn_no_text'] != $values['sn_no_text'],
                'mac_range_origin_type' => $raw['mac_range_origin_type'] != $values['mac_range_origin_type'],
                'mac_address_range' => $raw['mac_address_range'] != $values['mac_address_range'],
                'sn_range_origin_type' => $raw['sn_range_origin_type'] != $values['sn_range_origin_type'],
                'sn_no_range' => $raw['sn_no_range'] != $values['sn_no_range'],
            ];
            if (array_filter($conditions)) {
                $this->dealCode($values,$relateOrder,$conditions['mac_sn_attachment_id']);
            }
        }else{
            $this->dealCode($values,$relateOrder);
        }
    }

    /**
     * 整理mac和sn的相关数据
     * @param $values
     * @param $relateOrder
     * @param bool $isDealBatch
     * @return void
     */
    public function dealCode($values, $relateOrder, bool $isDealBatch = true)
    {
        $macService = make(MacAddressService::class);
        $snService = make(SnCodeService::class);
        $originType = array_keys(ProductionCode::ORIGIN_TYPE);
        $macData = array_fill_keys($originType, []);
        $snData = array_fill_keys($originType, []);
        $batchData = [];
        //获取文件的数据
        if (!empty($values['mac_sn_attachment_id'])) {
            list($macData, $snData, $batchData) = $this->importMacAndSn($values['mac_sn_attachment_id'], $relateOrder);
        }
        //获取文本的数据
        foreach ($originType as $type) {
            //合并对应来源的mac文本数据
            if ($values['mac_text_origin_type'] == $type) {
                $macArr = $this->getCodeFromText($values['mac_address_text']);
                $this->checkMac([$type=>$macArr],$relateOrder);
                $macData[$type] = array_merge($macData[$type], $macArr);
            }
            //合并对应来源的sn文本数据
            if ($values['sn_text_origin_type'] == $type) {
                $snArr= $this->getCodeFromText($values['sn_no_text']);
                $this->checkSn([$type=>$snArr],$relateOrder);
                $snData[$type] = array_merge($snData[$type], $snArr);
            }
            //合并MAC区间数据
            if($values['mac_range_origin_type'] == $type && !empty($values['mac_address_range'])){
                $rangeArr = $values['mac_address_range'];
                //过滤掉非法数据
//                $rangeArr = $this->filterAndTrimCodeRange($values['mac_address_range']);
                $macService->checkOrderRange($type,$rangeArr,$relateOrder);
                $macData[$type] = array_merge($macData[$type],$macService->getRangeData($rangeArr));
            }
            //合并SN区间数据
            if($values['mac_range_origin_type'] == $type && !empty($values['sn_no_range'])){
                $rangeArr = $values['sn_no_range'];
                //过滤掉非法数据
//                $rangeArr = $this->filterAndTrimCodeRange($values['sn_no_range']);
                $snService->checkOrderRange($type,$rangeArr,$relateOrder);
                $snData[$type] = array_merge($snData[$type],$snService->getRangeData($rangeArr));            }
            //切换成大写
            $macData[$type] = upper_arr(unique_filter($macData[$type]));
            $snData[$type] = upper_arr(unique_filter($snData[$type]));
        }
        //汇集数据修改
        $macService->editOrderRelation($macData, $relateOrder);
        $snService->editOrderRelation($snData, $relateOrder);

        //批次记录
        $isDealBatch && $macService->editOrderBatch($batchData, $relateOrder);
    }

    /**
     * 过滤掉非法数据
     * @param $rangeArr
     * @return array
     */
    public function filterAndTrimCodeRange($rangeArr): array
    {
        if(empty($rangeArr)) return [];

        return array_filter(array_map(function ($item) {
            // 去除每个元素两端的空格
            return array_map('trim', $item);
        }, $rangeArr), function ($item) {
            // 过滤后检查两个元素是否都存在非空值
            return !empty($item[0]) && !empty($item[1]);
        });
    }

    public function checkMac($macData,$relateOrder){
        foreach ($macData as $macType => $macs) {
            if ($macs) {
                $usedMac = MacAddressModel::query()
                    ->whereIn('mac_address', $macs)->where('origin_type', $macType)
                    ->where('used_type', '<>', $relateOrder['used_type'])->where('used_relate_id', '<>', $relateOrder['used_relate_id'])
                    ->where('is_used', 1)->pluck('mac_address')->toArray();

                if ($usedMac) {
                    throw new AppException(StatusCode::ERR_SERVER, '填写的MAC地址【'. implode(',', $usedMac). '】已被使用，请重新填写');
                }
                //校验是否公司购买的范围内
                if ($macType === ProductionCode::ORIGIN_TYPE_OF_SELF) {
                    $outRange = make(MacAddressService::class)->checkHexRange($macs);
                    if ($outRange) {
                        throw new AppException(StatusCode::ERR_SERVER, '填写的MAC地址【'. implode(',', $outRange). '】不在公司的公司购买范围内，请重新填写');
                    }
                }

            }
        }
    }

    public function checkSn($snData,$relateOrder)
    {
        foreach ($snData as $snType => $sns) {
            if ($snType === ProductionCode::ORIGIN_TYPE_OF_SELF && $sns) {
                $usedSn = SnCodeModel::query()->whereIn('sn_code', $sns)->where('origin_type', $snType)
                    ->where('used_type', '<>', $relateOrder['used_type'])->where('used_relate_id', '<>', $relateOrder['used_relate_id'])
                    ->where('is_used', 1)->pluck('sn_code')->toArray();
                if ($usedSn) {
                    throw new AppException(StatusCode::ERR_SERVER, '填写的SN号【'. implode(',', $usedSn). '】已被使用，请重新填写');
                }
            }
        }
    }

    public function relateMacSnOld($raw, $values)
    {
        $orderType = ProductionCode::CODE_USED_TYPE_ASSEMBLE;
        $orderId = $raw['assemble_order_id'] ?? $values['assemble_order_id'];

        $order = AssembleOrderModel::find($orderId);
        $relateOrder = [
            'used_type'      => $orderType,
            'used_relate_id' => $orderId,
            'used_no'        => $order['code'] ?? '',
            'used_product'   => $order['product_name'] ?? ''
        ];


        $macService = make(MacAddressService::class);
        $snService = make(SnCodeService::class);

        $values = [
            'mac_sn_attachment_id' => $values['mac_sn_attachment_id'] ?? 0,
            'mac_text_origin_type' => $values['mac_text_origin_type'] ?? 0,
            'sn_text_origin_type'  => $values['sn_text_origin_type'] ?? 0,
            'mac_address_text'     => $values['mac_address_text'] ?? '',
            'sn_no_text'           => $values['sn_no_text'] ?? ''
        ];
        $originType = array_keys(ProductionCode::ORIGIN_TYPE);
        if ($raw) {
            //编辑
            if (!empty($values['mac_sn_attachment_id'])) {
                if ($values['mac_sn_attachment_id'] != $raw['mac_sn_attachment_id']) {
                    //删除原有的批次
                    OrderMacSnBatchModel::query()->where([
                        'order_type' => $orderType,
                        'order_id'   => $orderId
                    ])->delete();
                    $this->importMacAndSn($values['mac_sn_attachment_id'], $relateOrder);
                }
            } elseif (!empty($values['mac_address_text']) || !empty($values['sn_no_text'])) {
                //文本输入没有批次，删除原有的批次
                OrderMacSnBatchModel::query()->where([
                    'order_type' => $orderType,
                    'order_id'   => $orderId
                ])->delete();
                //mac文本输入
                if ($raw['mac_text_origin_type'] != $values['mac_text_origin_type'] || $raw['mac_address_text'] != $values['mac_address_text']) {
                    $macData = [];
                    foreach ($originType as $type) {
                        if ($values['mac_text_origin_type'] == $type) {
                            $macData[$type] = $this->getCodeFromText($values['mac_address_text']);
                        } else {
                            $macData[$type] = [];
                        }
                    }

                    $macService->editOrderRelation($macData, $relateOrder);
                }
                //sn文本输入
                if ($raw['sn_text_origin_type'] != $values['sn_text_origin_type'] || $raw['sn_no_text'] != $values['sn_no_text']) {
                    $snData = [];
                    foreach ($originType as $type) {
                        if ($values['sn_text_origin_type'] == $type) {
                            $snData[$type] = $this->getCodeFromText($values['sn_no_text']);
                        } else {
                            $snData[$type] = [];
                        }
                    }
                    $snService->editOrderRelation($snData, $relateOrder);
                }
            } else {
                $this->deleteMacAndSn($orderType, $orderId);
            }
        } else {
            if (!empty($values['mac_sn_attachment_id'])) {
                if ($values['mac_sn_attachment_id'] != $raw['mac_sn_attachment_id']) {
                    $this->importMacAndSn($values['mac_sn_attachment_id'], $relateOrder);
                }
            } else {
                //mac文本输入
                if (!empty($values['mac_address_text'])) {
                    $macData = [];
                    foreach ($originType as $type) {
                        if ($values['mac_text_origin_type'] == $type) {
                            $macData[$type] = $this->getCodeFromText($values['mac_address_text']);
                        } else {
                            $macData[$type] = [];
                        }
                    }
                    $macService->editOrderRelation($macData, $relateOrder);
                }
                //sn文本输入
                if (!empty($values['sn_no_text'])) {
                    $snData = [];
                    foreach ($originType as $type) {
                        if ($values['sn_text_origin_type'] == $type) {
                            $snData[$type] = $this->getCodeFromText($values['sn_no_text']);
                        } else {
                            $snData[$type] = [];
                        }
                    }
                    $snService->editOrderRelation($snData, $relateOrder);
                }
            }
        }
    }

    /**
     * 解析文本中的mac/sn
     * @param $text
     * @return array
     */
    public function getCodeFromText($text)
    {
        $text = mb_convert_encoding($text, 'UTF-8', 'auto');
        $input = preg_replace('/[，;；。.\s]+/', ',', $text);   // 替换、逗号和分号为逗号
        $snCodeArr = explode(',', $input);
        $snCodeArr = array_unique(array_filter($snCodeArr));
        return $snCodeArr;
    }


    /**
     * 解析导入的mac/sn文件
     * @param $fileId
     * @param $orderData
     * @return array
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function importMacAndSn($fileId, $orderData)
    {
        $realPath = make(AttachmentService::class)->getAttachmentPath($fileId);
        $reader = IOFactory::createReaderForFile($realPath);
        $reader->setReadDataOnly(true);
        $spreadsheet = $reader->load($realPath);
        $worksheet = $spreadsheet->getActiveSheet();
        // 定义表格的标题与数据库字段的映射
        $arr = [
            '订单备注' => 'batch_key',
            'MAC来源'  => 'mac_type',
            'MAC地址'  => 'mac_address',
            'SN来源'   => 'sn_type',
            'SN码'     => 'sn_code'
        ];

        // 获取第一行标题
        $headers = [];
        $rowIterator = $worksheet->getRowIterator(1, 1); // 获取第一行
        foreach ($rowIterator as $row) {
            foreach ($row->getCellIterator() as $cell) {
                $headers[] = $cell->getValue();
            }
        }

        // 获取数据并转化为所需格式
        $data = [];
        $rowIterator = $worksheet->getRowIterator(2); // 从第二行开始读取数据
        $emptyRow = [];
        foreach ($rowIterator as $rowIndex => $row) {
            $rowData = [];
            $columnIndex = 0;

            // 遍历每一列
            foreach ($row->getCellIterator() as $cell) {
                $header = $headers[$columnIndex]; // 获取对应列的标题
                if (isset($arr[$header])) {
                    // 根据标题映射保存到相应的字段
                    $rowData[$arr[$header]] = trim($cell->getFormattedValue());
                }
                $columnIndex++;

            }
            if (empty($rowData['mac_address']) && empty($rowData['sn_code'])) {
                $emptyRow[] = $rowIndex;
            }
            // 将转换后的数据添加到结果数组
            $data[$rowIndex] = $rowData;
        }
        //空项报错
        $errorMsg = '';
        if (!empty($emptyRow)) {
            $errorMsg .= '第' . implode(',', $emptyRow) . '行数据为空，请剔除空行；';
        }
        //校验mac和sn有没有重复的数据
        $macTemp = array_filter(array_column($data, 'mac_address'));
        if (count($macTemp) != count(array_unique($macTemp))) {
            $errorMsg .= 'MAC地址有重复，请剔除重复项；';
        }
        unset($macTemp);
        $snTemp = array_filter(array_column($data, 'sn_code'));
        if (count($snTemp) != count(array_unique($snTemp))) {
            $errorMsg .= 'SN码有重复，请剔除重复项；';
        }
        //有重复项或空项先行报错
        if ($errorMsg) {
            throw new AppException(StatusCode::ERR_SERVER, $errorMsg);
        }

        //整理数据
        $macRow = [];
        $snRow = [];
        $batchData = [];
        $batchNo = 0;
        $batchKey = null;

        //mac,sn默认两个来源，用于每个来源取消关联
        $originType = array_keys(ProductionCode::ORIGIN_TYPE);
        $macData = array_fill_keys($originType, []);
        $snData = array_fill_keys($originType, []);


        foreach ($data as $rowIndex => $value) {
            if (!empty($value['mac_address'])) {
                $macType = ProductionCode::getOriginTypeByName($value['mac_type']);
                $macRow[$rowIndex] = $value['mac_address'];
                $macData[$macType][] = $value['mac_address'];
            }
            if (!empty($value['sn_code'])) {
                $snType = ProductionCode::getOriginTypeByName($value['sn_type']);
                $snRow[$rowIndex] = $value['sn_code'];
                $snData[$snType][] = $value['sn_code'];
            }
            if (is_null($batchKey)) {
                $batchKey = $value['batch_key'];
                $batchNo++;
            } elseif ($batchKey != $value['batch_key']) {
                $batchKey = $value['batch_key'];
                $batchNo++;
            }
            $batchData[] = [
                'batch_num'   => $batchNo,
                'batch_key'   => $batchKey,
                'mac_type'    => $macType ?? 0,
                'mac_address' => $value['mac_address'],
                'sn_type'     => $snType ?? 0,
                'sn_code'     => $value['sn_code'],
            ];
        }
//        return [$macData,$snData,$batchData];
        //校验mac和sn是否有在使用
        $useError = '';
        foreach ($macData as $macType => $macs) {
            if ($macs) {
                $usedMac = MacAddressModel::query()
                    ->whereIn('mac_address', $macs)->where('origin_type', $macType)
                    ->where('used_type', '<>', $orderData['used_type'])->where('used_relate_id', '<>', $orderData['used_relate_id'])
                    ->where('is_used', 1)->pluck('mac_address')->toArray();

                if ($usedMac) {
                    $keys = array_keys(array_intersect($macRow, $usedMac));
                    $useError .= '第' . implode(',', $keys) . '的MAC地址已被使用，请重新填写；';
                }
                //校验是否公司购买的范围内
                if ($macType === ProductionCode::ORIGIN_TYPE_OF_SELF) {
                    $outRange = make(MacAddressService::class)->checkHexRange($macs);
                    if ($outRange) {
                        $keys = array_keys(array_intersect($macRow, $outRange));
                        $useError .= '第' . implode(',', $keys) . '的MAC地址不在公司购买的范围内，请重新填写；';
                    }
                }

            }
        }
        unset($macRow);
        foreach ($snData as $snType => $sns) {
            if ($snType === ProductionCode::ORIGIN_TYPE_OF_SELF && $sns) {
                $usedSn = SnCodeModel::query()->whereIn('sn_code', $sns)->where('origin_type', $snType)
                    ->where('used_type', '<>', $orderData['used_type'])->where('used_relate_id', '<>', $orderData['used_relate_id'])
                    ->where('is_used', 1)->pluck('sn_code')->toArray();
                if ($usedSn) {
                    $keys = array_keys(array_intersect($snRow, $usedSn));
                    $useError .= '第' . implode(',', $keys) . '的SN码已被使用，请重新填写；';
                }
            }
        }
        unset($snRow);
        if ($useError) {
            throw new AppException(StatusCode::ERR_SERVER, $useError);
        }
        return [
            $macData,
            $snData,
            $batchData
        ];
//        //更新订单的关联数据
//        make(MacAddressService::class)->editOrderRelation($macData, $orderData);
//        make(SnCodeService::class)->editOrderRelation($snData, $orderData);
//
//        //获取插入的数据的对用id
//        $orderMac = [];
//        foreach ($macData as $type => $macs) {
//            $orderMac[$type] = MacAddressModel::query()->whereIn('mac_address', $macs)->where('origin_type', $type)->pluck('id', 'mac_address')->toArray();
//        }
//        $orderSn = [];
//        foreach ($snData as $type => $sns) {
//            $orderSn[$type] = SnCodeModel::query()->whereIn('sn_code', $sns)->where('origin_type', $type)->pluck('id', 'sn_code')->toArray();
//        }
//        //匹配id批量插入批次
//        $insertBatchData = [];
//        foreach ($batchData as $key => $value) {
//            $insertBatchData[] = [
//                'order_type' => $orderData['used_type'],
//                'order_id'   => $orderData['used_relate_id'],
//                'batch_num'  => $value['batch_num'],
//                'batch_key'  => $value['batch_key'],
//                'mac_id'     => $orderMac[$value['mac_type']][$value['mac_address']] ?? 0,
//                'sn_id'      => $orderSn[$value['sn_type']][$value['sn_code']] ?? 0,
//                'created_at' => date('Y-m-d H:i:s'),
//                'updated_at' => date('Y-m-d H:i:s'),
//            ];
//        }
//
//        //批量插入
//        OrderMacSnBatchModel::query()->insert($insertBatchData);
//        return true;
    }

    public function deleteMacAndSn($orderType, $orderId)
    {
        MacAddressModel::query()->where([
            'used_type'      => $orderType,
            'used_relate_id' => $orderId
        ])->delete();
        SnCodeModel::query()->where([
            'used_type'      => $orderType,
            'used_relate_id' => $orderId
        ])->delete();
        OrderMacSnBatchModel::query()->where([
            'order_type' => $orderType,
            'order_id'   => $orderId
        ])->delete();
    }

    public function getOrderUser($assembleId){

        $order = AssembleOrderModel::find($assembleId);

        $info = $this->model::query()->where('assemble_order_id', $assembleId)->first();

        $wareUser = make(UserService::class)->getUsersByRole(AssembleOrderCode::WARE_ROLE);

        $wareUser = array_column($wareUser, 'id');
        $assembleUser = make(UserService::class)->getUsersByRole(AssembleOrderCode::PRODUCTION_ROLE);

        $assembleUser = array_column($assembleUser, 'id');

        $sendUser = [
            $info['assemble_user_id']?? 0,
            $info['software_user_id']?? 0,
            $order['sales_user_id']?? 0,
            $info['order_user_id']?? 0,
        ];
        $sendUser = array_merge($sendUser, $wareUser, $assembleUser);
        return array_unique(array_filter($sendUser));
    }
}