<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/13 下午5:21
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing;

use App\Constants\DataBaseCode;
use App\Core\Utils\TimeUtils;
use App\Model\TongjiSite\TongjiSiteModel;
use App\Model\TongjiSite\TongjiSiteSourceSiteModel;
use App\Model\TongjiSite\TongjiSiteVisitAreaModel;
use App\Model\TongjiSite\TongjiSiteVisitPageModel;
use Hyperf\Cache\Annotation\Cacheable;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Collection;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;

class SiteReportService extends \App\Core\Services\BusinessService
{
    /**
     * 获得网站统计概怳 PV,UV,IP数,跳出率,平均访问时长
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return array
     */
    public function siteTrendRpt($filter, $op, $sort, $order, $limit): array
    {
        /* @var TongjiSiteSourceSiteModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, TongjiSiteSourceSiteModel::query());
        $rows = $query->select(['site_id', DB::raw('SUM(pv_count) as pv_count, CONCAT(round(AVG(pv_ratio),2), \'%\') as pv_ratio, SUM(visit_count) as visit_count, SUM(visitor_count) as visitor_count, SUM(new_visitor_count) as new_visitor_count, round(AVG(new_visitor_ratio),2) as new_visitor_ratio, SUM(ip_count) as ip_count, CONCAT(round(AVG(bounce_ratio),2), \'%\') as bounce_ratio, round(AVG(avg_visit_time),2) as avg_visit_time, round(AVG(avg_visit_pages),2) as avg_visit_pages, SUM(trans_count) as trans_count, round(AVG(trans_ratio),2) as trans_ratio')])->first();
        if(!$rows){
            $rows = [
                'site_id'           => $filter['site_id'] ?? 0,
                'pv_count'          => 0,
                'pv_ratio'          => '0%',
                'visit_count'       => 0,
                'visitor_count'     => 0,
                'new_visitor_count' => 0,
                'new_visitor_ratio' => 0,
                'ip_count'          => 0,
                'bounce_ratio'      => 0,
                'avg_visit_time'    => '00:00:00',
                'avg_visit_pages'   => 0,
                'trans_count'       => 0,
                'trans_ratio'       => '0%',
            ];
        }else{
            $rows = $rows->toArray();
            $rows['avg_visit_time'] = !empty($rows['avg_visit_time']) ? gmdate('H:i:s', $rows['avg_visit_time']) : '00:00:00';
        }
        return $rows;
    }

    /**
     * 站点流量趋势数据
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @param $param [day_type => 'day']
     * @return Builder[]|Collection
     */
    public function siteTrendRptByDate($filter, $op, $sort, $order, $limit, $param = [])
    {
        /* @var TongjiSiteSourceSiteModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, TongjiSiteSourceSiteModel::query());

        $param['day_type'] = $param['day_type'] ?? 'day';
        $columns = ['site_id', DB::raw('SUM(pv_count) as pv_count, round(AVG(pv_ratio), 2) as pv_ratio, SUM(visit_count) as visit_count, SUM(visitor_count) as visitor_count, SUM(new_visitor_count) as new_visitor_count, round(AVG(new_visitor_ratio), 2) as new_visitor_ratio, SUM(ip_count) as ip_count, round(AVG(bounce_ratio), 2) as bounce_ratio, AVG(avg_visit_time) as avg_visit_time, round(AVG(avg_visit_pages), 2) as avg_visit_pages, SUM(trans_count) as trans_count, round(AVG(trans_ratio), 2) as trans_ratio')];
        if ($param['day_type'] == 'month'){
            $columns[] = DB::raw('FROM_UNIXTIME(unix_timestamp(dated_at), \'%Y-%m\') as dated_at, FROM_UNIXTIME(unix_timestamp(dated_at), \'%Y-%m\') as dated_at_text');
        }else{
            $columns[] = DB::raw('dated_at, dated_at as dated_at_text');
        }
        $rows = $query->select($columns)->groupBy('site_id', 'dated_at_text')->orderBy('dated_at', 'asc')->get()->toArray();

        $newRows = [];
        $startDate = '';
        $endDate   = '';
        foreach ($rows as $row){
            $newRows[$row['site_id']][] = $row;
            // 获取开始日期
            if(!$startDate){
                $startDate = $row['dated_at'];
            }else if($row['dated_at'] < $startDate){
                $startDate = $row['dated_at'];
            }
            // 获取结束日期
            if(!$endDate){
                $endDate = $row['dated_at'];
            }else if($row['dated_at'] < $endDate){
                $endDate = $row['dated_at'];
            }
        }
        $dayType = $param['day_type'] ?? 'day';
        if(!empty($filter['dated_at'])) {
            $apiDate = explode('-', $filter['dated_at']);
            $timeNum = TimeUtils::getTimeNum(date('Y-m-d', $apiDate[0]), date('Y-m-d', $apiDate[1]), $param['day_type']);
            $startDate = $apiDate[0];
            $endDate   = $apiDate[1];
        }else{
            $timeNum = TimeUtils::getTimeNum($startDate, $endDate);
        }
        foreach ($newRows as $nrkey => $nrval){
            if(empty($result[$nrkey])){
                $result[$nrkey] = [
                    'site_id' => $nrkey,
                    'domain'  => TongjiSiteModel::query()->where('site_id', $nrkey)->value('domain'),
                    'data'    => [],
                ];
            }
            $r = 0;
            for ($i = 0; $i<=$timeNum; $i++){
                $itemDate = TimeUtils::getDateFormat(strtotime("+{$i} {$dayType}", $startDate), $dayType);
                if(!empty($nrval[$r]['dated_at']) && $nrval[$r]['dated_at'] == $itemDate){
                    $result[$nrkey]['data'][] = $nrval[$r];
                    $r++;
                }else{
                    $result[$nrkey]['data'][] = [
                        'site_id'           => $nrkey,
                        'pv_count'          => 0,
                        'pv_ratio'          => 0,
                        'visit_count'       => 0,
                        'visitor_count'     => 0,
                        'new_visitor_count' => 0,
                        'new_visitor_ratio' => 0,
                        'ip_count'          => 0,
                        'bounce_ratio'      => 0,
                        'avg_visit_time'    => 0,
                        'avg_visit_pages'   => 0,
                        'trans_count'       => 0,
                        'trans_ratio'       => 0,
                        'dated_at'          => $itemDate
                    ];
                }
            }
        }
        $result = array_values($result);
        return $result;
    }

    /**
     * 受访页面列表
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @param $param
     * @return mixed
     */
    public function visitPageList($filter, $op, $sort, $order, $limit = 10, $param = [])
    {
        /* @var TongjiSiteVisitPageModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, TongjiSiteVisitPageModel::query());
        $columns = ['site_id', 'title', DB::raw('SUM(pv_count) as pv_count, CONCAT(round(AVG(ratio), 2), \'%\') as ratio'), 'dated_at'];
        $rows = $query->orderBy('pv_count', 'desc')->groupBy('title')->paginate($limit, $columns);
        return $rows;
    }

    /**
     * 站点来源页面访问数据列表
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @param $param
     * @return \Hyperf\Contract\LengthAwarePaginatorInterface
     */
    public function sourcePageList($filter, $op, $sort, $order, $limit = 10, $param = [])
    {
        /* @var TongjiSiteSourceSiteModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, TongjiSiteSourceSiteModel::query());
        $columns = ['site_id', 'device_type', 'title', DB::raw('SUM(pv_count) as pv_count, CONCAT(round(AVG(pv_ratio), 2), \'%\') as pv_ratio, SUM(visit_count) as visit_count, SUM(visitor_count) as visitor_count, SUM(new_visitor_count) as new_visitor_count, round(AVG(new_visitor_ratio), 2) as new_visitor_ratio, SUM(ip_count) as ip_count, round(AVG(bounce_ratio), 2) as bounce_ratio, AVG(avg_visit_time) as avg_visit_time, round(AVG(avg_visit_pages), 2) as avg_visit_pages, SUM(trans_count) as trans_count, round(AVG(trans_ratio), 2) as trans_ratio')];
        $rows    = $query->orderBy('pv_count', 'desc')->groupBy('title')->paginate($limit, $columns);
        return $rows;
    }

    /**
     * 访问地区分布数据
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return \Hyperf\Utils\Collection
     */
    public function visitAreaRpt($filter, $op, $sort, $order, $limit)
    {
        /* @var TongjiSiteVisitAreaModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, TongjiSiteVisitAreaModel::query());
        $columns = [DB::raw('site_id, area_id, name, SUM(pv_count) as pv_count, round(AVG(pv_ratio), 2) as pv_ratio, SUM(visit_count) as visit_count, SUM(visitor_count) as visitor_count, SUM(new_visitor_count) as new_visitor_count, round(AVG(new_visitor_ratio), 2) as new_visitor_ratio, SUM(ip_count) as ip_count, round(AVG(bounce_ratio), 2) as bounce_ratio, AVG(avg_visit_time) as avg_visit_time, round(AVG(avg_visit_pages), 2) as avg_visit_pages, SUM(trans_count) as trans_count, round(AVG(trans_ratio), 2) as trans_ratio, dated_at')];
        $rows = $query->select($columns)->orderBy($sort, $order)->orderBy('area_id', 'asc')->groupBy('area_id')
             ->groupBy('dated_at')->get();
        return $rows;
    }
}