<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/19 下午4:56
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Bbs;

use App\Constants\DataBaseCode;
use App\Core\Utils\Log;
use App\Model\Marketing\MarketingMonthDatum;
use App\Model\Marketing\MarketingMontyDatum;
use App\Model\StationPCBbs\CommonMember;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use mysql_xdevapi\Result;

/**
 * StationPC 用户数，以论坛用户为准
 */
class MemberService extends BbsBaseService
{
    /**
     * @Inject()
     * @var CommonMember
     */
    protected $stationpcBbsCommonMemberModel;

    /**
     * @Inject()
     * @var MarketingMonthDatum
     */
    protected $marketingMontyDateModel;

    public function getMemberList($connection = 'stationpc_bbsen')
    {
        $result = $this->stationpcBbsCommonMemberModel::on($connection)->count();
        return $result;
    }

    /**
     * 更新用户数
     * @param $brand 1-Firefly,2-StationPC
     * @return boolean
     */
    public function updateMember($brand = 2)
    {
        for ($i = 1; $i < 7; $i++) {
            $startTime = strtotime(date('Y-'.$i.'-01 00:00:00'));
            $endTime = strtotime(date('Y-'.$i.'-01 23:59:59') . "+1 month -1 day");
            // var_dump($startTime, $endTime);

            $memberCN = $this->stationpcBbsCommonMemberModel::on(DataBaseCode::STATIONPC_BBSCN)
                ->whereBetween('regdate',[$startTime, $endTime])->count();
            $memberEN = $this->stationpcBbsCommonMemberModel::on(DataBaseCode::STATIONPC_BBSEN)
                ->whereBetween('regdate',[$startTime, $endTime])->count();
            $result = $this->marketingMontyDateModel::query()->updateOrCreate(['month' => date('Y-'.$i.'-01 00:00:00'), 'brand' => $brand], [
                'user_cn_count' => $memberCN,
                'user_en_count' => $memberEN
            ]);
        }
        return true;
        // return $result;
    }
}