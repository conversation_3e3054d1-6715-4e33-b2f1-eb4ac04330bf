<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/20 下午5:53
 * <AUTHOR> X
 * @Description
 */

namespace App\Core\Services\Index\Quota;

use App\Core\Services\TchipSale\SaleService;
use Hyperf\Di\Annotation\Inject;

class TchipSale
{
    /**
     * @Inject()
     * @var SaleService
     */
    protected $service;

    public function monthQuota()
    {
        $quota = $this->service->achievementProgress($this->service::TYPE_MONTH, date('Y-m-d'));
        return ($quota ?? 0) . '%';
    }

    public function quarterQuota()
    {
        $quota = $this->service->achievementProgress($this->service::TYPE_QUARTER, date('Y-m-d'));
        return ($quota ?? 0) . '%';
    }

    public function yearQuota()
    {
        $quota = $this->service->achievementProgress($this->service::TYPE_YEAR, date('Y-m-d'));
        return ($quota ?? 0) . '%';
    }

    public function deviceProportion()
    {
        $prop = $this->service->deviceProportion(['saledate' => [date('Y-01-01'), date('Y-m-d')]]);
        return ($prop ?? 0) . '%';
    }

    /**
     * 外单占比
     * @return int|mixed
     */
    public function exteriorOrderProportion()
    {
        $info = $this->service->orderCount(['saledate' => [date('Y-01-01'), date('Y-m-d')]]);
        return ($info['exterior']['prop'] ?? 0) . '%';
    }

    /**
     * 内单占比
     * @return int|mixed
     */
    public function insideOrderProportion()
    {
        $info = $this->service->orderCount(['saledate' => [date('Y-01-01'), date('Y-m-d')]]);
        return ($info['inside']['prop'] ?? 0) . '%';
    }
}