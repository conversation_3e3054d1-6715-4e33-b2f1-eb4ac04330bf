<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date      2022/5/14 下午7:58
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\OaReportService;
use App\Core\Services\UserService;
use App\Core\Services\WorkWx\WorkWxDepartmentService;
use App\Core\Services\WorkWx\WorkWxUserService;
use App\Core\Utils\Pinyin;
use App\Model\TchipBi\UserModel;
use App\Request\User\CodeRequest;
use App\Request\User\ResetPasswordRequest;
use Hyperf\Context\Context;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\Middleware;
use Hyperf\HttpServer\Annotation\Middlewares;
use App\Middleware\AuthMiddleware;
use App\Middleware\MenuMiddleware;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Psr\Http\Message\ResponseInterface;
use App\Annotation\WorkWxTokenAnnotation;

/**
 * @ControllerNameAnnotation("用户应用")
 * @Controller()
 */
class UserController extends BaseController
{
    /**
     * @Inject()
     * @var WorkWxDepartmentService
     */
    protected $workWxDepartmentService;

    /**
     * @Inject()
     * @var WorkWxUserService
     */
    protected $workWxUserService;

    /**
     * @Inject()
     * @var UserService
     */
    protected $userService;

    public function index(): ResponseInterface
    {
        $result = $this->workWxDepartmentService->getContactToken();
        return $this->response->success($result);
    }

    /**
     * 获取公钥
     * @RequestMapping(path="/getPublicKey")
     * @return ResponseInterface
     */
    public function getPublicKey(): ResponseInterface
    {
        $publicKey = env('RSA_PUBLIC_KEY');
        return $this->response->success(['publicKey' => $publicKey]);
    }

    /**
     * 用户登录
     * @RequestMapping(path="/login")
     * @return ResponseInterface
     */
    public function login()
    {
        $payload = $this->request->input('payload');

        // 解码 Base64 字符串
        $decodedPayload = base64_decode($payload);
        if ($decodedPayload === false) {
            return $this->response->error('载荷解码失败', 400);
        }

        // 使用私钥解密
        $privateKey = env('RSA_PRIVATE_KEY');
        // 获取私钥资源
        $privateKeyResource = openssl_pkey_get_private($privateKey);
        if ($privateKeyResource === false) {
            return $this->response->error('私钥无效', 400);
        }

        if (!openssl_private_decrypt($decodedPayload, $decryptedData, $privateKeyResource)) {
            return $this->response->error('解密失败', 400);
        }

        // 解析 JSON 字符串
        $data = json_decode($decryptedData, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return $this->response->error('载荷解析失败', 400);
        }

        $username = $data['username'] ?? '';
        $password = $data['password'] ?? '';
        $result = $this->userService->login($username, $password);

        return $this->response->success($result);
    }

    /**
     * 用户登录
     * @RequestMapping(path="/wxlogin")
     * @return ResponseInterface
     */
    public function wxlogin()
    {
        $validated = make(CodeRequest::class)->validated();
        $result = $this->userService->wxlogin($validated['code']);
        return $this->response->success($result);
    }

    /**
     * 获取用户信息
     * @RequestMapping(path="/userInfo")
//     * @Middleware(AuthMiddleware::class)
     * @return ResponseInterface
     */
    public function userInfo(): ResponseInterface
    {
        $id = $this->request->input('user_id');
        $result = $this->userService->getUserInfo($id);
        return $this->response->success($result);
    }

    /**
     * 退出登录
     * @RequestMapping(path="/logout")
     * @Middleware(AuthMiddleware::class)
     * @return ResponseInterface
     */
    public function logout(): ResponseInterface
    {
        $result = $this->userService->logout();
        return $this->response->success($result);
    }

    /**
     * 账号初始化
     * @RequestMapping(path="/user/init")
     * @Middleware(AuthMiddleware::class)
     * @return ResponseInterface
     */
    public function initAccount()
    {
        $password = $this->request->input('password');
        // $confirmPassword = $this->request->input('confim_password');
        $redminePassword = $this->request->input('redmine_password');
        $result          = $this->userService->initAccount($password, $redminePassword);
        return $this->response->success($result);
    }

    /**
     * 重置密码
     * @RequestMapping(path="/user/resetPassword")
     * @Middleware(AuthMiddleware::class)
     * @param ResetPasswordRequest $resetPasswordRequest
     * @return ResponseInterface
     */
    public function resetPassword(ResetPasswordRequest $resetPasswordRequest): ResponseInterface
    {
        $validated = $resetPasswordRequest->validated();
        $result    = $this->userService->resetPassword($validated['repassword']);
        return $this->response->success($result, '修改成功，请使用新密码重新登录');
    }

    /**
     * 平台用户列表
     * @RequestMapping(path="/user/userThirdList")
     * @Middleware(AuthMiddleware::class)
     * @return ResponseInterface
     */
    public function userThirdList()
    {
        $third = $this->request->input('third');
        return $this->response->success($this->userService->userThirdList($third));
    }

    /**
     * 获取指定角色列表
     * @RequestMapping(path="/user/getUsersByRole")
     * @Middleware(AuthMiddleware::class)
     * @return ResponseInterface
     */
    public function getUsersByRole()
    {
        $role = $this->request->input('role', null);
        return $this->response->success($role ? $this->userService->getUsersByRole($role) : []);
    }

    /**
     * firefly平台帖子处理人 列表
     * @RequestMapping(path="/user/threadProcessor")
     * @return ResponseInterface
     */
    public function fireflyThreadProcessor()
    {
        $res        = UserModel::query()
            ->select(['id', 'workwx_userid', 'name', 'status', 'department'])
            //->select(['id', 'workwx_userid', 'name'])
            ->whereJsonLength('department', '>=', 1)//刷选 有部门的

                                                    //->whereJsonContains('department',8)//过滤 仓库部
            //->whereJsonContains('department',9)//
            //->whereJsonContains('department', 11)//
            //->whereJsonContains('department', 12)//
            //->whereJsonContains('department', 13)//
            //->whereJsonContains('department', 17)//
            //->whereJsonContains('department', 24)//
            //->whereJsonContains('department', 25)//

            ->whereJsonDoesntContain('department', 21)//过滤 仓库部
            ->whereJsonDoesntContain('department', 20)//过滤 互联网软件部
            ->whereJsonDoesntContain('department', 19)//过滤 采购部
            ->whereJsonDoesntContain('department', 18)//过滤 深圳天启
            ->whereJsonDoesntContain('department', 14)//过滤 生产组
            ->whereJsonDoesntContain('department', 6)//过滤 财务部
            ->whereJsonDoesntContain('department', 5)//过滤 产品部
            ->whereJsonDoesntContain('department', 4)//过滤 综合部
            ->whereJsonDoesntContain('department', 3)//过滤 营销策划部
            ->whereJsonDoesntContain('department', 2)//过滤 销售部
            ->whereJsonDoesntContain('department', 10)//排除总经办
            //->whereJsonContains('department', 10, 'and', 1)//排除总经办
            ->where('status', 1)//激活状态的
            ->orderBy('email')
            ->orderBy('workwx_userid')
            ->get();
        $department = [];
        $department_txt = [];
        //$department=array_column($res->toArray(),'department','department');
        /** @var UserModel $val */
        foreach ($res as $val) {
            //$department = array_unique(array_merge_recursive($department, array_values($val->department)));
            //$department_txt[$val->department[0]] =$val->toArray()['department_text'];
            //$val->setHidden(['status_text', 'department_text']);
            $val->setHidden(['status','status_text']);
            //$val->setHidden(['status', 'department']);

            //$val->setAttribute('first',$val->getAttribute('name'));
            $val->setAttribute('initial',Pinyin::get($val->getAttribute('name'),true));
        }
        return $this->response->success($res->toArray());
    }

    /**
     * @ControllerNameAnnotation("同步用户到redmine")
     * @RequestMapping(path="/user/syncuser")
     * @Middlewares({
     *     @Middleware(AuthMiddleware::class),
     *     @Middleware(MenuMiddleware::class)
     * })
     * @WorkWxTokenAnnotation(type="contact")
     * @return ResponseInterface
     */
    public function syncuser(): ResponseInterface
    {
        set_time_limit(60);
        // 1.同步企微部门信息
        $this->workWxDepartmentService->syncDepartmentList();
        // 2.同步企微信息到bi
        $this->workWxUserService->syncUserList();
        // 3.bi同步到redmine
        $result = $this->userService->syncThirdByRedmine();
        return $this->response->success($result);
    }

    /**
     * 发送密码重置链接
     * @RequestMapping(path="/forgetPassword/forgetPasswordsendResetLink")
     * @ControllerNameAnnotation("忘记密码重置链接")
     */
    public function forgetPasswordsendResetLink(): ResponseInterface
    {
        $email = $this->request->input('email');
        
        // 基本验证
        if (empty($email)) {
            return $this->response->error('', '邮箱不能为空');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return $this->response->error('', '邮箱格式错误');
        }
        
        $result = $this->userService->forgetPasswordSendResetLink($email);
        return $this->response->success($result, '密码重置链接已发送到您的邮箱，请在30分钟内完成重置');
    }

    /**
     * 验证重置令牌
     * @RequestMapping(path="/forgetPassword/forgetPasswordVerifyToken")
     * @ControllerNameAnnotation("忘记密码验证重置令牌")
     */
    public function forgetPasswordVerifyToken(): ResponseInterface
    {
        $token = $this->request->input('token');
        $result = $this->userService->forgetPasswordVerifyResetToken($token);
        return $this->response->success($result);
    }

    /**
     * 重置密码
     * @RequestMapping(path="/forgetPassword/forgetPasswordResetPassword")
     * @ControllerNameAnnotation("忘记密码重置密码")
     */
    public function forgetPasswordResetPassword(): ResponseInterface
    {
        $token = $this->request->input('token');
        $password = $this->request->input('password');
        
        // 基本验证
        if (empty($token)) {
            return $this->response->error('', '令牌不能为空');
        }
        
        $result = $this->userService->forgetPasswordResetPassword($token, $password);
        return $this->response->success($result, '密码重置成功，请使用新密码登录');
    }

}