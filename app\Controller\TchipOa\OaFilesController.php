<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\OaFilesService;
use App\Request\Project\OverViewRequest;
use App\Request\TchipOa\OaFiles\OaFilesEditRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("员工档案管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class OaFilesController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var OaFilesService
     */
    protected $service;

    public function getWeekWorkDate()
    {
        $id     = $this->request->input('id', '');
        $type   = $this->request->input('type', '');
        $result = $this->service->getWeekWorkDate($id, $type);
        return $this->response->success($result);
    }

    public function doEdit()
    {
        $request = make(OaFilesEditRequest::class);
        $request->validated();
        $id = (int) $this->request->input('id', 0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->doEdit($id ?? null, $params);
        return $this->response->success($result);
    }
}