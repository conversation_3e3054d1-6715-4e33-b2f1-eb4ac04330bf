<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\TchipOa;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipOa\UserPartService;
use App\Request\Project\OverViewRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("OA部门管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class UserPartController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var UserPartService
     */
    protected $service;


    public function getTreeList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getTreeList($filter, $op, $sort, $order);
        return $this->response->success($result);
    }
}