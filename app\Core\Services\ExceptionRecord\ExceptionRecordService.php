<?php

namespace App\Core\Services\ExceptionRecord;

use App\Constants\AssembleOrderCode;
use App\Constants\ProductionCode;
use App\Constants\ProductionOrderCode;
use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Exception\AppException;
use App\Model\TchipBi\AssembleOrderModel;
use App\Model\TchipBi\AttachmentModel;
use App\Model\TchipBi\ExceptionRecordModel;
use App\Model\TchipBi\ExceptionRecordRelationModel;
use App\Model\TchipBi\ExceptionRecordRelationReplyModel;
use App\Model\TchipBi\ProductionOrderModel;
use Exception;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class ExceptionRecordService extends BusinessService
{
    /**
     * @Inject()
     * @var ExceptionRecordModel
     */
    protected $model;

    public function getOverView($id)
    {
        $result = $this->model::query()->find($id);
        if ($result) {
            $result['attachments'] = !empty($result['attachment_ids']) ? AttachmentModel::query()->whereIn('id', $result['attachment_ids'])->get()->toArray() : [];
        }
        return $result ? $result->toArray() : [];

    }

    /**
     * 获取某一类型的异常记录列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return array
     */
    public function getRelateList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 999);
        //关联表
        $query = $query->leftJoin('exception_record_relation as relation', function ($join) {
            $join->on('exception_record.id', '=', 'relation.exception_record_id');
            $join->whereNull('relation.deleted_at');
        });
        $field = [
            'exception_record.*',
            'relation.id as relation_id',
            'relation.relate_product_code',
            'relation.relate_product_name',
        ];
        $list = $query->select($field)->orderBy($sort, $order)->get() ?: [];
        foreach ($list as $key => &$item) {
            $item['attachments'] = !empty($item['attachment_ids']) ? AttachmentModel::query()->whereIn('id', $item['attachment_ids'])->get()->toArray() : [];
            //统计反馈结果条数，以及最新的反馈内容
            $reply = make(ExceptionRecordReplyService::class)->getAllList(['exception_record_id' => $item['id']]);
            $item['reply_content'] = $reply[0]['reply_content'] ?? '';
            $item['reply_user_name'] = $reply[0]['user']['name']??'';
            $item['reply_count'] = count($reply);
            $item['reply_list'] = $reply;
        }
        return $list;
    }

    /**
     * 新增/编辑
     * @param int $id
     * @param array $values
     * @return bool|Builder|Model|int
     */
    public function doEdit(int $id, array $values)
    {
        Db::beginTransaction();
        try {
            if ($id > 0) {
                $row = $this->model::query()->find($id);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                $oldRow = $row->toArray();
                $result = $row->update($values);
                //状态改变，关联同类型产品的订单
                if (!empty($values['status']) && $values['status'] != $oldRow['status']) {
                    $this->doRelateOrder($id, $values);
                }
            } else {
                //目前只关联生产订单和组装订单，关联的产品必须从订单获取
                if (empty($values['relate_type']) || empty($values['relate_id'])) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.Missing_parameter'));
                }
                $relateInfo = $this->getRelateData($values['relate_type'], $values['relate_id']);
                if (!$relateInfo) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                $values['type'] = $values['relate_type'];
                $values['product_name'] = $relateInfo['product_name'];
                $values['product_code'] = $relateInfo['product_code'];
                $values['product_main_code'] = substr($relateInfo['product_code'], 0, -6);
                empty($values['created_by']) && $values['created_by'] = auth()->id() ?: 0;
                $result = $this->model::query()->create($values);

                if (!empty($result->id)) {
                    //添加第一个订单关联，以及反馈内容
                    $relationResult = ExceptionRecordRelationModel::query()->create([
                        'exception_record_id' => $result->id,
                        'relate_type'         => $values['relate_type'],
                        'relate_id'           => $values['relate_id'],
                        'relate_product_code' => $relateInfo['product_code'],
                        'relate_product_name' => $relateInfo['product_name'],
                        'is_first_related'    => 1
                    ]);
                    if (!empty($relationResult->id) && !empty($values['reply_content'])) {
                        $reply = [
                            'exception_record_id' => $result->id,
                            'relation_id'         => $relationResult->id,
                            'reply_content'       => $values['reply_content'],
                            'created_by'          => auth()->id() ?: 0
                        ];
                        make(ExceptionRecordReplyService::class)->doEdit(0, $reply);
                    }
                    //关联同类型产品的订单
                    $this->doRelateOrder($result->id, $values);
                }
            }
            //关联相同产品的订单
//            Db::rollBack();return;
            Db::commit();
            return $result;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 根据状态关联同类型产品的订单
     * @param $id
     * @param $values
     * @return void
     */
    public function doRelateOrder($id, $values)
    {
        //待处理以及完成不会再添加同类型产品的订单
        if (in_array($values['status'], [
            ProductionCode::EXCEPTION_STATUS_WAIT,
            ProductionCode::EXCEPTION_STATUS_FINISH
        ])) return;
        $exception = $this->model::query()->with(['relation'])->find($id);
        if (!$exception) return;
        //获取已经关联的订单
        $exception = $exception->toArray();
        $existRelateId = array_column($exception['relation'], 'relate_id');
        $needRelateList = $this->getNeedRelateList($exception);
        $needRelateId = array_column($needRelateList, 'id');
        $insertIdArr = array_diff($needRelateId, $existRelateId);
        if (!empty($insertIdArr)) {
            foreach ($needRelateList as $item) {
                if (in_array($item['id'], $insertIdArr)) {
                    ExceptionRecordRelationModel::query()->create([
                        'exception_record_id' => $id,
                        'relate_type'         => $exception['type'],
                        'relate_id'           => $item['id'],
                        'relate_product_code' => $item['product_code'],
                        'relate_product_name' => $item['product_name'],
                    ]);
                }
            }
        }
    }

    /**
     * 获取需要关联的订单
     * @param $values
     * @return array|mixed[]
     */
    public function getNeedRelateList($exception)
    {
        //通过主编号(产品编号去掉后6位)，来匹配订单
        switch ($exception['type']) {
            case ProductionCode::EXCEPTION_TYPE_PROUCTION_ORDER:
                //挂起是关联全部订单
                $query = ProductionOrderModel::query()->where('product_code', 'like', "{$exception['product_main_code']}%")->select([
                    'production_order.id',
                    'product_code',
                    'product_name'
                ]);
                //处理中，是关联未核准的订单
                if ($exception['status'] == ProductionCode::EXCEPTION_STATUS_DOING) {
                    $query = $query->join('production_order_info', 'production_order_info.production_order_id', '=', 'production_order.id')
                        ->where('production_order_info.approve_status', '=', ProductionOrderCode::APPROVE_STATUS_WAIT);
                }
                $list = $query->get();
                $list = $list ? $list->toArray() : [];
                break;
            case ProductionCode::EXCEPTION_TYPE_ASSEMBLE_ORDER:
                //挂起是关联全部订单
                $query = AssembleOrderModel::query()->where('product_code', 'like', "{$exception['product_main_code']}%")->select([
                    'assemble_order.id',
                    'product_code',
                    'product_name'
                ]);
                //处理中，是关联未核准的订单
                if ($exception['status'] == ProductionCode::EXCEPTION_STATUS_DOING) {
                    $query = $query->join('assemble_order_info', 'assemble_order_info.assemble_order_id', '=', 'assemble_order.id')
                        ->where('assemble_order_info.approve_status', '=', AssembleOrderCode::APPROVE_STATUS_WAIT);
                }
                $list = $query->get();
                $list = $list ? $list->toArray() : [];
                break;
            default:
                $list = [];
                break;
        }
        return $list;
    }

    /**
     * 获取关联订单的数据
     * @param $relateType
     * @param $ids
     * @return array|mixed|mixed[]
     */
    public function getRelateData($relateType, $ids)
    {
        $ids = (array)$ids;
        switch ($relateType) {
            case ProductionCode::EXCEPTION_TYPE_PROUCTION_ORDER:
                $list = ProductionOrderModel::query()->whereIn('id', $ids)->get();
                break;
            case ProductionCode::EXCEPTION_TYPE_ASSEMBLE_ORDER:
                $list = AssembleOrderModel::query()->whereIn('id', $ids)->get();
                break;
            default:
                $list = [];
                break;
        }
        $list = $list ? $list->toArray() : [];
        if (count($ids) == 1) {
            return $list[0] ?: [];
        }
        return $list;
    }

    /**
     * 订单新建时自动关联异常
     * @param $relateType
     * @param $relateId
     * @return void
     */
    public function doRelateException($relateType, $relateId)
    {
        $info = $this->getRelateData($relateType, $relateId);
        if (!$info) return;
        $mainCode = substr($info['product_code'], 0, -6);
        $list = $this->model::query()->where('product_main_code', $mainCode)
            ->whereIn('status', [
                ProductionCode::EXCEPTION_STATUS_DOING,
                ProductionCode::EXCEPTION_STATUS_SUSPEND
            ])->get();
        foreach ($list as $item) {
            ExceptionRecordRelationModel::query()->create([
                'exception_record_id' => $item->id,
                'relate_type'         => $relateType,
                'relate_id'           => $relateId,
                'relate_product_code' => $info['product_code'],
                'relate_product_name' => $info['product_name'],
            ]);
        }
    }

    /**
     * 删除
     * @param $ids
     * @return int
     */
    public function doDelete($ids): int
    {

        Db::beginTransaction();
        try {
            $ids = explode(',', $ids);
            $result = $this->model::destroy($ids);
            //删除关联关系
            ExceptionRecordRelationModel::query()->whereIn('exception_record_id', $ids)->delete();
            ExceptionRecordRelationReplyModel::query()->whereIn('exception_record_id', $ids)->delete();
            Db::commit();
            return $result;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }
}