<?php

namespace App\Core\Services\AssembleOrder;

use App\Constants\AssembleOrderCode;
use App\Constants\StatusCode;
use App\Constants\WorkFlowSceneCode;
use App\Core\Services\BusinessService;
use App\Core\Services\Notice\NoticeService;
use App\Core\Services\UserService;
use App\Exception\AppException;
use App\Model\TchipBi\AssembleOrderInfoModel;
use App\Model\TchipBi\AssembleOrderMaterialCompletenessModel;
use App\Model\TchipBi\AssembleOrderModel;
use App\Model\TchipBi\AttachmentModel;
use Exception;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;


class   AssembleOrderMaterialCompletenessService extends BusinessService
{
    const SCENE_TYPE = WorkFlowSceneCode::SCENE_ASSEMBLE_ORDER;
    /**
     * @Inject()
     * @var AssembleOrderMaterialCompletenessModel
     */
    protected $model;

    public function batchDoEdit($orderId, $data)
    {
        foreach ($data as $val) {
            if (empty($val['assemble_order_id'])) {
                $val['assemble_order_id'] = $orderId;
            }
            $this->doEdit(0, $val,false);
        }
        $this->updateAssembleOrderMaterialStatus($orderId);
    }

    public function doEdit(int $id, array $values,$isUpdateStatus = true)
    {
        DB::beginTransaction();
        try {
            $row = [];
            if ($id > 0) {
                $row = $this->model::query()->find($id);
            } elseif (!empty($values['assemble_order_id'] && !empty($values['type']))) {
                $row = $this->model::query()->where([
                    'assemble_order_id' => $values['assemble_order_id'],
                    'type'              => $values['type']
                ])->first();
            }
            $assembleOrderId = 0;
            $sendType = 0;

            if ($row) {//更新
                $assembleOrderId = $row->assemble_order_id;
                if($row->type == AssembleOrderCode::MATERIAL_TYPE_TAG){
                    if(!empty($values['audit_status']) && $values['audit_status'] != $row->audit_status){
                        $values['audit_user_id'] = auth()->id();
                        $sendType = $values['audit_status'] != AssembleOrderCode::ATTACHMENT_AUDIT_PASS?2:3;
                    }elseif(!empty($values['images']) && $values['images'] != $row->images){
                        if($row['audit_status'] == AssembleOrderCode::ATTACHMENT_AUDIT_REJECT){
                            $values['audit_status'] = AssembleOrderCode::ATTACHMENT_AUDIT_WAITING;
                        }
                        $sendType = 1;
                    }
                }
                $result = $row->update($values);
            } else {//新建基本信息
                if (empty($values['assemble_order_id']) || empty($values['type'])) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, '参数错误');
                }
                if($values['type'] == AssembleOrderCode::MATERIAL_TYPE_TAG && !empty($values['images'])){
                    $sendType = 1;
                }
                $assembleOrderId = $values['assemble_order_id'];
                $result = $this->model::query()->create($values);
            }
            $isUpdateStatus && $this->updateAssembleOrderMaterialStatus($assembleOrderId);
            $sendType && $this->sendTagNotice($sendType,$assembleOrderId);
            Db::commit();
            return $result;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }

    }

    /**
     * 便签图片上传审核以及驳回审核
     * @param $type
     * @param $orderId
     * @return void
     */
    public function sendTagNotice($type,$orderId)
    {
        $order = AssembleOrderModel::query()->find($orderId);
        $orderInfo = AssembleOrderInfoModel::query()->where('assemble_order_id',$orderId)->first();
        if(!$order || !$orderInfo)return;
        $params = make(AssembleOrderInfoService::class)->getCommonNoticeParams($order);

        $sendUser = 0;
        if($type==1){
            $sendUser = $orderInfo['assemble_user_id'];
            $params['notice_msg'] = '标签已经上传图片，请前往审核!';
        }elseif ($type == 2){
            $wareUser = make(UserService::class)->getUsersByRole(AssembleOrderCode::WARE_ROLE);
            $sendUser = array_column($wareUser, 'id');
            $params['notice_msg'] = '标签审核已通过，请知悉!';
        }elseif ($type == 3){
            $wareUser = make(UserService::class)->getUsersByRole(AssembleOrderCode::WARE_ROLE);
            $sendUser = array_column($wareUser, 'id');
            $params['notice_msg'] = '标签审核未通过，请前往重新上传!';
        }
        make(NoticeService::class)->commonAssembleOrderNotice($sendUser, $params);
    }


    /**
     * 更新订单的总齐料状态
     * @param $orderId
     * @return void
     */
    public function updateAssembleOrderMaterialStatus($orderId)
    {
        $materialType = array_keys(AssembleOrderCode::MATERIAL_TYPE);
        $statusArr = $this->model::query()->where(['assemble_order_id' => $orderId])->whereIn('type', $materialType)->pluck('status')->toArray();
        $uniqueStatus = array_unique($statusArr);
        if (count($statusArr) !== count($materialType)) {
            if (in_array(AssembleOrderCode::MATERIAL_STATUS_FINISHED, $uniqueStatus)) {
                $materialStatus = AssembleOrderCode::ORDER_MATERIAL_STATUS_PART;
            } else {
                $materialStatus = AssembleOrderCode::ORDER_MATERIAL_STATUS_NOT;
            }
        } else {
            if (count($uniqueStatus) == 1 && $statusArr[0] == AssembleOrderCode::MATERIAL_STATUS_WAITING) {
                //全部等待中
                $materialStatus = AssembleOrderCode::ORDER_MATERIAL_STATUS_NOT;
            } elseif (count($uniqueStatus) == 1 && $statusArr[0] == AssembleOrderCode::MATERIAL_STATUS_FINISHED) {
                //全部齐料
                $materialStatus = AssembleOrderCode::ORDER_MATERIAL_STATUS_COMPLETE;
            } else {
                //部分齐料
                $materialStatus = AssembleOrderCode::ORDER_MATERIAL_STATUS_PART;
            }
        }
        $data = [
            'assemble_order_id' => $orderId,
            'material_status'   => $materialStatus,
        ];
        make(AssembleOrderInfoService::class)->doEdit(0, $data);
    }

    public function getMaterialStatus($orderId)
    {
        $materialType = AssembleOrderCode::MATERIAL_TYPE;
        $materialStatus = AssembleOrderCode::MATERIAL_STATUS;
        $data = [];
        foreach ($materialType as $type => $name) {
            $info = $this->model::query()
                ->where([
                    'assemble_order_id' => $orderId,
                    'type'              => $type
                ])->first();
            if ($info) {
                $info = $info->toArray();
                $info['attachments'] = $info['attachment_ids'] ? AttachmentModel::query()->whereIn('id', $info['attachment_ids'])->get()->toArray() : [];
                $info['type_name'] = $name;
                $info['status_name'] = $materialStatus[$info['status']] ?? '';
                $info['images']= $info['images']?:[];
                $data[] = $info;
            } else {
                $data[] = [
                    'type'              => $type,
                    'type_name'         => $name,
                    'assemble_order_id' => $orderId,
                    'attachments'       => [],
                    'attachment_ids'    => [],
                    'images'    => [],
                    'status'            => AssembleOrderCode::MATERIAL_STATUS_WAITING,
                    'status_name'       => $materialStatus[AssembleOrderCode::MATERIAL_STATUS_WAITING],
                    'audit_status'      => AssembleOrderCode::ATTACHMENT_AUDIT_WAITING,
                ];
            }
        }
        return $data;
    }

}