<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/28 上午10:30
 * <AUTHOR> X
 * @Description
 */

namespace App\Controller\TchipBbs;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipBbs\ChannelService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\BbsUserMiddleware;

/**
 * @ControllerNameAnnotation("文化频道管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ChannelController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ChannelService
     */
    protected $service;

    public function getList()
    {
        return parent::getList(); // TODO: Change the autogenerated stub
    }

    public function getTreeList()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->getTreeList($filter, $op, $sort, $order);
        return $this->response->success($result);
    }

    /**
     * @Middleware(BbsUserMiddleware::class)
     */
    public function doEdit()
    {
        return parent::doEdit(); // TODO: Change the autogenerated stub
    }
}