<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/28 上午10:30
 * <AUTHOR> X
 * @Description
 */

namespace App\Controller\TchipBbs;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipBbs\ArchivesService;
use App\Request\TchipBbs\VoteRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use App\Middleware\BbsUserMiddleware;

/**
 * @ControllerNameAnnotation("文章管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class ArchivesController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var ArchivesService
     */
    protected $service;

    public function getList()
    {
        return parent::getList(); // TODO: Change the autogenerated stub
    }


    /**
     * @Middleware(BbsUserMiddleware::class)
     */
    public function doEdit()
    {
        return parent::doEdit();
    }

    public function overView()
    {
        return parent::overView(); // TODO: Change the autogenerated stub
    }

    public function product()
    {
        list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
        $result = $this->service->product($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    public function categoryArchivesList()
    {
        $parentId = $this->request->input('parent_id');
        $cateNum = $this->request->input('cate_num', 4);
        $archivesNum = $this->request->input('archives_num', 8);
        $result = $this->service->categoryArchivesList((int) $parentId, (int) $cateNum, (int) $archivesNum);
        return $this->response->success($result);
    }

    /**
     * 阅读等操作
     * @Middleware(BbsUserMiddleware::class)
     */
    public function increase()
    {
        $id = $this->request->input('id');
        $value = $this->request->input('value');
        $field = $this->request->input('field');
        $result = $this->service->increase($id, $value, $field);
        return $this->response->success($result);
    }

    /**
     * 赞与踩
     * @Middleware(BbsUserMiddleware::class)
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function vote(VoteRequest $request)
    {
        $validated = $request->validated();
        $result = $this->service->vote($validated['id'], $validated['type'], $validated['value'], $validated['act']);
        return $this->response->success($result);
    }

}