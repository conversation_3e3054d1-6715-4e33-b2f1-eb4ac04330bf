<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/16 下午7:31
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\WorkWx;

use App\Annotation\WorkWxTokenAnnotation;
use App\Constants\AutoUserCountCode;
use App\Controller\BaseController;
use App\Core\Services\WorkWx\WorkWxAgentService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middlewares;
use Hyperf\HttpServer\Annotation\Middleware;
use Psr\Http\Message\ResponseInterface;

/**
 * 企业微信-设置应用在工作台展示
 * @AutoController()
 * @WorkWxTokenAnnotation(type="agent_tchip_bi")
 */
class BenchController extends BaseController
{
    /**
     * @Inject()
     * @var WorkWxAgentService
     */
    protected $workWxAgentService;

    /**
     * 设置应用在工作台展示的模版
     * @return ResponseInterface
     */
    public function setWorkbenchTemplate(): ResponseInterface
    {
        $result = $this->workWxAgentService->setWorkbenchTemplate();
        return $this->response->success($result);
    }

    /**
     * 获取应用在工作台展示的模版
     * @return ResponseInterface
     */
    public function getWorkbenchTemplate(): ResponseInterface
    {
        $result = $this->workWxAgentService->getWorkbenchTemplate();
        return $this->response->success($result);
    }

    /**
     * 设置应用在工作台展示的数据
     * @return ResponseInterface
     */
    public function setWorkbenchData(): ResponseInterface
    {
        $result = $this->workWxAgentService->setWorkbenchData();
        return $this->response->success($result);
    }
}