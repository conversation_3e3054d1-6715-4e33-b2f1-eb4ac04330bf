<?php
declare(strict_types=1);

namespace App\Core\Services\Index\Quota;


class QuotaManager
{
    protected $driver = 'default';

    protected $method;

    protected $provider = 'App\Core\Services\Index\Quota\\';

    public function __construct($driver, $name)
    {
        $this->driver = $this->getDriver($driver);
        $this->method = $name;
    }

    public function getQuota(...$params)
    {
        if (class_exists($this->driver) && method_exists($this->driver, $this->method) ) {
            return make($this->driver)->{$this->method}(...$params);
        }
        return '';
    }

    protected function getDriver($driver)
    {
        return $this->provider . $driver;
    }
}
