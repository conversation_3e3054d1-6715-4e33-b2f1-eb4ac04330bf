<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:59
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing;

use App\Constants\DataBaseCode;
use App\Constants\MarketingCode;
use App\Core\Services\TchipBi\ProductService;
use App\Core\Utils\TimeUtils;
use App\Model\Marketing\MarketingPlatform;
use App\Model\TchipBi\CategoryModel;
use App\Model\TchipBi\MarketingPlatformModel;
use App\Model\TchipSale\ClientSaleKyptTableModel;
use App\Model\TchipSale\LinkageModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use App\Constants\CommonCode;
use App\Core\Services\TchipSale\SaleService;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\Time;

/**
 * 品牌运营-推广平台
 */
class MarketingPlatformMonthReportService extends \App\Core\Services\BusinessService
{
    /**
     * 多月平台数据列表(按日计算，按月汇总）
     *
     * 该方法用于统计某品牌在指定日期范围内（按天）在各个平台的销售数据，并按月汇总返回结果。
     *
     * @param string $brandCode 品牌代码，用于标识具体品牌。
     * @param string $date 时间范围，格式为 'YYYY-MM-DD - YYYY-MM-DD'。
     * @param string|null $productName 产品名称，可选参数。如果指定该参数，则只统计该产品的销售数据。
     * @return array 返回包含各个月份销售数据的数组，每个元素包含各平台的销售额和销售量。
     */
    public function brandStatisticsSalesByDay($brandCode, $date, $productName = null)
    {
        // 获取所有销售平台，类型为销售平台且状态为启用
        $salePlatforms = MarketingPlatform::query()->selectRaw('id, name, IFNULL(sales_name, name) as sales_name, type, status')
            ->where('type', MarketingCode::PLATFORM_TYPE_SALE)
            ->where('status', 1)
            ->get();

        // 如果没有销售平台数据，返回空数组
        if (!$salePlatforms) {
            return [];
        }

        // 转换平台数据为数组并提取平台的名称
        $salePlatforms = $salePlatforms->toArray();
        $platformNames = array_column($salePlatforms, 'sales_name');
        $salePlatforms = array_column($salePlatforms, null, 'sales_name');

        // 获取对应平台名称的联动数据
        $salesLinkage = LinkageModel::query()->whereIn('text', $platformNames)->get();

        // 如果没有联动数据，返回空数组
        if (!$salesLinkage) {
            return [];
        }

        // 转换联动数据为数组，并提取联动 ID
        $salesLinkage = $salesLinkage->toArray();
        $salesIds     = array_column($salesLinkage, 'id');
        $salesLinkage = array_column($salesLinkage, null, 'text');

        // 根据传入的产品名称决定获取品牌的所有产品还是指定产品
        if (!$productName) {
            // 获取品牌的市场产品列表
            $products = make(ProductService::class)->getBrandMarketProducts($brandCode);
        } else {
            // 根据产品名称获取产品列表
            $products = make(ProductService::class)->getAllList(['product_name' => $productName], ['product_name' => 'IN']);
        }

        // 如果没有产品数据，返回空数组
        if (!$products) {
            return [];
        }

        // 提取产品的销售产品 ID
        $productSaleIds = array_column($products, 'sale_product_id');

        // 构造查询语句，用于计算人民币和美元的销售额，以及按日分组的数据
        $select = "
            (SUM(IF(num > 0, IF(curr='美元', 0, IF(ratemoney <> 0, ratemoney, money)), 0))) as rmb, 
            (SUM(IF(num > 0, IF(curr='美元', IF(ratemoney <> 0, ratemoney, money), 0), 0))) as dollar, 
            FROM_UNIXTIME(UNIX_TIMESTAMP(saledate), '%Y-%m-%d') as date, 
            sale_id, 
            SUM(IF(num > 0, num, 0)) as volume,  -- 原查询保持不变
            
            (SUM(IF(num < 0, IF(curr='美元', 0, IF(ratemoney <> 0, ratemoney, money)), 0))) as rmb_negative, 
            (SUM(IF(num < 0, IF(curr='美元', IF(ratemoney <> 0, ratemoney, money), 0), 0))) as dollar_negative, 
            SUM(IF(num < 0, num, 0)) as volume_negative
        ";


        // 构造筛选条件和操作符
        $filter  = [
            // 'delete_time' => 0,  // 筛选未被删除的数据
            // 'replenish'   => 0,  // 筛选非补货的数据
            'prod_id'     => implode(',', $productSaleIds),  // 产品 ID 列表
            'sale_id'     => implode(',', $salesIds),        // 平台 ID 列表
        ];
        $op      = [
            'prod_id' => 'IN',  // 对产品 ID 进行 IN 操作
            'sale_id' => 'IN',  // 对平台 ID 进行 IN 操作
        ];

        // 处理时间范围，直接使用传入的起止日期
        $dateArr = explode(' - ', $date);
        if (count($dateArr) == 2) {
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        } else {
            return [];  // 如果时间格式不符合要求，返回空数组
        }

        // 构造查询语句，并按日期（天）和平台分组，获取销售数据
        $saleModel = make(ClientSaleKyptTableModel::class);
        list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 9999, $saleModel->getKyptPjSaleSelect($saleModel->kyptCountSelect, $saleModel->pjCountSelect));
        $saleData = $query->selectRaw($select);


        if ($productName == 'Station M2' || $productName == 'Station M3') {
            //20240829 stationM2 stationM3 查询条件 只有电商仓 或 dwin_user_main_table.part_group_id = 53里所有的user_id
            $userIds = DB::connection(DataBaseCode::TCHIP_SALE)
                ->table('user_main_table')
                ->where('part_group_id', 53) // 该组暂定名称为‘天猫’
                ->pluck('user_id')
                ->toArray();

            $saleData = $saleData->where(function ($query) use ($userIds) {
                $query
                    ->where('cargo_space', '=', '027')
                    ->orWhereIn('user_id', $userIds); // 这里使用之前获取的user_id数组
            });
        }

        $saleData = $saleData->groupBy('date')
            ->groupBy('sale_id')
            ->orderBy('saledate', 'desc')->get();

        if ($saleData) {
            $saleData = dbToArray($saleData);
        }

        // 初始化结果数组，包含指定时间段内的每月数据
        $result   = [];
        $monthNum = TimeUtils::calculateMonthDifference($dateArr[0], $dateArr[1]);
        for ($i = 0; $i <= $monthNum; $i++) {
            $dateItem = $i == 0 ? date('Y-m', strtotime($dateArr[0])) : date('Y-m', strtotime("+{$i} month", strtotime($dateArr[0])));
            // 初始化基础字段
            $result[$dateItem] = [
                'rmb'             => 0,  // 人民币销售额
                'rmb_negative'    => 0,  // 人民币负销售额
                'dollar'          => 0,  // 美元销售额
                'dollar_negative' => 0,  // 美元负销售额
                'amount'          => 0,  // 总销售额（以人民币计算）
                'volume'          => 0,  // 销售量
                'volume_negative' => 0,  // 负销售量
                'date'            => $dateItem,  // 当前月份
            ];

            // 初始化各平台的销售额和销售量字段
            foreach ($salesIds as $saleId) {
                $result[$dateItem]['amount_' . $saleId] = 0;               // 各平台的销售额
                $result[$dateItem]['amount_' . $saleId . '_negative'] = 0; // 各平台的负销售额
                $result[$dateItem]['volume_' . $saleId] = 0;               // 各平台的销售量
                $result[$dateItem]['volume_' . $saleId . '_negative'] = 0; // 各平台的负销售量
            }
        }

        // 各月的汇率，按月计算
        $rate = [];
        foreach ($saleData as $datum) {
            $month = date('Y-m', strtotime($datum['date']));

            if (empty($rate[$month])) {
                $rate[$month] = nowUsdRate($datum['date']);
            }

            // 初始化判断
            $result[$month]['rmb']             = $result[$month]['rmb'] ?? 0;
            $result[$month]['rmb_negative']    = $result[$month]['rmb_negative'] ?? 0;

            $result[$month]['dollar']          = $result[$month]['dollar'] ?? 0;
            $result[$month]['dollar_negative'] = $result[$month]['dollar_negative'] ?? 0;

            $result[$month]['volume']          = $result[$month]['volume'] ?? 0;
            $result[$month]['volume_negative'] = $result[$month]['volume_negative'] ?? 0;

            $result[$month]['amount']          = $result[$month]['amount'] ?? 0;
            $result[$month]['amount_' . $datum['sale_id']] = $result[$month]['amount_' . $datum['sale_id']] ?? 0;
            $result[$month]['amount_' . $datum['sale_id'] . '_negative'] = $result[$month]['amount_' . $datum['sale_id'] . '_negative'] ?? 0;

            $result[$month]['volume_' . $datum['sale_id']] = $result[$month]['volume_' . $datum['sale_id']] ?? 0;
            $result[$month]['volume_' . $datum['sale_id'] . '_negative'] = $result[$month]['volume_' . $datum['sale_id'] . '_negative'] ?? 0;

            // 计算各月的销售数据
            $result[$month]['rmb']             += $datum['rmb'] ?? 0;
            $result[$month]['rmb_negative']    += $datum['rmb_negative'] ?? 0;

            $result[$month]['dollar']          += $datum['dollar'] ?? 0;
            $result[$month]['dollar_negative'] += $datum['dollar_negative'] ?? 0;

            $result[$month]['volume']          += $datum['volume'] ?? 0;
            $result[$month]['volume_negative'] += $datum['volume_negative'] ?? 0;

            // 计算总销额（人民币和美元按汇率转换后相加）
            $result[$month]['amount']          += (($datum['rmb'] ?? 0) + ($datum['rmb_negative'] ?? 0))
                + (($datum['dollar'] ?? 0) + ($datum['dollar_negative'] ?? 0)) * $rate[$month];

            // 计算各平台的销售额和销售量
            $result[$month]['amount_' . $datum['sale_id']]               += ($datum['rmb'] ?? 0) + ($datum['dollar'] ?? 0) * $rate[$month];
            $result[$month]['amount_' . $datum['sale_id'] . '_negative'] += ($datum['rmb_negative'] ?? 0) + ($datum['dollar_negative'] ?? 0) * $rate[$month];

            $result[$month]['volume_' . $datum['sale_id']]               +=  $datum['volume'] ?? 0;
            $result[$month]['volume_' . $datum['sale_id'] . '_negative'] += $datum['volume_negative'] ?? 0;


        }

        // 第二个foreach循环，处理结果数据
        foreach ($result as $month => &$data) {
            // 将rmb、rmb_negative、dollar限制为两位小数
            $data['rmb'] = round($data['rmb'], 2);
            $data['rmb_negative'] = round($data['rmb_negative'], 2);
            $data['dollar'] = round($data['dollar'], 2);
            $data['dollar_negative'] = round($data['dollar_negative'], 2);

            // 处理amount和其他计算值
            $data['amount'] = round($data['amount'], 2);

            // 处理每个带有 sale_id 的 amount 和 volume 相关字段
            foreach ($data as $key => $value) {
                if (strpos($key, 'amount_') === 0 || strpos($key, 'volume_') === 0) {
                    $data[$key] = round($value, 2);
                }
            }

            // 计算rmb和rmb_negative的汇总值
            $data['rmb_sum'] = $data['rmb'] + $data['rmb_negative'];

            // 计算dollar和dollar_negative的汇总值
            $data['dollar_sum'] = $data['dollar'] + $data['dollar_negative'];

            // 计算量的总和
            $data['volume_sum'] = $data['volume'] + $data['volume_negative'];

            // 处理各sale_id的amount和volume汇总
            foreach ($data as $key => $value) {
                if (strpos($key, 'amount_') === 0 && strpos($key, '_negative') === false) {
                    // 计算带有sale_id的汇总值
                    $negative_key = $key . '_negative';
                    $data[$key . '_sum'] = $value + ($data[$negative_key] ?? 0);
                }

                if (strpos($key, 'volume_') === 0 && strpos($key, '_negative') === false) {
                    // 计算带有sale_id的volume汇总值
                    $negative_key = $key . '_negative';
                    $data[$key . '_sum'] = $value + ($data[$negative_key] ?? 0);
                }
            }
        }

        // 按日期倒序返回结果数组
        return array_values(collect($result)->sortByDesc('date')->toArray());
    }



    /**
     * 多月平台数据列表(按月计算）
     * @param $brandCode
     * @param $date
     * @return array
     */
    public function brandStatisticsSales($brandCode, $date, $productName = null)
    {
        $salePlatforms = MarketingPlatform::query()->selectRaw('id, name, IFNULL(sales_name, name) as sales_name, type, status')
            ->where('type', MarketingCode::PLATFORM_TYPE_SALE)->where('status', 1)->get();
        if (!$salePlatforms) {
            return [];
        }
        $salePlatforms = $salePlatforms->toArray();
        $platformNames = array_column($salePlatforms, 'sales_name');
        $salePlatforms = array_column($salePlatforms, null, 'sales_name');

        $salesLinkage = LinkageModel::query()->whereIn('text', $platformNames)->get();
        if (!$salesLinkage) {
            return [];
        }
        $salesLinkage = $salesLinkage->toArray();
        $salesIds     = array_column($salesLinkage, 'id');
        $salesLinkage = array_column($salesLinkage, null, 'text');

        if (!$productName) {
            $products = make(ProductService::class)->getBrandMarketProducts($brandCode);
        } else {
            $products = make(ProductService::class)->getAllList(['product_name' => $productName], ['product_name' => 'IN']);
        }
        if (!$products) {
            return [];
        }
        $productSaleIds = array_column($products, 'sale_product_id');

        // 目前只统计人民币与美元，港元包含在人民币里
        $select = "(SUM( IF(curr='美元', 0, IF(ratemoney <> 0,ratemoney,money)))) as rmb, (SUM( IF(curr='美元', IF(ratemoney <> 0,ratemoney,money),0))) as dollar, FROM_UNIXTIME(UNIX_TIMESTAMP(saledate),'%Y-%m') as date, sale_id, SUM(num) as volume";

        $filter  = [
            // 'delete_time' => 0,
            // 'replenish'   => 0,
            'prod_id'     => implode(',', $productSaleIds),
            'sale_id'     => implode(',', $salesIds),
        ];
        $op      = [
            'prod_id' => 'IN',
            'sale_id' => 'IN',
        ];
        $dateArr = explode(' - ', $date);
        if (count($dateArr) == 2) {
            $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
            $date       = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        } else {
            $month              = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0]         = $month['starttime'];
            $dateArr[1]         = $month['endtime'];
            $date               = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        }

        $saleModel = make(ClientSaleKyptTableModel::class);
        $query = $saleModel->getKyptPjSaleSelect($saleModel->kyptCountSelect, $saleModel->pjCountSelect);
        list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 9999, $query);
        $saleData = $query->selectRaw($select)->groupBy('date')->groupBy('sale_id')->orderBy('saledate', 'desc')->get();
        if ($saleData)  {
            $saleData = dbToArray($saleData);
        }
        $result   = [];
        $monthNum = TimeUtils::calculateMonthDifference($dateArr[0], $dateArr[1]);
        for ($i = 0; $i <= $monthNum; $i++) {
            $dateItem = $i == 0 ? date('Y-m', strtotime($dateArr[0])) : date('Y-m', strtotime("+{$i} month", strtotime($dateArr[0])));
            $result[$dateItem] = [
                'rmb'    => 0,
                'dollar' => 0,
                'amount' => 0,
                'volume' => 0,
                'date'   => $dateItem,
            ];
            // 生成各平台的字段
            foreach ($salesIds as $saleId) {
                $result[$dateItem]['amount_' . $saleId] = 0;
                $result[$dateItem]['volume_' . $saleId] = 0;
            }
        }
        // 各月的汇率
        $rate = [];
        foreach ($saleData as $datum) {
            if (empty($rate[$datum['date']])) {
                $rate[$datum['date']] = nowUsdRate($datum['date']);
            }

            $result[$datum['date']]['rmb']             += $datum['rmb'];
            $result[$datum['date']]['dollar']          += $datum['dollar'];
            $result[$datum['date']]['volume']          += $datum['volume'];
            // 当月总销额
            $result[$datum['date']]['amount']    += $datum['rmb'] + $datum['dollar'] * $rate[$datum['date']];
            // 指定平台销额
            $result[$datum['date']]['amount_' . $datum['sale_id']] += $datum['rmb'] + $datum['dollar'] * $rate[$datum['date']];
            $result[$datum['date']]['volume_' . $datum['sale_id']] += $datum['volume'];
        }
        return array_values(collect($result)->sortByDesc('date')->toArray());
    }

    public function monthPlatformReport($brandCode, $date, $productName = null)
    {
        $salePlatforms = make(MarketingPlatformService::class)->getAllList(['type' => MarketingCode::PLATFORM_TYPE_SALE, 'status' => 1]);
        if (!$salePlatforms) {
            return [];
        }

        if ($productName) {
            $products = make(ProductService::class)->getAllList(['product_name' => $productName], ['product_name' => 'IN']);
        } else {
            $products     = make(ProductService::class)->getBrandMarketProducts($brandCode);
            if (!$products) {
                return [];
            }
        }

        $salesIds     = array_column($salePlatforms, 'sale_platform_id');
        $productSaleIds = array_column($products, 'sale_product_id');

        // 目前只统计人民币与美元，港元包含在人民币里
        $select = "
            (SUM(IF(curr='美元', 0, IF(ratemoney <> 0, ratemoney, money)))) as rmb, 
            (SUM(IF(curr='美元', IF(ratemoney <> 0, ratemoney, money), 0))) as dollar, 
            FROM_UNIXTIME(UNIX_TIMESTAMP(saledate), '%Y-%m') as date, 
            sale_id, 
            SUM(num) as volume, 
            
           -- 负值相关字段
            (SUM(IF(num < 0 AND curr != '美元', IF(ratemoney <> 0, ratemoney, money), 0))) as rmb_negative, 
            (SUM(IF(num < 0 AND curr = '美元', IF(ratemoney <> 0, ratemoney, money), 0))) as dollar_negative, 
            SUM(IF(num < 0, num, 0)) as volume_negative
        ";

        $filter  = [
            // 'delete_time' => 0,
            // 'replenish'   => 0,
            'prod_id'     => implode(',', $productSaleIds),
            'sale_id'     => implode(',', array_filter($salesIds)),
        ];
        $op      = [
            'prod_id' => 'IN',
            'sale_id' => 'IN',
        ];
        $dateArr = explode(' - ', $date);
        if (count($dateArr) == 2) {
            $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
            $date       = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        } else {
            $month              = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0]         = $month['starttime'];
            $dateArr[1]         = $month['endtime'];
            $date               = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        }

        // 1.1. 开源平台销售表查询语句
        $saleModel = make(ClientSaleKyptTableModel::class);
        // 获取销售表联表查询
        $query = $saleModel->getKyptPjSaleSelect($saleModel->kyptCountSelect, $saleModel->pjCountSelect);
        list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 9999, $query);
        // list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 9999, ClientSaleKyptTableModel::query());
        $saleData = $query->selectRaw($select);

        if ($productName == 'Station M2' || $productName == 'Station M3') {
            //20240829 stationM2 stationM3 查询条件 只有电商仓 或 dwin_user_main_table.part_group_id = 53里所有的user_id
            $userIds = DB::connection(DataBaseCode::TCHIP_SALE)
                ->table('user_main_table')
                ->where('part_group_id', 53) // 该组暂定名称为‘天猫’
                ->pluck('user_id')
                ->toArray();

            $saleData = $saleData->where(function ($query) use ($userIds) {
                $query
                    ->where('cargo_space', '=', '027')
                    ->orWhereIn('user_id', $userIds); // 这里使用之前获取的user_id数组
            });
        }

        $saleData = $saleData->groupBy('date')->groupBy('sale_id')->orderBy('saledate', 'desc')->get();
        if ($saleData) {
            // DB查询的结果如果使用->toArray，子数组还是claClass的结构 故使用json_decode方法
            $saleData = dbToArray($saleData);
        }
        $saleData = $saleData ? array_column($saleData, null, 'sale_id') : [];
        $rate = [];
        //foreach ($salePlatforms as &$platform) {
        //    if (!empty($saleData[$platform['sale_platform_id']])) {
        //        if (empty($rate[$datum['date']])) {
        //            $rate[$saleData[$platform['sale_platform_id']]['date']] = nowUsdRate($saleData[$platform['sale_platform_id']]['date']);
        //        }
        //        $platform = array_merge($platform, $saleData[$platform['sale_platform_id']]);
        //        $platform['amount'] = round($platform['rmb'] + ($platform['dollar'] * $rate[$saleData[$platform['sale_platform_id']]['date']]), 2);
        //    } else {
        //        $platform['rmb'] = 0;
        //        $platform['dollar'] = 0;
        //        $platform['amount'] = 0;
        //        $platform['volume'] = 0;
        //    }
        //}
        foreach ($salePlatforms as &$platform) {
            if (!empty($saleData[$platform['sale_platform_id']])) {
                $date = $saleData[$platform['sale_platform_id']]['date'];
                if (empty($rate[$date])) {
                    $rate[$date] = nowUsdRate($date);
                }
                $platform = array_merge($platform, $saleData[$platform['sale_platform_id']]);
                $platform['amount'] = round($platform['rmb'] + ($platform['dollar'] * $rate[$date]), 2);
            } else {
                $platform['rmb'] = 0;
                $platform['dollar'] = 0;
                $platform['amount'] = 0;
                $platform['volume'] = 0;
            }
        }
        return $salePlatforms;
    }


}