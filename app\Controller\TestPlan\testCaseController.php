<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2024/08/12 17:00
     * <AUTHOR>
     * @Description
     */

    namespace App\Controller\TestPlan;
    use App\Annotation\ControllerNameAnnotation;
    use App\Controller\BaseController;
    use App\Core\Services\TestPlan\TestCaseService;
    use App\Middleware\AuthMiddleware;
    use App\Middleware\MenuMiddleware;
    use App\Request\IdsRequest;
    use Hyperf\Di\Annotation\Inject;
    use Hyperf\HttpServer\Annotation\AutoController;
    use Hyperf\HttpServer\Annotation\Middleware;

    /**
     * @ControllerNameAnnotation("测试用例")
     * @AutoController(prefix="/test_plan/test_case/index")
     * @Middleware(AuthMiddleware::class)
     */
    class testCaseController extends \App\Controller\BaseController
    {
        /**
         * @Inject()
         * @var TestCaseService
         */
        protected $service;


        /**
         * @ControllerNameAnnotation("测试用例-列表查询")
         */
        public function getList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $result = $this->service->getList($filter, $op, $sort, $order, (int)$limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("测试用例-用例详情")
         */
        public function overView()
        {
            $id = $this->request->input('id');
            $result = $this->service->getOverView($id);


            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("测试用例-增改")
         */
        public function doEdit(): \Psr\Http\Message\ResponseInterface
        {
            $id = (int) $this->request->input('id', 0);
            $params = $this->request->all();
            unset($params['id']);
            $result = $this->service->doEdit($id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("测试用例-删除")
         */
        public function doDelete(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doDelete($validated['ids']);
            return $this->response->success($result);
        }



        /**
         * @ControllerNameAnnotation("测试用例-目录-列表查询（左侧卡片）")
         */
        public function getDirList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $page = $this->request->input('pageNo');
            $result = $this->service->getDirList($filter, $op, $sort, $order, (int) $limit, $page, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("测试用例-目录-增改")
         */
        public function doEditDir(): \Psr\Http\Message\ResponseInterface
        {
            $id = (int) $this->request->input('id', 0);
            $params = $this->request->all();
            unset($params['id']);
            $result = $this->service->doEditDir($id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("测试用例-目录-删除")
         */
        public function doDeleteDir(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doDeleteDir($validated['ids']);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("用例库（版本）-列表查询")
         */
        public function getLibList(): \Psr\Http\Message\ResponseInterface
        {
            $sortInput = (int) $this->request->input('sort', 0);
            list($filter, $op, $sort, $order, $limit, $search, $searchFields) = $this->getParams();
            $sort = $sortInput == 0 ? 'sort' : $sortInput;
            $result = $this->service->getLibList($filter, $op, $sort, $order, (int) $limit, $search, $searchFields);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("用例库（版本）-增改")
         */
        public function doEditLib()
        {
            $id = (int) $this->request->input('id', 0);
            $params = $this->request->all();
            unset($params['id']);
            $result = $this->service->doEditLib($id, $params);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("用例库（版本）-删除")
         */
        public function doDeleteLib(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doDeleteLib($validated['ids']);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("用例步骤-增改")
         */
        public function doEditStep()
        {
            $id = (int) $this->request->input('id', 0);
            $noLog = $this->request->input('no_log', false);
            $params = $this->request->all();
            unset($params['id']);
            unset($params['no_log']);
            $result = $this->service->doEditStep($id, $params, $noLog);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("用例步骤-删除")
         */
        public function doDeleteStep(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doDeleteStep($validated['ids']);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("关联事项-增改")
         */
        public function doEditIssueRelation()
        {
            $id = (int) $this->request->input('id', 0);
            $noLog = $this->request->input('no_log', false);
            $params = $this->request->all();
            unset($params['id']);
            unset($params['no_log']);
            $result = $this->service->doEditIssueRelation($id, $params, $noLog);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("关联事项-删除")
         */
        public function doDeleteIssueRelation(): \Psr\Http\Message\ResponseInterface
        {
            $validated = make(IdsRequest::class)->validated();
            $result = $this->service->doDeleteIssueRelation($validated['ids']);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("附件信息修改")
         */
        public function updateAttachment()
        {
            $id = $this->request->input('id',0);
            $noLog = $this->request->input('no_log', false);
            $params = $this->request->all();
            unset($params['id']);
            unset($params['no_log']);

            $result = $this->service->updateAttachment($id,$params, $noLog);
            return $result ? $this->response->success('上传成功') : $this->response->error('上传失败');
        }

        /**
         * @ControllerNameAnnotation("附件删除")
         */
        public function deleteAttachment()
        {
            $id = $this->request->input('id',0);
            $result = $this->service->deleteAttachment($id);
            return $result ? $this->response->success('附件删除成功') : $this->response->error('附件删除失败');

        }

        /**
         * @ControllerNameAnnotation("模块列表（为用例统计）")
         */
        public function getCategoryList()
        {
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
            $result = $this->service->getCategoryList($filter, $op, $sort, $order, $limit);
            return $this->response->success($result);

        }

        /**
         * @ControllerNameAnnotation("用例变更日志列表")
         */
        public function getCaseChangeLogList(): \Psr\Http\Message\ResponseInterface
        {
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
            $result = $this->service->getCaseChangeLogList($filter, $op, $sort, $order, (int)$limit);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("用例树")
         */
        public function getProjectCaseTree()
        {
            $projectId = $this->request->input('project_id');
            $result = $this->service->getProjectCaseTree($projectId);

            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("复制用例和目录树")
         */
        public function copyCaseAndDir()
        {
            $treeData = $this->request->input('tree'); // 用例和目录的树形结构数据
            $projectId = $this->request->input('project_id'); // 项目ID
            $libraryId = $this->request->input('library_id'); // 目标库ID

            $result = $this->service->copyCaseAndDir($treeData, $projectId, $libraryId);

            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("用例-批量创建")
         */
        public function doBatchCreateCase()
        {
            $params = $this->request->all(); // 获取所有请求参数

            // 检查必要字段
            if (empty($params['title'])) {
                return $this->response->error('没有提供用例标题');
            }
            $result = $this->service->doBatchCreateCase($params);
            return  $result['success'] ? $this->response->success($result) : $this->response->error($result['errors']);
        }



        /**
         * 导入用例
         * @ControllerNameAnnotation("用例-导入")
         * @return \Psr\Http\Message\ResponseInterface
         */
        public function excelImport()
        {
            // 获取上传的文件
            $file = $this->request->file('file');

            $params = $this->request->all();

            $result = $this->service->excelImport($file, $params);
            // 将文件和参数数组传递给服务层
            return $result['status'] == 'success' ? $this->response->success($result) : $this->response->error($result);
        }

        /**
         * 导出用例
         * @ControllerNameAnnotation("用例-导出")
         * @return \Psr\Http\Message\ResponseInterface
         */
        public function exportExcel()
        {
            list($filter, $op, $sort, $order, $limit) = $this->getParams();
            $result = $this->service->exportExcel($filter, $op, $sort, $order, (int)$limit);
            return $result;
        }

        /**
         * 批量修改用例
         * @ControllerNameAnnotation("用例-批量修改")
         */
        public function doBatchEdit()
        {
            $rows = $this->request->input('rows');
            $params = $this->request->input('params');
            $result = $this->service->doBatchEdit($rows, $params);
            return $result;
        }

        /**
         * 批量复制用例
         * @ControllerNameAnnotation("用例-批量复制")
         */
        public function doBatchCopyCase()
        {
            $rows = $this->request->input('rows');
            $params = $this->request->input('params');
            $result = $this->service->doBatchCopyCase($rows, $params);
            return $result;
        }

        /**
         * 批量删除
         * @ControllerNameAnnotation("用例-批量删除")
         */
        public function doBatchDeleteCase()
        {
            $rows = $this->request->input('rows');
            $result = $this->service->doBatchDeleteCase($rows);
            return $this->response->success($result);
        }

        /**
         * @ControllerNameAnnotation("重新排序函数")
         */
        public function rearrangement()
        {
            $dragNodeId = $this->request->input('drag_node_id');
            $targetNode = $this->request->input('target_node_data');
            $dropToGap = $this->request->input('drop_to_gap');
            $dropPosition = $this->request->input('drop_position');
            $result = $this->service->rearrangement($dragNodeId, $targetNode, $dropToGap, $dropPosition);
            return $this->response->success($result);
        }



    }