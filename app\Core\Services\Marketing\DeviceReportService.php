<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/9/13 下午4:37
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing;

use App\Constants\DataBaseCode;
use App\Core\Services\BusinessService;
use App\Core\Utils\Log;
use App\Model\StationPCManager\AppStatResultModel;
use App\Model\StationPCManager\DeviceModel;
use App\Model\StationPCManager\DeviceStatResultModel;
use Carbon\Carbon;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class DeviceReportService extends BusinessService
{
    /**
     * @Inject()
     * @var DeviceModel
     */
    protected $managerDeviceModel;

    /**
     * 设备统计
     * @return
     */
    public function statResult()
    {
        Log::get('system', 'system')->info('==============================================================================');
        Log::get('system', 'system')->info('开始采集设备数');
        $collectUserCountStartTime = microtime(true);
        $this->_statResult();
        $this->_statResult(DataBaseCode::STATIONPC_MANAGEREN);
        $executionTime = sprintf('%.2f', ((microtime(true) - $collectUserCountStartTime) * 1000));
        Log::get('system', 'system')->info('采集结束，耗时'.$executionTime.'毫秒');
        Log::get('system', 'system')->info('==============================================================================');
        return true;
    }

    /**
     * 设备统计执行方法
     * @param string $connection
     * @return Builder|Model|object|null
     */
    private function _statResult(string $connection = DataBaseCode::STATIONPC_MANAGERCN)
    {
        $startTime = strtotime(Carbon::yesterday()->format('Y-m-d'));
        $endTime = strtotime(Carbon::yesterday()->format('Y-m-d 23:59:59'));

        // 各平台 各型号 在线数/设备总数
        $items = $this->managerDeviceModel::on($connection)->select(
            array(
                'name',
                Db::raw('SUM(case when online = 1 then 1 else 0 end) AS online_device_count'),
                Db::raw('COUNT(*) AS total_devices'),
            )
        )->groupBy('name')->get()->toArray();

        // 各平台 各型号 时间段内新增设备数
        $newDeviceCount = $this->managerDeviceModel::on($connection)
            ->whereBetween('createtime', [$startTime, $endTime])->select(
                array(
                    'name',
                    Db::raw('COUNT(*) AS new_device_count'),
                )
            )->groupBy('name')->get();

        Log::get('system', 'system')->info($connection.',新增设备:'.$newDeviceCount);

        foreach ($items as &$item) {
            $findItem                 = $newDeviceCount->firstWhere('name', $item['name']);
            $item['new_device_count'] = $findItem['new_device_count'] ?? 0;
            $item['dimension']        = 'day';
            $item['stat_date']        = Carbon::yesterday()->format('Ymd');
            $item['start_time']       = Carbon::yesterday()->format('Y-m-d');
            $item['end_time']         = Carbon::yesterday()->format('Y-m-d 23:59:59');
            $item['platform']         = $connection;
            DeviceStatResultModel::query()->updateOrCreate(
                [
                    'stat_date' => Carbon::yesterday()->format('Ymd'),
                    'dimension' => 'day',
                    'platform'  => $item['platform'],
                    'name'      => $item['name']
                ],
                $item);
        }
        return $items;
    }

    /**
     * 获取面板数据
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return array
     */
    public function getPanelData($filter, $op, $sort, $order, $limit): array
    {
        $initData = $this->getInitData($filter, $op, $sort, $order, $limit)->get();
        $result = [
            'new_device_count_sum'        => $initData->sum('new_device_count_sum'),
            'online_device_count_sum'     => $initData->last()['online_device_count_sum'],
            'total_devices_sum'           => $initData->last()['total_devices_sum']
        ];
        return $result;
    }

    /**
     * 趋势图数据
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return array
     */
    public function getTrendData($filter, $op, $sort, $order, $limit)
    {
        return $this->getInitData($filter, $op, $sort, $order, $limit)->get();

    }

    /**
     * 图表数据
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return array
     */
    public function getTableData($filter, $op, $sort, $order, $limit)
    {
        $initData = $this->getInitData($filter, $op, $sort, $order, $limit);
        return $initData->orderBy('stat_date', 'desc')->paginate($limit);
    }

    /**
     * 基础数据
     * @param $filter
     * @param $op
     * @param $sort
     * @param $order
     * @param $limit
     * @return \Hyperf\Utils\Collection
     */
    private function getInitData($filter, $op, $sort, $order, $limit)
    {
        /* @var DeviceStatResultModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, DeviceStatResultModel::query());
        $query->groupBy('stat_date')->select(array(
            'stat_date',
            'dimension',
            'start_time',
            'end_time',
            Db::raw('SUM(new_device_count) AS new_device_count_sum'),
            Db::raw('SUM(online_device_count) AS online_device_count_sum'),
            Db::raw('SUM(total_devices) AS total_devices_sum'),
        ));
        return $query;
    }
}