<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/31 下午2:38
 * <AUTHOR> X
 * @Description
 */

namespace App\Core\Services\Index;

use App\Model\TchipBi\FeedbackModel;
use Hyperf\Di\Annotation\Inject;
use Qbhy\HyperfAuth\AuthManager;

class FeedbackService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var FeedbackModel
     */
    protected $model;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    public function myList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $filter['user_id'] = $this->auth->user()->getId();
        return parent::getList($filter, $op, $sort, $order, $limit);
    }

    public function doEdit(int $id, array $values)
    {
        $values['user_id'] = $this->auth->user()->getId();
        return parent::doEdit($id, $values);
    }
}