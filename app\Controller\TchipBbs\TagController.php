<?php
/**
 * @Copyright T-chip Team.
 * @Date 2023/3/28 上午10:30
 * <AUTHOR> X
 * @Description
 */

namespace App\Controller\TchipBbs;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\TchipBbs\TagService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;

/**
 * @ControllerNameAnnotation("文化标签管理")
 * @AutoController()
 * @Middleware(AuthMiddleware::class)
 */
class TagController extends \App\Controller\BaseController
{
    /**
     * @Inject()
     * @var TagService
     */
    protected $service;

    public function hot()
    {
        $result = $this->service->hot();
        return $this->response->success($result);
    }
}