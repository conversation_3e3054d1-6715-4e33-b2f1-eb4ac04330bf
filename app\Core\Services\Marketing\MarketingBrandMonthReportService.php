<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/31 下午4:59
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Marketing;

use App\Constants\MarketingCode;
use App\Constants\StatusCode;
use App\Core\Services\TchipSale\SaleService;
use App\Core\Utils\TimeUtils;
use App\Exception\AppException;
use App\Model\TchipBi\CategoryModel;
use App\Model\TchipBi\MarketingBrandMonthReportModel;
use App\Model\TchipBi\MarketingPlatformModel;
use App\Model\TchipBi\ProductModel;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

/**
 * 品牌运营-推广平台
 */
class MarketingBrandMonthReportService extends \App\Core\Services\BusinessService
{

    /**
     * @Inject()
     * @var MarketingBrandMonthReportModel
     */
    protected $model;

    /**
     * 新增/编辑
     * @param int $id
     * @param array $values
     * @return bool|Builder|Model|int
     */
    public function doEdit(int $id, array $values)
    {
        if ($id > 0) {
            $row = $this->model::query()->find($id);
            if (!$row) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            }
            $result = $row->update($values);
        } else {
            if ($this->model::query()->where('brand_code', $values['brand_code'])->where('date', $values['date'])->first()) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.Data_is_exist'));
            }
            $result = $this->model::query()->create($values);
        }

        return $result;
    }

    /**
     * 根据品牌code,date,保存报表数据(使用页面:品牌运营->数据汇总->市场数据->表头数据)
     * @param $brandCode
     * @param $date
     * @param $values
     * @return MarketingBrandMonthReportModel|Model
     */
    public function saveBrandReport($brandCode, $date, $values)
    {
        $condition = ['brand_code' => $brandCode, 'date' => $date];
        return $this->model::updateOrCreate($condition, $values);
    }

    /**
     * 获取品牌平台预算计划(使用页面:品牌运营->数据汇总->预算计划->表头数据)
     * @return array
     */
    public function getBrandPromtion($brandCode, $date)
    {
        $brand = $this->model::query()->where('brand_code', $brandCode)->where('date', $date)->first();
        if ($brand) {
            $brand = $brand->toArray();
        } else {
            $brand = $this->initBrandMonthReport($brandCode);
        }
        $brand['date']               = $date;
        $platform                    = MarketingPlatformModel::query()->where('status', 1)->orderByDesc('id')->get();
        if ($platform) {
            $platform         = $platform->toArray();
            $platformIds      = array_column($platform, 'id');
            $promotionService = make(MarketingPromotionReportService::class);
            $promotionRows    = $promotionService->getAllList(['platform_id' => implode(',', $platformIds), 'date' => $date, 'brand_code' => $brandCode], ['platform_id' => 'IN']);
            $promotionRows    = $promotionRows ? array_column($promotionRows, null, 'platform_id') : [];

            foreach ($platform as $key => $item) {
                if (!empty($promotionRows[$item['id']])) {
                    $brand['type'.$item['type']][$key] = $promotionRows[$item['id']];
                } else {
                    $brand['type'.$item['type']][$key] = [
                        'brand_name'  => $brand['brand_name'],
                        'brand_code'  => $brandCode,
                        'platform_id' => $item['id'],
                        'platform_name' => $item['name'],
                        'date'        => $date,
                    ];
                    $promotionService->initData($brand['type'.$item['type']][$key]);
                }
            }
            foreach ($platform as $item) {
                $brand['type'.$item['type']] = array_values($brand['type'.$item['type']]);
            }
        }
        return $brand;
    }

    /**
     * 保存品牌平台预算计划(使用页面:品牌运营->数据汇总->预算计划->表头数据)
     * @return bool
     */
    public function saveBrandPromtion($brandCode, $date, $values)
    {
        Db::beginTransaction();
        try {
            $platformsType    = [MarketingCode::PLATFORM_TYPE_SALE, MarketingCode::PLATFORM_TYPE_MARKET];
            $promotionService = make(MarketingPromotionReportService::class);
            foreach ($platformsType as $type) {
                if (!empty($values['type'.$type])) {
                    foreach ($values['type'.$type] as &$promotion) {
                        $promotionService->doEditFilter($brandCode, $promotion['platform_id'], $date, $promotion);
                    }
                    unset($values['type'.$type]);
                }
            }
            $condition = ['brand_code' => $brandCode, 'date' => $date];
            $this->model::updateOrCreate($condition, $values);
            Db::commit();
        } catch (\Throwable $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_EXCEPTION, $e->getMessage());
        }
        return true;
    }

    /**
     * 月度推广预算表头数据
     * @param $brandCode
     * @param $date
     * @return array
     */
    public function getMonthReport($brandCode, $date)
    {
        $filter  = [
            'date'       => $date,
            'brand_code' => $brandCode,
        ];
        list($query, $sort, $order, $limit) = $this->buildparams($filter, [], 'id', 'desc', 99999, $this->model);
        $row = $query->first();
        $row = $row ? $row->toArray() : [];
        if ($row) {
            // 总预算
            $row['expect_amount_count'] = 0;
            // 总实际
            $row['practical_amount_count'] = 0;
            // 销售使用预算
            $row['sale_expect_amount_count'] = 0;
            // 销售使用实际
            $row['sale_practical_amount_count'] = 0;
            // 推广使用预算
            $row['promotion_expect_amount_count'] = 0;
            // 推广使用实际
            $row['promotion_practical_amount_count'] = 0;
            $promotionService = make(MarketingPromotionReportService::class);
            $promotion = $promotionService->getAllList(['platform_type' => MarketingCode::PLATFORM_TYPE_MARKET, 'date' => $date]);
            $sale = $promotionService->getAllList(['platform_type' => MarketingCode::PLATFORM_TYPE_SALE, 'date' => $date]);
            foreach ($promotion as $item) {
                $row['expect_amount_count'] = round($row['expect_amount_count'] + $item['expect_amount'], 2);
                $row['practical_amount_count'] = round($row['practical_amount_count'] + $item['practical_amount'], 2);
                $row['promotion_expect_amount_count'] = round($row['promotion_expect_amount_count'] + $item['expect_amount'], 2);
                $row['promotion_practical_amount_count'] = round($row['promotion_practical_amount_count'] + $item['practical_amount'], 2);
            }
            foreach ($sale as $item) {
                $row['expect_amount_count'] = round($row['expect_amount_count'] + $item['expect_amount'], 2);
                $row['practical_amount_count'] = round($row['practical_amount_count'] + $item['practical_amount'], 2);
                $row['sale_expect_amount_count'] = round($row['sale_expect_amount_count'] + $item['expect_amount'], 2);
                $row['sale_practical_amount_count'] = round($row['sale_practical_amount_count'] + $item['practical_amount'], 2);
            }
        }
        return $row;
    }

    /**
     * 获取品牌数据汇总(根据时间)
     * @param string $brandCode
     * @param string $date
     * @return array
     */
    public function getSummary(string $brandCode, string $date)
    {
        $dateArr = explode(' - ', $date);
        if (count($dateArr) == 2) {
            $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
            $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
            $date       = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        } else {
            $month              = TimeUtils::getMonthFirstandLast($dateArr[0]);
            $dateArr[0]         = $month['starttime'];
            $dateArr[1]         = $month['endtime'];
            $date               = implode(' - ', $dateArr);
            $filter['saledate'] = $date;
            $op['saledate']     = 'DATE';
        }
        $filter  = [
            'date'       => $date,
            'brand_code' => $brandCode,
        ];
        $op['date'] = 'DATE';
        // 这一期的数据
        $info = $this->assignStatistics($brandCode, $dateArr[0], $dateArr[1]);
        $sales = make(MarketingProductMonthReportService::class)->statisticsProductSales($brandCode, $date);
        $marketing = make(MarketingPromotionReportService::class)->brandStatistics($brandCode, $date);
        $info = array_merge($info, $sales);
        $info['marketing'] = $marketing;

        // 上一期的数据
        $diffDate = TimeUtils::calculateMonthDifference($dateArr[0], $dateArr[1]) + 1;
        $oldstart = date('Y-m-d', strtotime("-{$diffDate} month", strtotime($dateArr[0])));
        $oldend = date('Y-m-d', strtotime("-{$diffDate} month", strtotime($dateArr[1])));
        $oldInfo = $this->assignStatistics($brandCode, $oldstart, $oldend);
        $oldSales = make(MarketingProductMonthReportService::class)->statisticsProductSales($brandCode, "{$oldstart} - {$oldend}");
        $oldMarketing = make(MarketingPromotionReportService::class)->brandStatistics($brandCode, "{$oldstart} - {$oldend}");
        $oldInfo = array_merge($oldInfo, $oldSales);
        $oldInfo['marketing'] = $oldMarketing;

        // 总销售
        $sale = make(MarketingProductMonthReportService::class)->statisticsProductSales($brandCode, $date);

        // 销额差
        $info['sale_amount_diff'] = !empty($oldInfo['amount']) ? $info['amount'] - $oldInfo['amount'] : '0.00';
        // 销量差
        $info['sale_volume_diff'] = !empty($oldInfo['volume']) ? $info['volume'] - $oldInfo['volume'] : 0;

        // 用户差
        $info['marketing']['users_count_diff'] = !empty($oldInfo['marketing']['users_count']) ? $info['marketing']['users_count'] - $oldInfo['marketing']['users_count'] : 0;
        // 互动差
        $info['marketing']['interact_count_diff'] = !empty($oldInfo['marketing']['interact_count']) ? $info['marketing']['interact_count'] - $oldInfo['marketing']['interact_count'] : 0;
        // 曝光度差
        $info['marketing']['exposure_count_diff'] = !empty($oldInfo['marketing']['exposure_count']) ? $info['marketing']['exposure_count'] - $oldInfo['marketing']['exposure_count'] : 0;
        // 投流总额差
        $info['marketing']['practical_amount_diff'] = !empty($oldInfo['marketing']['practical_amount']) ? $info['marketing']['practical_amount'] - $oldInfo['marketing']['practical_amount'] : 0;
        // 点击率差
        $info['marketing']['click_rate_avg_diff'] = !empty($oldInfo['marketing']['click_rate_avg']) ? $info['marketing']['click_rate_avg'] - $oldInfo['marketing']['click_rate_avg'] : 0;
        //点击转化率差
        $info['marketing']['click_conversion_rate_diff'] = !empty($oldInfo['marketing']['click_conversion_rate']) ? $info['marketing']['click_conversion_rate'] - $oldInfo['marketing']['click_conversion_rate'] : 0;
        // 投资回报率差
        $info['marketing']['invest_return_rate_avg_diff'] = !empty($oldInfo['marketing']['invest_return_rate_avg']) ? $info['marketing']['invest_return_rate_avg'] - $oldInfo['marketing']['invest_return_rate_avg'] : 0;
        return $info;
    }

    /**
     * 获取品牌数据汇总(所有)
     * @param string $brandCode
     * @param string $date
     * @return array
     */
    public function getSummaryTotal(string $brandCode, string $date): array
    {
        $dateArr = explode(' - ', $date);
        $dateArr[0] = date('Y-m-01', strtotime($dateArr[0]));
        $dateArr[1] = TimeUtils::getMonthLastday($dateArr[1]);
        $date       = implode(' - ', $dateArr);

        $info = $this->assignStatistics($brandCode, $dateArr[0], $dateArr[1]);
        $sales = make(MarketingProductMonthReportService::class)->statisticsProductSales($brandCode, $date, true);
        $marketing = make(MarketingPromotionReportService::class)->brandStatistics($brandCode, $date);
        $info = array_merge($info, $sales);
        $info['marketing'] = $marketing;

        return $info;
    }


    /**
     * 获取品牌在指定日期范围内的销售统计数据
     *
     * @param string $brandCode 品牌代码
     * @param string $start 开始日期，格式为 'YYYY-MM-DD'
     * @param string $end 结束日期，格式为 'YYYY-MM-DD'
     * @return array 返回包含品牌销售统计的数组，包括品牌代码、销售额、销售量等信息
     *
     * 该方法基于提供的品牌代码和日期范围，生成过滤条件并执行查询。
     * 查询结果包括品牌名称、品牌代码、销售额配额 (sale_amount_quota)、销售量配额 (sale_volume_quota) 等。
     * 如果查询结果为空，方法会返回一个默认值数组，设置销售额为 '0.00' 和销售量为 0。
     */
    public function assignStatistics(string $brandCode, string $start, string $end): array
    {
        $filter  = [
            'date'       => "{$start} - {$end}",
            'brand_code' => $brandCode,
        ];
        $op['date'] = 'DATE';
        list($query, $sort, $order, $limit) = $this->buildparams($filter, $op, 'id', 'desc', 99999, $this->model);
        $info = $query->selectRaw('brand_name, brand_code, IFNULL(SUM(sale_amount_quota), 0.00) as sale_amount_quota, IFNULL(SUM(sale_volume_quota), 0) as sale_volume_quota')->first();
        if (!$info) {
            $info = ['brand_code' => $brandCode, 'date' => $start, 'sale_amount_quota' => '0.00', 'sale_volume_quota' => 0];
        } else {
            $info = $info->toArray();
            $info['sale_amount_quota'] = str_replace(',', '', number_format($info['sale_amount_quota'], 2));
        }
        return $info;
    }

    /**
     * @param string $brandCode 公司识别码stationpc
     * @param string $date 月份格式'2024-06-01'
     * @return array|false
     */
    public function monthStatistics(string $brandCode, string $date)
    {
        $brandProductNames = ProductModel::query()->selectRaw('IFNULL(product_sale_name, product_name) as product_name')->where('brand', $brandCode)
            ->where('status', 1)
            ->where('is_marketing', 1)
            ->get();
        $brandProductNames = $brandProductNames ? array_column($brandProductNames->toArray(), 'product_name') : [];
        if (!$brandProductNames) return false;
        $saleProductsIds = make(LinkageService::class)->getProductsIdsByNames($brandProductNames);
        if (!$saleProductsIds) return false;

        $nowDate = TimeUtils::getMonthFirstandLast($date);
        $amount = make(SaleService::class)->statisticsMoneyByDate($nowDate['starttime'], $nowDate['endtime'], null, ['prod_id' => implode(',', $saleProductsIds)], ['prod_id' => 'IN']);
        $volume = make(SaleService::class)->statisticsVolumeByDate($nowDate['starttime'], $nowDate['endtime'], null, ['prod_id' => implode(',', $saleProductsIds)], ['prod_id' => 'IN']);


        $filter = [
            'brand_code' => $brandCode,
        ];
        $this->model::updateOrCreate();
    }

    /**
     * 初始化品牌计划数据
     * @param $brandCode
     * @return array
     */
    public function initBrandMonthReport($brandCode)
    {
        return [
            'brand_name' => CategoryModel::query()->where('keywords', $brandCode)->value('name'),
            'brand_code' => $brandCode,
            'expect_description' => null,
            'practical_description' => null,
            // 'sale_platform' => [],
            // 'promotion_platform',
        ];
    }
}